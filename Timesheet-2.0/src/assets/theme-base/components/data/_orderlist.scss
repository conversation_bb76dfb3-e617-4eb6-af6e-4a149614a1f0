.p-orderlist {
  .p-orderlist-controls {
    padding: $panelContentPadding;

    .p-button {
      margin-bottom: $inlineSpacing;
    }
  }

  .p-orderlist-list-container {
    background: $panelContentBg;
    border: $panelContentBorder;
    border-radius: $borderRadius;
    transition: $formElementTransition;
    outline-color: transparent;

    &.p-focus {
      @include focused-input();
    }
  }

  .p-orderlist-header {
    color: $panelHeaderTextColor;
    padding: $panelHeaderPadding;
    font-weight: $panelHeaderFontWeight;
    .p-orderlist-title {
      font-weight: $panelHeaderFontWeight;
    }
  }

  .p-orderlist-filter-container {
    padding: $panelHeaderPadding;
    background: $panelContentBg;
    border: $panelHeaderBorder;
    border-bottom: 0 none;

    .p-orderlist-filter-input {
      padding-right: nth($inputPadding, 2) + $primeIconFontSize;
    }

    .p-orderlist-filter-icon {
      right: nth($inputPadding, 2);
      color: $inputIconColor;
    }
  }

  .p-orderlist-list {
    color: $panelContentTextColor;
    padding: $inputListPadding;
    outline: 0 none;

    &:not(:first-child) {
      border-top: $panelContentBorder;
    }

    .p-orderlist-item {
      padding: $inputListItemPadding;
      margin: $inputListItemMargin;
      border: $inputListItemBorder;
      color: $inputListItemTextColor;
      background: $inputListItemBg;
      transition: $listItemTransition;

      &:first-child {
        margin-top: 0;
      }

      &:not(.p-highlight):hover {
        background: $inputListItemHoverBg;
        color: $inputListItemTextHoverColor;
      }

      &.p-focus {
        color: $inputListItemTextFocusColor;
        background: $inputListItemFocusBg;
      }

      &.p-highlight {
        color: $highlightTextColor;
        background: $highlightBg;

        &.p-focus {
          background: $highlightFocusBg;
        }
      }
    }

    .p-orderlist-empty-message {
      padding: $inputListItemPadding;
      color: $inputListItemTextColor;
    }

    &:not(.cdk-drop-list-dragging) {
      .p-orderlist-item {
        &:not(.p-highlight):hover {
          background: $inputListItemHoverBg;
          color: $inputListItemTextHoverColor;
        }
      }
    }
  }

  &.p-orderlist-striped {
    .p-orderlist-list {
      .p-orderlist-item:nth-child(even) {
        background: $panelContentEvenRowBg;

        &:hover {
          background: $inputListItemHoverBg;
        }
      }
    }
  }
}

.p-orderlist-item {
  &.cdk-drag-preview {
    padding: $inputListItemPadding;
    box-shadow: $inputOverlayShadow;
    border: $inputListItemBorder;
    color: $inputListItemTextColor;
    background: $panelContentBg;
    margin: 0;
  }
}
