.p-menu {
  padding: $verticalMenuPadding;
  background: $menuBg;
  color: $menuTextColor;
  border: $menuBorder;
  border-radius: $borderRadius;
  width: $menuWidth;

  .p-menuitem {
    @include menuitem();
  }

  &.p-menu-overlay {
    background: $overlayMenuBg;
    border: $overlayMenuBorder;
    box-shadow: $overlayMenuShadow;
  }

  .p-submenu-header {
    margin: $submenuHeaderMargin;
    padding: $submenuHeaderPadding;
    color: $submenuHeaderTextColor;
    background: $submenuHeaderBg;
    font-weight: $submenuHeaderFontWeight;
    border-top-right-radius: $submenuHeaderBorderRadius;
    border-top-left-radius: $submenuHeaderBorderRadius;
  }

  .p-menuitem-separator {
    border-top: $divider;
    margin: $menuSeparatorMargin;
  }

  .p-menuitem-badge {
    @include menuitem-badge();
  }
}
