$dialogBorderRadius: 0.5rem;

.p-dialog {
  border-radius: $dialogBorderRadius;
  box-shadow: $overlayContainerShadow;
  border: $overlayContentBorder;

  .p-dialog-header {
    border-bottom: $dialogHeaderBorder;
    background: $dialogHeaderBg;
    color: $dialogHeaderTextColor;
    padding: $dialogHeaderPadding;
    border-top-right-radius: $dialogBorderRadius;
    border-top-left-radius: $dialogBorderRadius;

    .p-dialog-title {
      font-weight: $dialogHeaderFontWeight;
      font-size: $dialogHeaderFontSize;
    }

    .p-dialog-header-icon {
      @include action-icon();
      margin-right: $inlineSpacing;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .p-dialog-content {
    background: $overlayContentBg;
    color: $panelContentTextColor;
    padding: $dialogContentPadding;

    &:last-of-type {
      border-bottom-right-radius: $dialogBorderRadius;
      border-bottom-left-radius: $dialogBorderRadius;
    }
  }

  .p-dialog-footer {
    border-top: $dialogFooterBorder;
    background: $overlayContentBg;
    color: $panelFooterTextColor;
    padding: $dialogFooterPadding;
    text-align: right;
    border-bottom-right-radius: $dialogBorderRadius;
    border-bottom-left-radius: $dialogBorderRadius;

    button {
      margin: 0 $inlineSpacing 0 0;
      width: auto;
    }
  }

  &.p-confirm-dialog {
    .p-confirm-dialog-icon {
      font-size: $primeIconFontSize * 2;

      &.p-icon {
        width: $primeIconFontSize * 2;
        height: $primeIconFontSize * 2;
      }
    }

    .p-confirm-dialog-message {
      margin-left: $inlineSpacing * 2;
    }
  }
}
