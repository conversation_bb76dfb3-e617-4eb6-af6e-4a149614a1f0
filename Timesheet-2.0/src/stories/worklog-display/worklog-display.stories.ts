import { Meta, StoryFn } from '@storybook/angular';
import { WorklogDisplayComponent } from '../../app/components/worklog-display/worklog-display.component';
import { Worklog } from '../../app/services/worklog/worklog.model';

export default {
  title: 'Components/WorklogDisplay',
  component: WorklogDisplayComponent,
} as Meta;

const worklogs: Worklog[] = [
  {
    id: '1',
    workDate: new Date('2024-12-10T08:00:00Z'),
    startDate: new Date('2024-12-10T09:00:00Z'),
    endDate: new Date('2024-12-10T17:00:00Z'),
    clientId: '123',
    projectId: '456',
    employeeId: '789',
    managerId: '987',
    taskId: '101112',
    description: 'Completed module implementation and bug fixes.',
    minutes: 480,
    createdAt: new Date('2024-12-10T07:00:00Z'),
    updatedAt: new Date('2024-12-10T18:00:00Z'),
    deleted: false,
    isOnLeave: false,
    isOnFirstHalfLeave: false,
    isOnSecondHalfLeave: false,
    isWeekOff: false,
    isCompanyOff: false,
    employeeResource: {
      name: 'John Doe',
    },
    project: {
      projectName: 'Mobile App Development',
      billable: true,
      projectStatus: 'active',
    },
    client: {
      name: 'Acme Corp',
      status: true,
    },
    workLogStatus: {
      status: 'approved',
      remarks: 'Good progress made on the project.',
    },
    task: {
      taskName: 'UI Design',
      contract: {
        customContractId: 'cont-001',
      },
    },
  },
  {
    id: '2',
    workDate: new Date('2024-12-11T08:00:00Z'),
    startDate: new Date('2024-12-11T09:00:00Z'),
    endDate: new Date('2024-12-11T16:00:00Z'),
    clientId: '124',
    projectId: '457',
    employeeId: '790',
    managerId: '988',
    taskId: '101113',
    description: 'Refactored API endpoints to improve performance.',
    minutes: 420,
    createdAt: new Date('2024-12-11T07:00:00Z'),
    updatedAt: new Date('2024-12-11T16:30:00Z'),
    deleted: false,
    isOnLeave: false,
    isOnFirstHalfLeave: false,
    isOnSecondHalfLeave: false,
    isWeekOff: false,
    isCompanyOff: false,
    employeeResource: {
      name: 'Jane Smith',
    },
    project: {
      projectName: 'API Integration',
      billable: false,
      projectStatus: 'completed',
    },
    client: {
      name: 'Tech Solutions',
      status: false,
    },
    workLogStatus: {
      status: 'submitted',
      remarks: 'Awaiting manager review.',
    },
    task: {
      taskName: 'API Optimization',
      contract: {
        customContractId: 'cont-002',
      },
    },
  },
  {
    id: '3',
    workDate: new Date('2024-12-12T08:00:00Z'),
    startDate: new Date('2024-12-12T10:00:00Z'),
    endDate: new Date('2024-12-12T15:00:00Z'),
    clientId: '125',
    projectId: '458',
    employeeId: '791',
    managerId: null,
    taskId: '101114',
    description: 'Prepared project documentation and user manuals.',
    minutes: 300,
    createdAt: new Date('2024-12-12T09:00:00Z'),
    updatedAt: new Date('2024-12-12T15:30:00Z'),
    deleted: false,
    isOnLeave: false,
    isOnFirstHalfLeave: false,
    isOnSecondHalfLeave: false,
    isWeekOff: false,
    isCompanyOff: false,
    employeeResource: {
      name: 'Alice Johnson',
    },
    project: {
      projectName: 'User Research',
      billable: true,
      projectStatus: 'in-progress',
    },
    client: {
      name: 'Innovate Inc.',
      status: true,
    },
    workLogStatus: {
      status: 'rejected',
      remarks: 'Documentation ready for client review.',
    },
    task: {
      taskName: 'Documentation',
      contract: {
        customContractId: 'cont-003',
      },
    },
  },
  {
    id: '4',
    workDate: new Date('2024-12-10T08:00:00Z'),
    startDate: new Date('2024-12-10T09:00:00Z'),
    endDate: new Date('2024-12-10T17:00:00Z'),
    clientId: '123',
    projectId: '456',
    employeeId: '789',
    managerId: '987',
    taskId: '101112',
    description: 'Completed module implementation and bug fixes.',
    minutes: 480,
    createdAt: new Date('2024-12-10T07:00:00Z'),
    updatedAt: new Date('2024-12-10T18:00:00Z'),
    deleted: false,
    isOnLeave: false,
    isOnFirstHalfLeave: false,
    isOnSecondHalfLeave: false,
    isWeekOff: false,
    isCompanyOff: false,
    employeeResource: {
      name: 'John Doe',
    },
    project: {
      projectName: 'Chatbot Development',
      billable: true,
      projectStatus: 'active',
    },
    client: {
      name: 'Acme Corp',
      status: true,
    },
    workLogStatus: {
      status: 'revised',
      remarks: 'Good progress made on the project.',
    },
    task: {
      taskName: 'UI Design',
      contract: {
        customContractId: 'cont-001',
      },
    },
  },
];

const Template: StoryFn<WorklogDisplayComponent> = (args: any) => ({
  props: args,
});

export const Default = Template.bind({});
Default.args = {
  worklogs,
};
