import { CommonModule, DatePipe } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { CalendarComponent } from '../../app/components/calendar/calendar.component';
import { Meta, StoryFn } from '@storybook/angular';

// Storybook metadata definition
export default {
  title: 'Components/calendar', // Storybook's title path for this component's stories
  component: CalendarComponent, // The component being showcased
  imports: [
    FormsModule,
    CommonModule, // Import Angular's CommonModule
  ],
} as Meta;

// Base template for all stories to reuse the configuration and props
const Template: StoryFn<CalendarComponent> = (args: any) => ({
  props: args,
});

export const Default = Template.bind({});
Default.args = {};
