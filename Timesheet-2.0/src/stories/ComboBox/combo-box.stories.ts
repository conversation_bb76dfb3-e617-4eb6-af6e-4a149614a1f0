import { CommonModule } from '@angular/common';
import {
  Form<PERSON>uilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { Meta, moduleMetadata, StoryFn } from '@storybook/angular';
import { MultiSelectModule } from 'primeng/multiselect';
import { ComboBoxComponent } from '../../app/components/combo-box/combo-box.component';
import { ButtonModule } from 'primeng/button';
import { signal } from '@angular/core';

interface City {
  name: string;
  code: string;
  disabled?: boolean;
}

const options: City[] = [
  { name: 'New York', code: 'NY' },
  { name: 'Rome', code: 'RM', disabled: false },
  { name: 'London', code: 'LDN' },
  { name: 'Istanbul', code: 'IST' },
  { name: 'Paris', code: 'PRS' },
];

/**
 * Storybook configuration for ComboBoxComponent.
 *
 * This file contains stories demonstrating the various states and configurations
 * of the ComboBoxComponent, which uses PrimeNG's MultiSelect for selecting options.
 * Stories cover different scenarios such as default selection, search enabled,
 * disabled state, select-all functionality, and disabled options.
 */
export default {
  title: 'Components/combo-box', // Storybook's title path for this component's stories
  component: ComboBoxComponent, // The component being showcased
  imports: [
    FormsModule,
    ButtonModule,
    ReactiveFormsModule, // Import FormsModule for ngModel binding
    MultiSelectModule, // Import PrimeNG's MultiSelect module
    CommonModule, // Import Angular's CommonModule
    BrowserAnimationsModule, // Import for enabling animations in Angular
  ],
  decorators: [
    moduleMetadata({
      imports: [
        BrowserAnimationsModule,
        ButtonModule,
        FormsModule,
        ReactiveFormsModule,
      ], // Add this to ensure animations are available globally
    }),
  ],
} as Meta;

// Base template for all stories to reuse the configuration and props
const Template: StoryFn<ComboBoxComponent> = (args: any) => ({
  props: args,
});

// Story 1: Default State - Demonstrates ComboBox with default options and no additional configurations
export const Default = Template.bind({});
Default.args = {
  options: options,
  selectedOptions: [{ name: 'New York', code: 'NY' }],
  disabled: false,
  isSearchEnabled: true,
};

// Story 2: With Search Enabled - Shows ComboBox with search functionality enabled
export const WithSearch = Template.bind({});
WithSearch.args = {
  options: options,
  selectedOptions: [{ name: 'New York', code: 'NY' }],
  disabled: false,
  isSearchEnabled: true,
};

// Story 3: Disabled State - Displays the ComboBox component with disabled state
export const Disabled = Template.bind({});
Disabled.args = {
  options: options,
  selectedOptions: [{ name: 'New York', code: 'NY' }],
  disabled: true,
  isSearchEnabled: true,
};

// Story 4: Default Selection - Default selection of options when provided as input
export const DefaultSelection = Template.bind({});
DefaultSelection.args = {
  selectedOptions: [{ name: 'New York', code: 'NY' }],
  options: [
    { name: 'New York', code: 'NY' },
    { name: 'Rome', code: 'RM' },
    { name: 'London', code: 'LDN' },
    { name: 'Istanbul', code: 'IST' },
    { name: 'Paris', code: 'PRS' },
  ],
};

// Story 5: Empty State - Show the ComboBox with no options available
export const EmptyState = Template.bind({});
EmptyState.args = {
  options: [],
  isSearchEnabled: false,
};

// Story 6: Disabled Options - Show the ComboBox with certain options disabled
export const DisabledOptions = Template.bind({});
DisabledOptions.args = {
  options: [
    { name: 'New York', code: 'NY' },
    { name: 'Rome', code: 'RM', disabled: true },
    { name: 'London', code: 'LDN' },
    { name: 'Istanbul', code: 'IST' },
    { name: 'Paris', code: 'PRS' },
  ],
};

// Story 7: ComboBox in Form - Demonstrates the use of ComboBoxComponent within a reactive form
// Template for the ComboBox in a reactive form
export const ComboBoxInForm: StoryFn = () => {
  // Story setup: create a form using FormBuilder
  const formBuilder = new FormBuilder();
  const form: FormGroup = formBuilder.group({
    selectedCities: [[], Validators.required], // Pre-select an empty array and add a required validator
  });

  const cities: City[] = options; // Options for the ComboBox

  let submittedCities: City[] = []; // Array to store submitted cities

  const onSubmit = () => {
    if (form.valid) {
      submittedCities = form.get('selectedCities')?.value;
      console.log('Submitted cities:', submittedCities);
    } else {
      form.markAllAsTouched();
    }
  };

  return {
    props: {
      form,
      cities,
      submittedCities,
      onSubmit,
    },
    template: `
    <div class="container mx-auto p-4">
      <h1 class="text-2xl font-bold mb-4">City Selection Form</h1>
      <form [formGroup]="form" (ngSubmit)="onSubmit()" class="space-y-4">
        <div>
          <label
            for="selectedCities"
            class="block text-sm font-medium text-gray-700"
            >Select Cities</label
          >
          <tms-combo-box
            id="selectedCities"
            [options]="cities"
            formControlName="selectedCities"
            [isSearchEnabled]="true"
          ></tms-combo-box>
          @if (form.get('selectedCities')?.invalid &&
          (form.get('selectedCities')?.dirty ||
          form.get('selectedCities')?.touched)) {
          <p class="mt-2 text-sm text-red-600">
            Please select at least one city.
          </p>
          }
        </div>
        <button
          pButton
          type="submit"
          label="Submit"
          class="p-button-primary"
        ></button>
      </form>
      <h1>{{submittedCities.length}}</h1>
      @if (submittedCities.length > 0) {
      <div class="mt-4">
        <h2 class="text-xl font-semibold">Submitted Cities:</h2>
        <ul class="list-disc list-inside">
          @for (city of submittedCities; track city.code) {
          <li>{{ city.name }}HI</li>
          }
        </ul>
      </div>
      }
    </div>
  `,
  };
};
