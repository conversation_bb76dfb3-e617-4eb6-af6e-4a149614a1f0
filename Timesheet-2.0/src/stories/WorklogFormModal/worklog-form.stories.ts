import { Meta, moduleMetadata, StoryFn } from '@storybook/angular';
import { Component } from '@angular/core';
import {
  DynamicDialogModule,
  DynamicDialogRef,
  DynamicDialogConfig,
  DialogService,
} from 'primeng/dynamicdialog';
import { ReactiveFormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { InputNumberModule } from 'primeng/inputnumber';
import { WorklogFormComponent } from '../../app/components/worklog-form/worklog-form.component';
import { MessageService } from 'primeng/api';
import { ToastModule } from 'primeng/toast';

// Mock Component for Storybook
@Component({
  selector: 'app-mock-worklog',
  standalone: true,
  imports: [ButtonModule],
  template: `
    <div class="w-full h-full flex gap-2 items-center justify-center">
      <p-toast></p-toast>
      <p-button
        label="Add Worklog"
        icon="pi pi-plus"
        (click)="openAddWorklogDialog()"
      ></p-button>
      <p-button
        label="Edit Worklog"
        icon="pi pi-pencil"
        (click)="openEditWorklogDialog()"
      ></p-button>
    </div>
  `,
})
class MockWorklogComponent {
  constructor(private dialogService: DialogService) {}

  openAddWorklogDialog() {
    this.dialogService.open(WorklogFormComponent, {
      data: { date: new Date() },
      header: 'Add Worklog',
      dismissableMask: true,
    });
  }

  openEditWorklogDialog() {
    const mockWorklog = {
      projectId: '1',
      taskId: '1',
      description: 'Existing work log description',
      loggedHours: new Date(2024, 0, 1, 2, 30),
    };

    this.dialogService.open(WorklogFormComponent, {
      data: { date: new Date(), worklog: mockWorklog },
      header: 'Edit Worklog',
      dismissableMask: true,
    });
  }
}

export default {
  title: 'Components/WorklogForm',
  component: MockWorklogComponent,
  decorators: [
    moduleMetadata({
      imports: [
        ReactiveFormsModule,
        DynamicDialogModule,
        ButtonModule,
        CalendarModule,
        DropdownModule,
        InputTextareaModule,
        InputNumberModule,
        ToastModule,
        MockWorklogComponent,
        WorklogFormComponent,
      ],
      providers: [
        MessageService,
        DialogService,
        DynamicDialogRef,
        DynamicDialogConfig,
      ],
    }),
  ],
} as Meta;

const Template: StoryFn<MockWorklogComponent> = (args: any) => ({
  props: args,
});

export const Default = Template.bind({});
Default.args = {};
