import { Meta, StoryFn } from '@storybook/angular';
import { ReactiveFormsModule } from '@angular/forms';
import { TextareaComponent } from '../../app/components/textarea/textarea.component';

export default {
  title: 'Components/Textarea',
  component: TextareaComponent,
  imports: [ReactiveFormsModule],
} as Meta;

const Template: StoryFn<TextareaComponent> = (args: any) => ({
  props: args,
});

export const Default = Template.bind({});
Default.args = {
  placeholder: 'Enter your message...',
  rows: 5,
  disabled: false,
  invalid: false,
  maxLength: 100,
  required: false,
};

export const WithMaxLength = Template.bind({});
WithMaxLength.args = {
  ...Default.args,
  maxLength: 10,
};

export const RequiredField = Template.bind({});
RequiredField.args = {
  ...Default.args,
  required: true,
};

export const DisabledField = Template.bind({});
DisabledField.args = {
  ...Default.args,
  disabled: true,
};

export const InvalidField = Template.bind({});
InvalidField.args = {
  ...Default.args,
  invalid: true,
};
