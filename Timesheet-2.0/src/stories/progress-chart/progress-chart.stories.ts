import { CommonModule } from '@angular/common';
import { Meta, StoryFn } from '@storybook/angular';
import { ProgressChartComponent } from '../../app/components/progress-chart/progress-chart.component';

export default {
  title: 'Components/ProgressChart',
  component: ProgressChartComponent,
  imports: [CommonModule, ProgressChartComponent],
} as Meta;

// Base template for all stories
const Template: StoryFn<ProgressChartComponent> = (args: any) => ({
  props: args,
});

export const DefaultView = Template.bind({});
DefaultView.args = {
  actualValue: 0,
  baseValue: 20,
  suffix: 'd',
};

export const HalfProgress = Template.bind({});
HalfProgress.args = {
  actualValue: 10,
  baseValue: 20,
  suffix: 'd',
};

export const CompletedProgress = Template.bind({});
CompletedProgress.args = {
  actualValue: 20,
  baseValue: 20,
  suffix: 'd',
};
