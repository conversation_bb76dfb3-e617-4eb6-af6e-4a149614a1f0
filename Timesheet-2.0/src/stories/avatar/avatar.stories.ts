import { CommonModule } from '@angular/common';
import { Meta, StoryFn } from '@storybook/angular';
import { AvatarComponent } from '../../app/components/avatar/avatar.component';

export default {
  title: 'Components/Avatar',
  component: AvatarComponent,
  imports: [CommonModule, AvatarComponent],
} as Meta;

// Base template for all stories
const Template: StoryFn<AvatarComponent> = (args: any) => ({
  props: args,
});

export const DefaultView = Template.bind({});
DefaultView.args = {
  label: 'Akshay',
  value: 1,
};

export const AvatarWithIcon = Template.bind({});
AvatarWithIcon.args = {
  icon: 'pi pi-user',
  value: 2,
};

export const SquareAvatarWithIcon = Template.bind({});
SquareAvatarWithIcon.args = {
  icon: 'pi pi-user',
  value: 2,
  shape: 'square',
};

export const CircleAvatarWithIcon = Template.bind({});
CircleAvatarWithIcon.args = {
  icon: 'pi pi-user',
  value: 2,
  shape: 'circle',
};

const imagePath =
  'https://cdn3.iconfinder.com/data/icons/business-avatar-1/512/11_avatar-512.png';
export const AvatarWithImage = Template.bind({});
AvatarWithImage.args = {
  image: imagePath,
  value: 3,
};

export const LargeAvatar = Template.bind({});
LargeAvatar.args = {
  icon: 'pi pi-user',
  value: 2,
  size: 'large',
};

export const XlargeAvatar = Template.bind({});
XlargeAvatar.args = {
  icon: ' pi pi-user',
  value: 2,
  size: 'xlarge',
};
