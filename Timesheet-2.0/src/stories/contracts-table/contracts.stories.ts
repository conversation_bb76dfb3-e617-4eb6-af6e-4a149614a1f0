import { moduleMetadata, StoryFn, Meta } from '@storybook/angular';
import { TableModule } from 'primeng/table';
import { ButtonModule } from 'primeng/button';
import { TooltipModule } from 'primeng/tooltip';
import { MenuModule } from 'primeng/menu';
import { SkeletonModule } from 'primeng/skeleton';
import {
  QueryClient,
  provideQueryClient,
} from '@tanstack/angular-query-experimental';
import { ContractsTableComponent } from '../../app/components/contracts-table/contracts-table.component';
import { signal } from '@angular/core';
import { ContractService } from '../../app/services/contract/contract.service';

export default {
  title: 'Components/ContractTable',
  component: ContractsTableComponent,
  decorators: [
    moduleMetadata({
      imports: [
        TableModule,
        ButtonModule,
        TooltipModule,
        MenuModule,
        SkeletonModule,
      ],
      providers: [
        provideQueryClient(new QueryClient()),
        {
          provide: ContractService,
          useValue: {
            fetchContractsByProjectId: () => Promise.resolve(mockContracts),
          },
        },
      ],
    }),
  ],
} as Meta;

const mockContracts = [
  {
    id: '25f141a3-1d98-4df0-a9bb-7df99275a4c4',
    startDate: '2024-10-14T18:30:00.000Z',
    endDate: '2024-12-30T18:30:00.000Z',
    contractId: 'Test contract',
    contractStatus: 'active',
    renewedFrom: null,
    contractBudgetDetail: [
      {
        id: 'a52bce4c-18a0-4196-8fea-d7d6186e5a4e',
        isBillable: false,
        contractId: '25f141a3-1d98-4df0-a9bb-7df99275a4c4',
      },
    ],
    project: {
      id: '2534bf68-4a86-42e8-8662-6279c05336f7',
      projectName: 'Application',
      contactName: 'Test2',
      contactEmail: '<EMAIL>',
    },
    contractManagers: [
      {
        id: 'cd6cfe7f-9aac-47c8-9918-a3963a189503',
        name: 'Vinayak Bhaskar Kalmane',
        email: '<EMAIL>',
        kekaId: '1052',
      },
      {
        id: '398ff605-7c8d-4280-b008-aa4708a451b8',
        name: 'Nagarjuna GS',
        email: '<EMAIL>',
        kekaId: '1044',
      },
    ],
    contractResources: 2,
    total: 0,
    totalLoggedMinutes: 0,
  },
  {
    id: 'efe15336-7bf2-48b5-bc03-2bded9f37541',
    startDate: '2024-10-14T18:30:00.000Z',
    endDate: '2024-12-30T18:30:00.000Z',
    contractId: 'Testing 2',
    contractStatus: 'active',
    renewedFrom: null,
    contractBudgetDetail: [],
    project: {
      id: '2534bf68-4a86-42e8-8662-6279c05336f7',
      projectName: 'Application',
      contactName: 'Test2',
      contactEmail: '<EMAIL>',
    },
    contractManagers: [
      {
        id: '8f677870-1f8d-43fb-9493-2d71967d3ecc',
        name: ' Shravan R Hegde',
        email: '<EMAIL>',
        kekaId: 'CC541',
      },
    ],
    contractResources: 0,
    total: 0,
    totalLoggedMinutes: 0,
  },
];

const Template: StoryFn<ContractsTableComponent> = (
  args: Partial<ContractsTableComponent>
) => ({
  props: {
    ...args,
    contracts: () => mockContracts,
  },
});

export const Default = Template.bind({});
Default.args = {};
