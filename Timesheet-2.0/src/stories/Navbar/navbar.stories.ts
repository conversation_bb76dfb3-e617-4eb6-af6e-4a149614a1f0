import { Meta, moduleMetadata } from '@storybook/angular';
import { StoryFn } from '@storybook/angular';
import { NavBarComponent } from '../../app/components/nav-bar/nav-bar.component';
import { ButtonModule } from 'primeng/button';
import { MenuModule } from 'primeng/menu';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { action } from '@storybook/addon-actions';
import { MenuItem } from 'primeng/api';
import { ChangeDetectorRef } from '@angular/core';

/**
 * Storybook configuration for the `NavBarComponent`.
 * This file defines how the `NavBarComponent` is displayed in Storybook, allowing different states and actions to be showcased interactively.
 *
 * **Usage:** This Storybook entry demonstrates the `NavBarComponent` in multiple states such as the default state, with badge notifications,
 * and tracking interaction with the profile and logout actions.
 */
export default {
  title: 'Components/Navbar',
  component: NavBarComponent,
  imports: [ButtonModule, MenuModule],
  decorators: [
    moduleMetadata({
      imports: [ButtonModule, BrowserAnimationsModule, MenuModule], // Add this to ensure animations are available globally
    }),
  ],
} as Meta;

// Base template for all stories
const Template: StoryFn<NavBarComponent> = (args: any) => ({
  props: args,
});

/**
 * Default Story: Shows the default state of the `NavBarComponent` without any badge.
 */
export const Default = Template.bind({});
Default.args = {
  badge: '',
};

/**
 * WithBadge Story: Displays the `NavBarComponent` with a badge of "3" to simulate notifications.
 */
export const WithBadge = Template.bind({});
WithBadge.args = {
  badge: '3',
};

/**
 * ProfileClicked Story: A story that simulates a user clicking the profile button.
 * It tracks the click event using `action()` from Storybook, which allows you to visualize the action in the Storybook UI.
 */
export const ProfileClicked: StoryFn<NavBarComponent> = (args: any) => {
  return {
    props: {
      ...args,
      navigateToProfile: action('Profile clicked'),
      logout: action('Logout clicked'),
    },
    template: `
          <tms-nav-bar [badge]="badge" [menuItems]="menuItems"></tms-nav-bar>
      `,
  };
};
