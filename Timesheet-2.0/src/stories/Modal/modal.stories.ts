import { Meta, moduleMetadata, StoryFn } from '@storybook/angular';
import { DialogService, DynamicDialogModule } from 'primeng/dynamicdialog';
import { Component } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { TextareaComponent } from '../../app/components/textarea/textarea.component';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ModalService } from '../../app/services/modal/modal-service.service';

// Form component to render in modal
@Component({
  selector: 'story-content',
  standalone: true,
  template: `
    <div class="modal min-w-80">
      <p class="m-0 mb-2">Message:</p>
      <app-textarea
        placeholder="Enter your message..."
        [maxLength]="10"
        [disabled]="false"
        [rows]="5"
        [required]="true"
        formControlName="message"
      ></app-textarea>
    </div>
    <p-button type="submit" severity="success" class="mt-4">Submit</p-button>
  `,
  imports: [ButtonModule, TextareaComponent, ReactiveFormsModule, FormsModule], // Import FormsModule for form handling
})
class DynamicFormComponent {
  onSubmit() {
    console.log('Form Submitted');
  }
}

// Text component to render in modal
@Component({
  selector: 'story-message',
  standalone: true,
  imports: [],
  template: `<p class="w-full max-w-52 m-0 mb-4">
    Lorem ipsum dolor sit amet consectetur adipisicing elit. Modi deserunt
    recusandae facere vitae nemo fuga quisquam voluptatibus! Id, obcaecati
    doloribus.
  </p>`,
})
export class DynamicTextComponent {}

@Component({
  selector: 'story-footer',
  standalone: true,
  imports: [ButtonModule],
  template: `<p-button label="Save"></p-button>`,
})
export class FooterComponent {}

// Wrapper component for modal demo in story
@Component({
  selector: 'modal-demo',
  standalone: true,
  template: `
    <div class="flex gap-4">
      <p-button label="Open Form Modal" (click)="openFormModal()"></p-button>
      <p-button label="Open Text Modal" (click)="openTextModal()"></p-button>
    </div>
  `,
  imports: [DynamicDialogModule, ButtonModule],
  providers: [DialogService, ModalService],
})
export class ModalDemoComponent {
  constructor(private modalService: ModalService) {}

  openTextModal() {
    this.modalService.openModal(DynamicTextComponent, 'Text Message Dialog', {
      templates: { footer: FooterComponent },
    });
  }
  openFormModal() {
    this.modalService.openModal(DynamicFormComponent, 'Form Dialog', {});
  }
}

export default {
  title: 'Components/Modal',
  component: ModalDemoComponent,
  decorators: [
    moduleMetadata({
      imports: [DynamicDialogModule, ButtonModule, BrowserAnimationsModule],
      providers: [ModalService, DialogService],
    }),
  ],
} as Meta;

const Template: StoryFn = () => ({
  component: ModalDemoComponent,
});

export const Default = Template.bind({});
