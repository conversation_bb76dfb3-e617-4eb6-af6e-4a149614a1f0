import { Meta, moduleMetadata, StoryFn } from '@storybook/angular';
import { Component, ViewChild } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { PopupComponent } from '../../app/components/popup/popup.component';
import { CommonModule, NgComponentOutlet } from '@angular/common';
import { TextareaComponent } from '../../app/components/textarea/textarea.component';

// Dynamic content for the popup
@Component({
  selector: 'dynamic-content',
  standalone: true,
  template: `
    <h4 class="m-0 mb-2">Demo Form</h4>
    <form class="w-full " (ngSubmit)="submitForm()">
      <app-textarea
        placeholder="Enter your message..."
        [maxLength]="10"
        [disabled]="false"
        [rows]="5"
        [required]="true"
        [invalid]="isTextInvalid"
        formControlName="message"
      ></app-textarea>
      <p-button type="submit" severity="success" class="mt-4">Submit</p-button>
    </form>
  `,
  imports: [ButtonModule, TextareaComponent],
})
class DynamicContentComponent {
  onSubmit() {
    console.log('Form Submitted');
  }
}

// Wrapper component for the story
@Component({
  selector: 'popup-story-wrapper',
  standalone: true,
  imports: [
    ButtonModule,
    OverlayPanelModule,
    PopupComponent,
    DynamicContentComponent,
    CommonModule,
    NgComponentOutlet,
  ],
  template: `
    <div class="flex justify-center items-center h-screen">
      <p-button type="button" (click)="openDynamicPopup($event)">
        Open Popup
      </p-button>
      <tms-popup #popup></tms-popup>
    </div>
  `,
})
class PopupStoryWrapperComponent {
  @ViewChild(PopupComponent) confirmPopup!: PopupComponent;

  openDynamicPopup(event: Event) {
    this.confirmPopup.openPopup(event, DynamicContentComponent);
  }
}

export default {
  title: 'Components/Popup',
  component: PopupStoryWrapperComponent,
  decorators: [
    moduleMetadata({
      imports: [PopupStoryWrapperComponent],
    }),
  ],
} as Meta<PopupComponent>;

// Template to render the wrapper component in the story
const Template: StoryFn = () => ({
  component: PopupStoryWrapperComponent,
});

export const Default = Template.bind({});
