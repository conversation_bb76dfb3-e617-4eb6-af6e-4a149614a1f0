import { Meta } from '@storybook/angular';
import { StoryFn } from '@storybook/angular';
import { ButtonComponent } from '../../app/components/button/button.component';
import { ButtonModule } from 'primeng/button';

/**
 * Storybook stories for the ButtonComponent.
 * These stories demonstrate various button configurations including text-only, icon, loading, disabled, and different severity levels.
 */
export default {
  title: 'Components/Button',
  component: ButtonComponent,
  imports: [ButtonModule],
} as Meta;

// Base template for creating button stories
const Template: StoryFn<ButtonComponent> = (args: any) => ({
  props: args,
});

// Basic button with a label
export const BasicButton = Template.bind({});
BasicButton.args = {
  label: 'Submit',
};

// Link-styled button
export const LinkButton = Template.bind({});
LinkButton.args = {
  label: 'Link',
  isLink: true,
};

// Button with only an icon
export const IconButton = Template.bind({});
IconButton.args = {
  icon: 'pi pi-user',
};

// Button with both icon and text
export const IconTextButton = Template.bind({});
IconTextButton.args = {
  label: 'User',
  icon: 'pi pi-user',
};

// Text-only button with no background or border
export const TextOnlyButton = Template.bind({});
TextOnlyButton.args = {
  label: 'User',
  text: true,
};

// Button with a loading spinner
export const LoadingButton = Template.bind({});
LoadingButton.args = {
  label: 'User',
  loading: true,
};

// Disabled button, non-clickable
export const DisabledButton = Template.bind({});
DisabledButton.args = {
  label: 'User',
  disabled: true,
};

// Button with success severity styling
export const ButtonSeverity = Template.bind({});
ButtonSeverity.args = {
  label: 'Success',
  severity: 'success',
};

// Button with rounded corners
export const RoundedButton = Template.bind({});
RoundedButton.args = {
  label: 'User',
  rounded: true,
};

// Button with outlined styling
export const OutlinedButton = Template.bind({});
OutlinedButton.args = {
  label: 'User',
  outlined: true,
};

// Button with a badge indicator
export const ButtonWithBadge = Template.bind({});
ButtonWithBadge.args = {
  label: 'User',
  badge: '3',
};

// Large-sized button
export const LargeButton = Template.bind({});
LargeButton.args = {
  label: 'User',
  size: 'small',
};
