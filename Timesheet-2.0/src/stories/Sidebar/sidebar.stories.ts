import { Meta, StoryFn } from '@storybook/angular';
import { SidebarModule } from 'primeng/sidebar';
import { BadgeModule } from 'primeng/badge';
import { ButtonModule } from 'primeng/button';
import { RippleModule } from 'primeng/ripple';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { Component, signal } from '@angular/core';
import { SidebarComponent } from '../../app/components/sidebar/sidebar.component';

@Component({
  selector: 'story-wrapper',
  template: `
    <div class="flex">
      <tms-sidebar
        [isSidebarVisible]="isSidebarVisible()"
        [menuItems]="menuItems"
        #sidebar
      ></tms-sidebar>
      <p-button
        label="Toggle Sidebar"
        (click)="sidebar.toggleSidebar()"
        class="p-button-outlined"
        style="margin-bottom: 20px;"
      ></p-button>
    </div>
  `,
  standalone: true,
  imports: [SidebarComponent, ButtonModule],
})
class WrapperComponent {
  isSidebarVisible = signal(false);
  menuItems: any;
}

export default {
  title: 'Components/Sidebar',
  component: WrapperComponent,
  declarations: [WrapperComponent],
  imports: [
    SidebarModule,
    RippleModule,
    ButtonModule,
    RouterModule,
    BadgeModule,
    CommonModule,
  ],
  argTypes: {
    menuItems: { control: 'object' },
  },
} as Meta;

const Template: StoryFn = (args: any) => ({
  props: {
    ...args,
  },
  template: `
    <story-wrapper [menuItems]="menuItems"></story-wrapper>
  `,
});

// Default Story
export const Default = Template.bind({});
Default.args = {
  menuItems: [
    {
      title: 'DASHBOARDS',
      items: [
        {
          label: 'Dashboard',
          icon: 'pi pi-objects-column',
          url: '/dashboard',
          badge: '1',
        },
        { label: 'Tracker', icon: 'pi pi-stopwatch', url: '/tracker' },
      ],
    },
    {
      title: 'DATA',
      items: [
        {
          label: 'Projects',
          icon: 'pi pi-book',
          url: '/projects',
          items: [
            { label: 'Kanban', icon: 'pi pi-list', url: '/projects/kanban' },
          ],
          expanded: false,
        },
        {
          label: 'Calendar',
          icon: 'pi pi-calendar',
          url: '/calendar',
          badge: '8',
        },
        { label: 'Teams', icon: 'pi pi-comments', url: '/teams' },

        {
          label: 'Leaves',
          icon: 'pi pi-envelope',
          url: '/leaves',
          expanded: false,
          badge: '10',
        },
      ],
    },
  ],
};
