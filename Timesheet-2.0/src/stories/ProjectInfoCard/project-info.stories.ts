import { CommonModule } from '@angular/common';
import { Meta, StoryFn } from '@storybook/angular';
import { ProjectInfoCardComponent } from '../../app/components/project-info-card/project-info-card.component';
import { ProjectDetailsById } from '../../app/services/project/project.model';

export default {
  title: 'Components/ProjectInfoCard',
  component: ProjectInfoCardComponent,
  imports: [CommonModule, ProjectInfoCardComponent],
} as Meta;

const activeProject: ProjectDetailsById = {
  id: 'be9bf3c7-d0c5-4d88-a9b6-89e77caa1d7c',
  projectName: 'Time Sheet',
  contactName: 'Praveen',
  contactEmail: '<EMAIL>',
  contactPhoneNumber: '+919988776655',
  description: 'Time sheet description',
  startDate: '2024-12-27T07:12:53.125Z',
  endDate: '2024-12-27T07:12:53.125Z',
  billable: true,
  createdBy: 'be9bf3c7-d0c5-4d88-a9b6-89e77caa1d7c',
  clientId: 'be9bf3c7-d0c5-4d88-a9b6-89e77caa1d7c',
  projectStatus: 'active',
  createdAt: '2024-12-27T07:12:53.125Z',
  updatedAt: '2024-12-27T07:12:53.125Z',
  deleted: true,
  client: {
    name: 'CodeCraft',
  },
  contractResource: [
    {
      id: 'cfb8182a-ae05-4a7f-a396-66fe866fe3a6',
      name: 'Prasanna',
      role: 'manager',
    },
  ],
  resourcesList: [
    {
      name: 'Azhan',
      designation: 'Backend developer',
      profilePicUrl:
        'https:///profile/aa1b9531-6301-4041-bc17-94b9183990cc.png',
    },
  ],
  projectBudgetDetails: [
    {
      id: 'cfb8182a-ae05-4a7f-a396-66fe866fe3a6',
      billingSettings: 'fixed',
      amount: 100,
    },
  ],
};

const inactiveProject: ProjectDetailsById = {
  id: 'be9bf3c7-d0c5-4d88-a9b6-89e77caa1d7c',
  projectName: 'Time Sheet',
  contactName: 'Praveen',
  contactEmail: '<EMAIL>',
  contactPhoneNumber: '+919988776655',
  description: 'Time sheet description',
  startDate: '2024-12-27T07:12:53.125Z',
  endDate: '2024-12-27T07:12:53.125Z',
  billable: true,
  createdBy: 'be9bf3c7-d0c5-4d88-a9b6-89e77caa1d7c',
  clientId: 'be9bf3c7-d0c5-4d88-a9b6-89e77caa1d7c',
  projectStatus: 'inActive',
  createdAt: '2024-12-27T07:12:53.125Z',
  updatedAt: '2024-12-27T07:12:53.125Z',
  deleted: true,
  client: {
    name: 'CodeCraft',
  },
  contractResource: [
    {
      id: 'cfb8182a-ae05-4a7f-a396-66fe866fe3a6',
      name: 'Prasanna',
      role: 'manager',
    },
  ],
  resourcesList: [
    {
      name: 'Azhan',
      designation: 'Backend developer',
      profilePicUrl:
        'https:///profile/aa1b9531-6301-4041-bc17-94b9183990cc.png',
    },
  ],
  projectBudgetDetails: [
    {
      id: 'cfb8182a-ae05-4a7f-a396-66fe866fe3a6',
      billingSettings: 'fixed',
      amount: 100,
    },
  ],
};

// Base template for all stories
const Template: StoryFn<ProjectInfoCardComponent> = (args: any) => ({
  props: args,
});

export const ActiveProject = Template.bind({});
ActiveProject.args = {
  project: activeProject,
};

export const InactiveProject = Template.bind({});
InactiveProject.args = {
  project: inactiveProject,
};
