export const HALF_DAY_MINUTES = 240;
export const FULL_DAY_MINUTES = 480;
export const EXPECTED_WORK_DAYS_PER_MONTH = 20;
export const DASHBOARD = 'dashboard';
export const EXCLUDED_BREADCRUMB_ROUTES: string[] = ['dashboard/tracker'];
export const SELECTED_PROJECTS = 'selectedProjects';
export const WORKLOG_DISPLAY_LIMIT = 2;
export const MAXIMUM_WORK_MINUTES_PER_DAY = 960;
export const DATE_FORMAT_YYYY_MM_DD = 'yyyy-MM-dd';
export const MAX_INT_VALUE = 2147483647;

export const CONTRACT_ID_PATTERN = /^[A-Za-z0-9\-_/#.() &]+$/;
export const ZIPCODE_PATTERN = /^(?=.*\d)[A-Za-z0-9\s\-]{3,10}$/;

export const PHONE_NUMBER_PATTERNS = {
  '+91': '^[6-9]\\d{9}$', // India: 10 digits, typically start with 6-9
  '+1': '^(\\d{3}[- .]?){2}\\d{4}$', // US/Canada: 10 digits (xxx-xxx-xxxx) format with optional formatting characters
  '+44': '^\\d{10,11}$', // UK area codes can be 2-5 digits, local numbers 4-8 digits, total must be 10-11 digits
};
