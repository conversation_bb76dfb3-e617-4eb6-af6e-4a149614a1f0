import { Injectable } from '@angular/core';
import {
  CanActivate,
  Router,
} from '@angular/router';
import { catchError, map, Observable, of } from 'rxjs';
import { AuthService } from '../services/auth/auth-service';

/**
 * AuthGuard
 *
 * This guard protects routes that require authentication and ensures
 * that only authenticated users have access to certain routes
 */
@Injectable({
  providedIn: 'root',
})
export class AuthGuard implements CanActivate {
  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  /**
   * Determines if the route can be activated and ensures that only logged in
   * users can access certain routes and redirecting unauthorized users back
   * to the login page
   */
  canActivate(): Observable<boolean> {
    return this.authService.isAuthenticated().pipe(
      map((isAuthenticated) => {
        if (isAuthenticated) {
          return true; // User is authenticated
        }
        this.router.navigate(['/login']); // Redirect to login page
        return false; // User is not authenticated
      }),
      catchError(() => {
        this.router.navigate(['/login']); // Redirect on error
        return of(false); // Return an observable with false
      })
    );
  }
}
