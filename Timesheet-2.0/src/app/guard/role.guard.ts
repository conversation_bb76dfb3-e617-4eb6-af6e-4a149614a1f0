import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, Router } from '@angular/router';
import { catchError, map, Observable, of } from 'rxjs';
import { AuthService } from '../services/auth/auth-service';

interface AuthResponse {
  payload: {
    id: string;
    email: string;
    role: 'admin' | 'manager' | 'employee' | 'finance';
  };
}

interface AuthenticationResponse {
  isAuthenticated: boolean;
  user: AuthResponse;
}
/**
 * RoleGuard
 *
 * This guard is used to protect routes based on user roles.
 * It checks if the user is authenticated and has the required role(s)
 * to access a specific route. If the user does not have the required role,
 * they are redirected to the tracker dashboard.
 */
@Injectable({
  providedIn: 'root',
})
export class RoleGuard implements CanActivate {
  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  /**
   * Determines if the route can be activated based on user roles.
   */
  canActivate(route: ActivatedRouteSnapshot): Observable<boolean> {
    const roles = route.data['roles'];
    return this.authService.isAuthenticated().pipe(
      map((response: AuthenticationResponse | boolean) => {
        if (typeof response === 'boolean') {
          if (!response) {
            this.router.navigate(['/dashboard/tracker']);
          }
          return response;
        }
        if (roles.includes(response.user?.payload?.role)) {
          return true;
        }
        this.router.navigate(['/dashboard/tracker']);
        return false;
      }),
      catchError(() => {
        this.router.navigate(['/dashboard/tracker']);
        return of(false);
      })
    );
  }
}
