import { Injectable } from '@angular/core';
import {
  CanActivate,
  Router,
} from '@angular/router';
import { AuthService } from '../services/auth/auth-service';
import { catchError, map, Observable, of } from 'rxjs';

/**
 * LoginGuard
 *
 * This guard restricts authenticated users from accessing the login page and the guard
 * is used in the route of the login page
 */
@Injectable({
  providedIn: 'root',
})
export class LoginGuard implements CanActivate {
  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  /**
   * Determines if the route can be activated and ensures that this can be activated
   * only if the user is not logged in
   */
  canActivate(): Observable<boolean> {
    return this.authService.isAuthenticated().pipe(
      map((isAuthenticated) => {
        if (!isAuthenticated) {
          return true; // User is authenticated
        }
        this.router.navigate(['/dashboard/tracker']); // Redirect to login page
        return false; // User is not authenticated
      }),
      catchError(() => {
        this.router.navigate(['/dashboard/tracker']); // Redirect on error
        return of(false); // Return an observable with false
      })
    );
  }
}
