/**
 * Calculates the total number of leave days between two dates, excluding weekends (Saturdays and Sundays).
 * It also accounts for half-day leaves on the start or end dates.
 *
 * @param startDate - The start date of the leave (can be null).
 * @param endDate - The end date of the leave (can be null).
 * @param typeOfLeaveForFrom - Specifies the type of leave for the start date ('fullDay' or 'halfDay').
 * @param typeOfLeaveForTo - Specifies the type of leave for the end date ('fullDay' or 'halfDay').
 * @returns The total number of leave days, excluding weekends and accounting for half-day leaves.
 */
export const calculateTotalLeaveDays = (
    startDate: Date | null,
    endDate: Date | null,
    typeOfLeaveForFrom: string,
    typeOfLeaveForTo: string,
  ): number => {
    let totalDays = 0;
    let currentDate = startDate && new Date(startDate);
  
    while (!!currentDate && !!endDate && currentDate <= endDate) {
      const dayOfWeek = currentDate.getDay();
      const SUNDAY = 0;
      const SATURDAY = 6;
  
      if (dayOfWeek !== SUNDAY && dayOfWeek !== SATURDAY) {
        if (
          (currentDate.getDate() === startDate?.getDate() &&
            typeOfLeaveForFrom === 'halfDay') ||
          (currentDate.getDate() === endDate?.getDate() &&
            typeOfLeaveForTo === 'halfDay')
        ) {
          totalDays += 0.5;
        } else {
          totalDays++;
        }
      }
  
      currentDate.setDate(currentDate.getDate() + 1);
    }
   return totalDays;
  };
  