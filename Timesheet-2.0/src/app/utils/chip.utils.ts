import { Worklog } from '../services/worklog/worklog.model';

/**
 * Returns chip details (text and CSS class) based on the worklog status.
 *
 * - Provides visual indicators for worklog statuses such as 'submitted,' 'approved,' 'rejected,' and 'revised.'
 * - Utilized in UI components to display consistent styles and labels for different worklog statuses.
 *
 * @param worklog :
 * - worklog: Worklog - The worklog object containing status information.
 *
 * @returns :
 * - An object containing 'chipText' and 'chipClass' for styling purposes.
 * - Returns 'null' if no matching status is found.
 */
export function getWorklogChipDetails(worklog: Worklog): {
  chipText: string;
  chipClass: string;
} | null {
  const optionsMap = [
    {
      condition: () => worklog.workLogStatus?.status === 'submitted',
      chipDetails: {
        chipText: 'Submitted',
        chipClass: 'bg-primary-100 text-primary-600',
      },
    },
    {
      condition: () => worklog.workLogStatus?.status === 'approved',
      chipDetails: {
        chipText: 'Approved',
        chipClass: 'bg-green-100 text-green-600',
      },
    },
    {
      condition: () => worklog.workLogStatus?.status === 'rejected',
      chipDetails: {
        chipText: 'Rejected',
        chipClass: 'bg-red-100 text-red-600',
      },
    },
    {
      condition: () => worklog.workLogStatus?.status === 'revised',
      chipDetails: {
        chipText: 'Revised',
        chipClass: 'bg-purple-100 text-purple-600',
      },
    },
  ];

  for (const option of optionsMap) {
    if (option.condition()) {
      return option.chipDetails;
    }
  }

  return null;
}

/**
 * Returns CSS class string for styling status chips based on project or contract status.
 *
 * - Provides consistent styling for project or contract statuses such as 'active,' 'inactive,' and 'completed.'
 * - Used in UI components to apply visual styles dynamically.
 *
 * @param worklog :
 * - status: string - The status string for which the CSS class needs to be determined.
 *
 * @returns :
 * - A string containing CSS classes for styling the chip.
 */
export function getStatusChipStyle(status: string): string {
  const baseClass =
    'px-5 flex items-center justify-center text-xs font-bold rounded-md w-28 ';
  switch (status) {
    case 'COMPLETED':
      return `${baseClass} bg-primary-100 text-primary-600`;
    case 'ACTIVE':
      return `${baseClass} bg-green-100 text-green-600`;
    case 'INACTIVE':
      return `${baseClass} bg-gray-100 text-gray-600`;
    default:
      return `${baseClass} bg-gray-300 text-black`;
  }
}
