export function formatHoursAndMinutes(valueInMinutes: number): string {
  const hours = Math.floor(valueInMinutes / 60)
    .toString()
    .padStart(2, '0');
  const minutes = (valueInMinutes % 60).toString().padStart(2, '0');
  return `${hours}:${minutes}`;
}
// Utility function to format decimal hours into HH:MM format
export function formatHoursToTimeString(decimalHours: number): string {
  if (isNaN(decimalHours)) return '-';

  const wholeHours = Math.floor(decimalHours);
  const minutes = Math.round((decimalHours - wholeHours) * 60);
  const formattedMinutes = minutes.toString().padStart(2, '0');

  return `${wholeHours}${minutes ? `:${formattedMinutes}` : ''}`;
}

// Utility function to convert minutes to hours in a string format
export function convertMinutesToHours(minutes: number): number {
  const hours = Math.floor(minutes / 60);
  return hours;
}
