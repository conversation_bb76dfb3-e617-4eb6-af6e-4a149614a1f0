import { Component, input, ViewEncapsulation } from '@angular/core';
import { AvatarModule } from 'primeng/avatar';
import { AvatarGroupModule } from 'primeng/avatargroup';
import { BadgeModule } from 'primeng/badge';
import { AvatarLetterPipe } from '../../pipes/avatar-letter.pipe';

export type AvatarSize = 'normal' | 'large' | 'xlarge';
export type AvatarShape = 'square' | 'circle';
export type BadgeSeverity = 'success' | 'info' | 'warning' | 'danger';

@Component({
  selector: 'tms-avatar',
  standalone: true,
  imports: [AvatarGroupModule, AvatarModule, BadgeModule, AvatarLetterPipe],
  templateUrl: './avatar.component.html',
  styleUrl: './avatar.component.css',
  encapsulation: ViewEncapsulation.None,
})
export class AvatarComponent {
  label = input<string>();
  icon = input<string>();
  image = input<string>();
  size = input<AvatarSize>('normal');
  shape = input<AvatarShape>();
  value = input<number | string>(0);
  style = input<{ [cssProp: string]: any }>();
  styleClass = input<string>();
  ariaLabel = input<string>();
  ariaLabelledBy = input<string>();
  badgeDisabled = input<boolean>(false);
  onImageError = input<(event: Event) => {}>();
  severity = input<BadgeSeverity>();
}
