import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TimelineHistoryViewComponent } from './timeline-history-view.component';
import { StringUtilsService } from '../../services/stringutils/stringutils.service';
import { ProjectsService } from '../../services/project/project.service';
import { MessageService } from 'primeng/api';
import { QueryClient } from '@tanstack/angular-query-experimental';
import { TIMELINE_MIN_VISIBLE } from '../../constants/constant';
import { ComponentRef } from '@angular/core';
import {
  BrowserAnimationsModule,
  NoopAnimationsModule,
} from '@angular/platform-browser/animations';

/**
 * Test suite for the TimelineHistoryViewComponent.
 */
describe('TimelineHistoryViewComponent', () => {
  let component: TimelineHistoryViewComponent;
  let componentRef: ComponentRef<TimelineHistoryViewComponent>;
  let fixture: ComponentFixture<TimelineHistoryViewComponent>;
  let mockStringUtilsService: jasmine.SpyObj<StringUtilsService>;
  let mockProjectsService: jasmine.SpyObj<ProjectsService>;
  let mockMessageService: jasmine.SpyObj<MessageService>;

  beforeEach(async () => {
    mockStringUtilsService = jasmine.createSpyObj('StringUtilsService', [
      'convertContractHistoryToTimelineData',
      'convertProjectHistoryToTimelineData',
      'capitalizeFirstLetter',
    ]);
    mockProjectsService = jasmine.createSpyObj('ProjectsService', [
      'projectsContractHistory',
    ]);
    mockMessageService = jasmine.createSpyObj('MessageService', ['add']);

    await TestBed.configureTestingModule({
      imports: [TimelineHistoryViewComponent, BrowserAnimationsModule],
      providers: [
        { provide: StringUtilsService, useValue: mockStringUtilsService },
        { provide: ProjectsService, useValue: mockProjectsService },
        { provide: MessageService, useValue: mockMessageService },
        QueryClient,
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(TimelineHistoryViewComponent);
    component = fixture.componentInstance;
    componentRef = fixture.componentRef;
    componentRef.setInput('timelineHeader', 'Default Test Header');
    fixture.detectChanges();
  });

  /**
   * Test to verify that the component is created successfully.
   */
  it('should create', () => {
    expect(component).toBeTruthy();
  });

  /**
   * Test to check that the component initializes with the correct default values.
   */
  it('should initialize with the correct default values', () => {
    expect(component.isDrawerVisible).toBeFalse();
    expect(component.timelineHistoryEvents()).toEqual([]);
    expect(component.isShowMoreButtonVisible()).toBeFalse();
  });

  /**
   * Test to verify that the drawer visibility toggles when a button is clicked.
   */
  it('should toggle drawer visibility when button is clicked', () => {
    expect(component.isDrawerVisible).toBeFalse();

    component.isDrawerVisible = true;
    fixture.detectChanges();

    expect(component.isDrawerVisible).toBeTrue();
  });

  /**
   * Test to check if "Show More" button is displayed when events exceed TIMELINE_MIN_VISIBLE.
   */
  it('should show "Show More" button when events exceed TIMELINE_MIN_VISIBLE', () => {
    const mockEvents = Array(TIMELINE_MIN_VISIBLE + 1).fill({
      projectId: '1',
      date: '2025-01-01',
      action: 'Updated',
      projectName: 'Test Project',
      personId: '123',
      personName: 'John Doe',
    });

    // Use the set() method to update the input signal
    componentRef.setInput('timelineHistoryEvents', mockEvents);

    expect(component.isShowMoreButtonVisible()).toBeTrue();
  });
});
