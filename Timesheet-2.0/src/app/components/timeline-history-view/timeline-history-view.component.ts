import { CommonModule } from '@angular/common';
import { Component, computed, input, ViewEncapsulation } from '@angular/core';
import { Message, MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { MessagesModule } from 'primeng/messages';
import { SidebarModule } from 'primeng/sidebar';
import { SkeletonModule } from 'primeng/skeleton';
import { TimelineModule } from 'primeng/timeline';
import { ToastModule } from 'primeng/toast';
import { TIMELINE_MIN_VISIBLE } from '../../constants/constant';
import { ContractHistoryResponse } from '../../services/contract/contract.model';
import {
  ProjectHistoryResponse,
  TimelineData,
} from '../../services/project/project.model';
import { StringUtilsService } from '../../services/stringutils/stringutils.service';

/**
 * Component for displaying the timeline history in a timeline format.
 *
 * This component retrieves timeline history data for a specific project or contract
 * and presents it in a timeline view. It allows users to see updates
 * and changes related to contracts over time.
 */
@Component({
  selector: 'tms-timeline-history-view',
  standalone: true,
  imports: [
    ButtonModule,
    SidebarModule,
    CommonModule,
    TimelineModule,
    CardModule,
    ToastModule,
    MessagesModule,
    SkeletonModule,
  ],
  templateUrl: './timeline-history-view.component.html',
  styleUrl: './timeline-history-view.component.css',
  encapsulation: ViewEncapsulation.None,
  providers: [MessageService],
})
export class TimelineHistoryViewComponent {
  isDrawerVisible: boolean = false;
  timelineHeader = input.required<string>();
  timelineHistoryEvents = input<
    ProjectHistoryResponse[] | ContractHistoryResponse[]
  >([]);
  projectId = input<string>('');
  errorMessage = input<Message[]>([]);
  isLoading = input<boolean>();
  hasError = input<boolean>();
  skeletonRows = Array(4);

  constructor(public stringUtilsService: StringUtilsService) {}

  /**
   * Converts a list of events into a timeline data format.
   */
  private convertToTimelineData(
    events: ProjectHistoryResponse[] | ContractHistoryResponse[]
  ) {
    return this.timelineHeader() === 'Project History'
      ? this.stringUtilsService.convertProjectHistoryToTimelineData(
          events as ProjectHistoryResponse[]
        )
      : this.stringUtilsService.convertContractHistoryToTimelineData(
          events as ContractHistoryResponse[]
        );
  }

  /**
   * Computed property that returns the timeline entries to be displayed.
   */
  displayedTimelineEntries = computed<TimelineData[]>(() => {
    const timelineEvents = this.timelineHistoryEvents().slice(
      0,
      TIMELINE_MIN_VISIBLE
    );
    return this.convertToTimelineData(timelineEvents);
  });

  /**
   * Computed property that determines if the "Show More" button should be visible.
   */
  isShowMoreButtonVisible = computed<boolean>(
    () => this.timelineHistoryEvents().length > TIMELINE_MIN_VISIBLE
  );

  /**
   * Loads more events when the "Show More" button is clicked.
   *
   * This method updates the displayed timeline entries to show all available
   * events and hides the "Show More" button.
   */
  loadMoreEvents(): void {
    this.displayedTimelineEntries = computed(() => {
      const timelineEvents = this.timelineHistoryEvents();
      return this.convertToTimelineData(timelineEvents);
    });
    this.isShowMoreButtonVisible = computed(() => false);
  }
}
