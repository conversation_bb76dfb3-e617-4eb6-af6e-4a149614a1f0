<!-- Drawer component for displaying project history in a timeline format -->
<p-button
  icon="pi pi-clock"
  size="small"
  rounded="true"
  (onClick)="isDrawerVisible = true"
  [attr.aria-label]="'Open history drawer'"
></p-button>
<p-sidebar
  position="right"
  [(visible)]="isDrawerVisible"
  styleClass="contract-sidebar"
>
  <ng-template pTemplate="header">
    <span class="font-semibold text-xl">{{ timelineHeader() }}</span>
  </ng-template>
  <div class="pt-5">
    <p-timeline
      align="right"
      [value]="displayedTimelineEntries()"
      class="contract-drawer"
    >
      <ng-template pTemplate="marker">
        <div
          class="w-4 h-4 rounded-lg bg-primary-500 border-2 border-solid border-primary-500"
        ></div>
      </ng-template>
      <ng-template pTemplate="content" let-event>
        <div class="flex flex-col ml-4">
          <span class="text-sm text-neutral-500 italic whitespace-nowrap">{{
            event.date | date: 'EEE, MMM d, yy'
          }}</span>
          <span class="text-sm text-neutral-500 font-normal italic">
            {{ event.date | date: 'h:mm a' }}
          </span>
        </div>
      </ng-template>
      <ng-template pTemplate="opposite" let-event>
        <div class="-mt-5 pb-10">
          <h1 class="text-neutral-500 text-base font-medium uppercase my-1">
            {{ event.title }}
          </h1>
          <p class="my-1 text-sm">
            <span class="text-neutral-500 mr-1">by</span>
            <span class="text-neutral-800 font-medium">{{
              stringUtilsService.capitalizeFirstLetter(event.author)
            }}</span>
          </p>
          <p class="text-sm m-0 text-neutral-500 leading-5">
            {{ event.subTitle }}
          </p>
        </div>
      </ng-template>
    </p-timeline>
    <!-- Show More button to load additional events -->
    @if (isShowMoreButtonVisible() === true) {
      <p-button
        label="Show More"
        text="true"
        (onClick)="loadMoreEvents()"
        [attr.aria-label]="'Show more'"
      ></p-button>
    }
    @if (isLoading()) {
      @for (_ of skeletonRows; track $index) {
        <div class="flex items-center mb-8">
          <div class="w-1/2 pr-4">
            <p-skeleton height="1rem" styleClass="mb-2 w-3/4"></p-skeleton>
            <p-skeleton height="0.75rem" styleClass="mb-2 w-1/2"></p-skeleton>
          </div>
          <div class="w-4 h-4 rounded-lg bg-gray-200 mx-2"></div>
          <div class="w-1/2 pl-4">
            <p-skeleton height="0.75rem" styleClass="mb-2 w-1/4"></p-skeleton>
            <p-skeleton height="0.75rem" styleClass="mb-2 w-1/3"></p-skeleton>
          </div>
        </div>
      }
      @if (isLoading()) {
        @for (_ of skeletonRows; track $index) {
          <div class="flex items-center mb-8">
            <div class="w-1/2 pr-4">
              <p-skeleton height="1rem" styleClass="mb-2 w-3/4"></p-skeleton>
              <p-skeleton height="0.75rem" styleClass="mb-2 w-1/2"></p-skeleton>
            </div>
            <div class="w-4 h-4 rounded-lg bg-gray-200 mx-2"></div>
            <div class="w-1/2 pl-4">
              <p-skeleton height="0.75rem" styleClass="mb-2 w-1/4"></p-skeleton>
              <p-skeleton height="0.75rem" styleClass="mb-2 w-1/3"></p-skeleton>
            </div>
          </div>
        }
      }
      @if (hasError()) {
        <p-messages
          [value]="errorMessage()"
          [enableService]="false"
          styleClass="w-full"
        ></p-messages>
      }
      @if (displayedTimelineEntries().length === 0) {
        <p>No history to be displayed</p>
      }
    }
  </div>
</p-sidebar>
