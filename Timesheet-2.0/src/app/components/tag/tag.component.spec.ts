import { ComponentFixture, TestBed } from '@angular/core/testing';

import { TagComponent } from './tag.component';

describe('TagComponent', () => {
  let component: TagComponent;
  let fixture: ComponentFixture<TagComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TagComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(TagComponent);
    component = fixture.componentInstance;
    fixture.componentRef.setInput('label', '');
    fixture.componentRef.setInput('styles', '');
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should display the correct label', () => {
    fixture.componentRef.setInput('label', 'Test Label');
    fixture.detectChanges();
    const compiled = fixture.nativeElement as HTMLElement;
    expect(compiled.textContent?.trim()).toBe('Test Label');
  });

  it('should apply the correct styles', () => {
    fixture.componentRef.setInput('styles', 'bg-green-500');
    fixture.detectChanges();
    const divElement = fixture.nativeElement.querySelector('div');
    expect(divElement?.classList).toContain('bg-green-500');
  });
});
