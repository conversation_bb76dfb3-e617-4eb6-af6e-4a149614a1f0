<form
  [formGroup]="clientFormGroup"
  (ngSubmit)="onSubmit()"
  class="flex flex-col space-y-2 w-full"
>
  <div class="flex flex-col gap-1">
    <label for="clientName" class="text-sm text-neutral-500"
      >Client Name<span class="font-bold text-red-500">*</span></label
    >
    <input
      pInputText
      id="clientName"
      formControlName="clientName"
      placeholder="Enter client name"
      [style]="{ width: '100%' }"
    />
    <small class="text-red-500 min-h-[1rem] block">
      {{ checkErrors('clientName') || '\u00A0' }}
    </small>
  </div>

  <div class="grid md:grid-cols-2 gap-4">
    <div class="flex flex-col gap-1">
      <label for="contactName" class="text-sm text-neutral-500"
        >Contact Name<span class="font-bold text-red-500">*</span></label
      >
      <input
        pInputText
        id="contactName"
        formControlName="contactName"
        placeholder="Enter contact name"
        [style]="{ width: '100%' }"
      />
      <small class="text-red-500 min-h-[1rem] block">
        {{ checkErrors('contactName') || '\u00A0' }}
      </small>
    </div>

    <div class="flex flex-col w-full gap-1">
      <label for="contactPhoneNumber" class="text-sm text-neutral-500">
        Contact Number
      </label>

      <div class="flex w-full" id="contactPhoneNumber">
        <p-dropdown
          formControlName="countryCode"
          [options]="countryCodes"
          optionLabel="value"
          [style]="{ width: '100%' }"
          styleClass="border-r-0 rounded-r-none px-0"
          [dropdownIcon]="'pi pi-angle-down'"
        >
          <ng-template pTemplate="selectedItem" let-selected>
            <div class="flex items-center">
              <div>{{ selected.value }}</div>
            </div>
          </ng-template>
          <ng-template let-country pTemplate="item">
            <div class="flex items-center">
              <div>{{ country.label }}</div>
            </div>
          </ng-template>
        </p-dropdown>

        <input
          pInputText
          formControlName="contactPhoneNumber"
          type="tel"
          maxlength="15"
          [style]="{ width: '100%' }"
          placeholder="Enter contact number"
          class="rounded-l-none pl-1"
        />
      </div>
      <small class="text-red-500 min-h-[1rem] block">{{
        checkErrors('contactPhoneNumber' || '\u00A0')
      }}</small>
    </div>
  </div>
  <div class="grid md:grid-cols-2 gap-4">
    <div class="flex flex-col gap-1">
      <label for="contactEmail" class="text-sm text-neutral-500">
        Contact Email
        <span class="text-red-500 text-lg leading-none">*</span>
      </label>
      <input
        pInputText
        id="contactEmail"
        formControlName="contactEmail"
        placeholder="Enter email address"
        [style]="{ width: '100%' }"
      />
      <small class="text-red-500 min-h-[1rem] block">
        {{ checkErrors('contactEmail') || '\u00A0' }}
      </small>
    </div>

    <div class="flex flex-col gap-1">
      <label for="country" class="text-sm text-neutral-500">
        Country
        <span class="text-red-500 text-lg leading-none">*</span>
      </label>
      <p-dropdown
        formControlName="country"
        [options]="countries"
        optionLabel="label"
        [style]="{ width: '100%' }"
        [dropdownIcon]="'pi pi-angle-down'"
        [placeholder]="'Select country'"
      >
        <ng-template pTemplate="selectedItem" let-selected>
          <div class="flex items-center">
            <div>{{ selected.value }}</div>
          </div>
        </ng-template>
        <ng-template let-country pTemplate="item">
          <div class="flex items-center">
            <div>{{ country.label }}</div>
          </div>
        </ng-template>
      </p-dropdown>
      <small class="text-red-500 min-h-[1rem] block">
        {{ checkErrors('country') || '\u00A0' }}
      </small>
    </div>
  </div>

  <div class="grid md:grid-cols-2 gap-4">
    <div class="flex flex-col gap-1">
      <label for="zipcode" class="text-sm text-neutral-500">
        Zip Code
        <span class="text-red-500 text-lg leading-none">*</span>
      </label>
      <input
        pInputText
        id="zipcode"
        formControlName="zipcode"
        placeholder="Enter zip code"
        [style]="{ width: '100%' }"
      />
      <small class="text-red-500 min-h-[1rem] block">
        {{ checkErrors('zipcode') || '\u00A0' }}
      </small>
    </div>

    <div class="flex flex-col gap-1">
      <label for="currency" class="text-sm text-neutral-500">
        Currency
        <span class="text-red-500 text-lg leading-none">*</span>
      </label>
      <p-dropdown
        formControlName="currency"
        [options]="currencyCodes"
        optionLabel="label"
        [style]="{ width: '100%' }"
        [dropdownIcon]="'pi pi-angle-down'"
        placeholder="Select currency"
      >
        <ng-template pTemplate="selectedItem" let-selected>
          <div class="flex items-center">
            <div>{{ selected.label }}</div>
          </div>
        </ng-template>
        <ng-template let-currency pTemplate="item">
          <div class="flex items-center">
            <div>{{ currency.label }}</div>
          </div>
        </ng-template>
      </p-dropdown>
      <small class="text-red-500 min-h-[1rem] block">
        {{ checkErrors('currency') || '\u00A0' }}
      </small>
    </div>
  </div>

  <div class="flex flex-col gap-1">
    <label for="address" class="text-sm text-neutral-500">
      Address<span class="text-lg text-red-500">*</span>
    </label>
    <textarea
      pInputTextarea
      id="address"
      formControlName="address"
      [rows]="3"
      [cols]="30"
      placeholder="Enter address"
      [style]="{ width: '100%' }"
    ></textarea>
    <small class="text-red-500 min-h-[1rem] block">
      {{ checkErrors('address') || '\u00A0' }}
    </small>
  </div>

  <div class="flex flex-row-reverse">
    <p-button
      type="submit"
      [label]="
        formSubmitButtonLabelService.getFormSubmitButtonLabel(
          isSubmitting,
          isEditMode
        )
      "
      [icon]="
        formSubmitButtonLabelService.getFormSubmitButtonIcon(isSubmitting)
      "
      [disabled]="isSubmitButtonDisabled"
      styleClass="submit-button disabled:cursor-not-allowed disabled:bg-neutral-400 disabled:border-neutral-400"
    ></p-button>
  </div>
</form>
