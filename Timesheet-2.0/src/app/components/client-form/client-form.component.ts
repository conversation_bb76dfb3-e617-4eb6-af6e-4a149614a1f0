import { Component } from '@angular/core';
import { AuthService } from '../../services/auth/auth-service';
import { ClientService } from '../../services/client/client.service';
import { InputNumberModule } from 'primeng/inputnumber';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { DropdownModule } from 'primeng/dropdown';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { ButtonModule } from 'primeng/button';
import { InputGroupModule } from 'primeng/inputgroup';
import {
  acceptedIsoCodes,
  currencyLocaleCodes,
} from '../../constants/ISOCodes';
import { ErrorMessage, Option } from '../../services/common.model';
import { FormSubmitButtonLabelService } from '../../services/formsubmitbuttonlabel/formsubmitbuttonlabel.service';
import { injectMutation } from '@tanstack/angular-query-experimental';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import {
  ClientResponse,
  CreateClient,
  UpdateClient,
} from '../../services/client/client.model';
import { PHONE_NUMBER_PATTERNS, ZIPCODE_PATTERN } from '../../settings';
import { COUNTRIES } from '../../constants/constant';
import { CommonModule } from '@angular/common';

interface ClientFormData {
  clientName: string;
  contactName: string;
  contactEmail: string;
  countryCode: Option;
  contactPhoneNumber: string;
  country: Option;
  currency: Option;
  zipcode: string;
  address: string;
}

@Component({
  selector: 'tms-client-form',
  standalone: true,
  imports: [
    InputNumberModule,
    InputTextareaModule,
    DropdownModule,
    ReactiveFormsModule,
    InputTextModule,
    ButtonModule,
    InputGroupModule,
    CommonModule,
  ],
  templateUrl: './client-form.component.html',
  styleUrl: './client-form.component.css',
})
export class ClientFormComponent {
  clientFormGroup: FormGroup;
  countryCodes: Option[];
  countries: Option[];
  currencyCodes: Option[];
  isSubmitting = false;
  isEditMode = false;

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private clientService: ClientService,
    public formSubmitButtonLabelService: FormSubmitButtonLabelService,
    private dialogRef: DynamicDialogRef,
    private dialogConfig: DynamicDialogConfig
  ) {
    this.countryCodes = acceptedIsoCodes;
    this.currencyCodes = currencyLocaleCodes;
    this.countries = COUNTRIES;
    this.isEditMode = dialogConfig.data?.isEditMode;

    this.clientFormGroup = this.formBuilder.group(
      {
        clientName: [
          '',
          [
            Validators.required,
            Validators.minLength(2),
            Validators.maxLength(50),
            Validators.pattern(/^(?!\s*$).+/),
          ],
        ],
        contactName: [
          '',
          [
            Validators.required,
            Validators.minLength(2),
            Validators.maxLength(50),
            Validators.pattern(/^(?!\s*$).+/),
          ],
        ],
        contactEmail: ['', [Validators.required, Validators.email]],
        countryCode: [''],
        contactPhoneNumber: [
          '',
          {
            validators: [Validators.pattern('^\\d{10}$')],
            updateOn: 'blur',
          },
        ],
        country: ['', Validators.required],
        zipcode: [
          '',
          [
            Validators.required,
            Validators.pattern(ZIPCODE_PATTERN),
            Validators.pattern(/^(?!\s*$).+/),
          ],
        ],
        currency: ['', Validators.required],
        address: ['', [Validators.required, Validators.pattern(/^(?!\s*$).+/)]],
      },
      {
        validators: [this.countryCodeAndPhoneValidator()],
      }
    );

    this.clientFormGroup
      ?.get('countryCode')
      ?.valueChanges.subscribe((selectedCountry) => {
        if (selectedCountry && selectedCountry.value) {
          const countryCode =
            selectedCountry.value as keyof typeof PHONE_NUMBER_PATTERNS;
          const phoneControl = this.clientFormGroup?.get('contactPhoneNumber');

          // Update the validation pattern based on selected country
          phoneControl?.setValidators([
            Validators.pattern(PHONE_NUMBER_PATTERNS[countryCode] || '^\\d+$'),
          ]);

          // Important: This triggers validation re-check
          phoneControl?.updateValueAndValidity();
        }
      });

    this.clientFormGroup
      .get('contactPhoneNumber')
      ?.valueChanges.subscribe(() => {
        this.clientFormGroup.updateValueAndValidity({
          onlySelf: false,
          emitEvent: false,
        });
      });

    this.clientFormGroup.get('countryCode')?.valueChanges.subscribe(() => {
      this.clientFormGroup.updateValueAndValidity({
        onlySelf: false,
        emitEvent: false,
      });
    });
  }

  /**
   * Populate the form with the data on initialization.
   */
  ngAfterContentInit() {
    if (this.isEditMode && this.dialogConfig.data?.clientData) {
      this.loadClientData();
    }
  }

  /**
   * Loads existing form data into the reactive form if provided.
   */
  private loadClientData() {
    const clientData: ClientResponse = this.dialogConfig.data?.clientData;
    const { matchedCode, phoneNumber } = this.loadPhoneNumberDetails(
      clientData.contactPhoneNumber ?? ''
    );
    const selectedCurrency = this.currencyCodes.find(
      (option) => option.label === clientData.currency
    );

    const selectedCountry = this.countries.find(
      (option) => option.value === clientData.country
    );

    if (clientData && this.isEditMode) {
      this.clientFormGroup.patchValue({
        clientName: clientData.name,
        contactName: clientData.contactName,
        countryCode: phoneNumber ? matchedCode : '',
        contactPhoneNumber: (phoneNumber ?? '').trim(),
        contactEmail: clientData.contactEmail,
        address: clientData.address,
        zipcode: clientData.zipcode,
        country: selectedCountry,
        currency: selectedCurrency,
      });
      this.clientFormGroup.updateValueAndValidity({
        onlySelf: false,
        emitEvent: true,
      });
    }
  }

  private loadPhoneNumberDetails(fullPhoneNumber: string) {
    let matchedCode: Option | null = null;
    let phoneNumber: string | null = null;
    for (const code of this.countryCodes) {
      if (fullPhoneNumber?.startsWith(code.value)) {
        matchedCode = code;
        phoneNumber = fullPhoneNumber.substring(code.value.length);
        break;
      }
    }
    return { matchedCode, phoneNumber };
  }

  /**
   * Mutation hook for creating a new client.
   * It triggers a mutation to create a new client and handles success/error responses.
   */
  createClientQuery = injectMutation(() => ({
    mutationKey: ['client', this.authService.userId()],
    mutationFn: (clientData: CreateClient) =>
      this.clientService.addClient(clientData),
    onSuccess: (data) => {
      this.clientService.getAllClientsQuery().refetch();
      this.dialogRef.close({
        type: 'success',
        severity: data.status,
        message: data.message,
        summary: 'Client created successfully.',
      });
    },
    onError: (error) => {
      console.error('Error creating client:', error);

      // Check if error.message is an array
      const errorMessages = Array.isArray(error.message)
        ? error.message
        : [error.message];

      errorMessages.forEach((msg: string) => {
        // Show a separate toast message for each error
        this.dialogRef.close({
          type: 'error',
          severity: 'error',
          message: msg,
          summary: 'Failed to create a client',
        });
      });
    },
  }));

  /**
   * Mutation to update an existing client based on form input.
   */
  updateClientQuery = injectMutation(() => ({
    mutationKey: ['updateClient', this.authService.userId],
    mutationFn: (clientData: UpdateClient) =>
      this.clientService.updateClient(clientData),
    onSuccess: (data) => {
      this.clientService.getAllClientsQuery().refetch();
      this.dialogRef.close({
        type: 'success',
        severity: 'success',
        message: 'Client updated successfully!',
        summary: 'Client Updated.',
      });
    },
    onError: (error) => {
      console.error('Error updating client:', error);
      const errorMessages = Array.isArray(error.message)
        ? error.message
        : [error.message];
      errorMessages.forEach((msg: string) => {
        this.dialogRef.close({
          type: 'error',
          severity: 'error',
          message: msg,
          summary: 'Failed to update the client',
        });
      });
    },
  }));

  countryCodeAndPhoneValidator(): ValidatorFn {
    return (group: AbstractControl): ValidationErrors | null => {
      const phone = group.get('contactPhoneNumber');
      const code = group.get('countryCode');

      const hasPhone = !!phone?.value?.trim();
      const hasCode = !!code?.value;

      if ((hasPhone && !hasCode) || (!hasPhone && hasCode)) {
        phone?.setErrors({ ...(phone.errors || {}), required: true });
        code?.setErrors({ ...(code.errors || {}), required: true });
      } else {
        if (phone?.hasError('required')) {
          const { required, ...others } = phone.errors || {};
          phone.setErrors(Object.keys(others).length ? others : null);
        }
        if (code?.hasError('required')) {
          const { required, ...others } = code.errors || {};
          code.setErrors(Object.keys(others).length ? others : null);
        }
      }

      return null;
    };
  }

  onSubmit() {
    if (this.clientFormGroup.invalid) {
      this.clientFormGroup.markAllAsTouched(); // Marking fields as touched to show validation errors
      return;
    }

    this.isSubmitting = true;

    const formData: ClientFormData = this.clientFormGroup.value;
    const clientDataById: ClientResponse = this.dialogConfig.data.clientData;

    const fullPhoneNumber = `${
      formData.countryCode?.value === undefined
        ? ''
        : formData.countryCode.value
    }${formData.contactPhoneNumber?.trim()}`;

    const clientData: CreateClient = {
      name: formData.clientName.trim(),
      contactName: formData.contactName.trim(),
      contactPhoneNumber: fullPhoneNumber.trim(),
      contactEmail: formData.contactEmail.trim(),
      country: formData.country.label,
      address: formData.address.trim(),
      zipcode: formData.zipcode.trim(),
      currency: formData.currency.label,
      status: false,
    };
    if (this.isEditMode) {
      const updateClientData: UpdateClient = {
        clientId: clientDataById.id,
        ...clientData,
      };
      this.updateClientQuery.mutate(updateClientData);
    } else {
      this.createClientQuery.mutate(clientData);
    }
    this.clientFormGroup.disable();
  }

  checkErrors(fieldName: keyof ClientFormData): string | null {
    const control = this.clientFormGroup.get(fieldName);
    const phoneControl = this.clientFormGroup.get('contactPhoneNumber');
    const countryCodeControl = this.clientFormGroup.get('countryCode');

    const errorMessage: ErrorMessage<ClientFormData> = {
      clientName: {
        minlength: 'Client name is too short.',
        maxlength: 'Client name is too long.',
        pattern: 'Client name cannot contain only space.',
      },
      contactName: {
        minlength: 'Contact name is too short.',
        maxlength: 'Contact name is too long.',
        pattern: 'Contact name cannot contain only space.',
      },
      contactEmail: {
        email: 'Enter a valid email address.',
      },
      contactPhoneNumber: {
        pattern: 'Enter a valid contact number for the selected country.',
        required: 'Phone and code must both be filled or both be empty.',
      },
      zipcode: {
        pattern: 'Enter valid zip code.',
      },
      address: {
        pattern: 'Address cannot contain only space.',
      },
    };

    if (fieldName === 'contactPhoneNumber') {
      const phoneTouched = phoneControl?.touched || phoneControl?.dirty;
      const countryTouched = countryCodeControl?.touched;

      if (
        (phoneControl?.value && !countryCodeControl?.value) ||
        (!phoneControl?.value && countryCodeControl?.value)
      ) {
        if (phoneTouched || countryTouched) {
          if (phoneControl?.value) {
            return 'Country code is required';
          } else {
            return 'Contact number is required';
          }
        }
      }

      if (phoneControl?.invalid && phoneTouched && phoneControl.errors) {
        for (const key of Object.keys(phoneControl.errors)) {
          const message =
            errorMessage.contactPhoneNumber?.[
              key as keyof typeof errorMessage.contactPhoneNumber
            ];
          if (message) return message;
        }
      }

      return null;
    }
    if (
      control?.invalid &&
      (control.touched || control.dirty) &&
      control.errors
    ) {
      for (const key of Object.keys(control.errors)) {
        const message =
          errorMessage[fieldName]?.[
            key as keyof (typeof errorMessage)[typeof fieldName]
          ];
        if (message) return message;
      }
    }

    return null;
  }

  get isSubmitButtonDisabled() {
    return this.clientFormGroup.invalid || this.isSubmitting;
  }
}
