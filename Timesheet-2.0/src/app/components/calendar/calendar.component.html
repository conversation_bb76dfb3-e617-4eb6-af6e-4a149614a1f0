<!-- 
  Calendar component that renders a weekly view, displaying the days of the week in the first row, followed by all the days of 
  the month. It highlights the current day and differentiates days from the current month and others using custom styles.
-->
<div class="flex flex-col h-screen">
  <header
    class="flex justify-center flex-col md:flex-row md:justify-between items-center mb-4 gap-2"
  >
    <div
      class="flex flex-wrap justify-center items-center md:justify-start lg:justify-start gap-2"
    >
      <p-button
        label="Today"
        icon="pi pi-calendar"
        size="small"
        (onClick)="goToToday()"
        styleClass="rounded-md"
        ariaLabel="go today"
      ></p-button>
      <div class="gap-1">
        <p-button
          icon="pi pi-chevron-left"
          [text]="true"
          size="small"
          class="rounded-md"
          (onClick)="navigateMonth('prev')"
          ariaLabel="previous month"
        ></p-button>
        <p-button
          icon="pi pi-chevron-right"
          [text]="true"
          size="small"
          (onClick)="navigateMonth('next')"
          class="rounded-md"
          ariaLabel="next month"
        ></p-button>
      </div>
      <h1 class="text-2xl font-semibold text-center text-neutral-700">
        {{ currentDate() | date: 'MMMM yyyy' }}
      </h1>
    </div>
    <ng-container *ngTemplateOutlet="calendarHeaderTemplate()"></ng-container>
  </header>

  <div class="flex-grow overflow-hidden rounded-lg">
    <div class="grid grid-cols-7 bg-gray-300">
      @for (day of allDays.slice(0, 7); track $index) {
        <div class="bg-white p-3 border border-neutral-100">
          <div class="font-medium text-sm text-neutral-500 text-center">
            {{ day | date: 'EEE' }}
          </div>
        </div>
      }
    </div>
    <div
      class="grid grid-cols-7 bg-gray-300 overflow-y-auto calendar-grid md:h-[calc(100vh-10rem)]"
    >
      @for (day of allDays; track $index) {
        @if (isSameMonth(day, currentDate())) {
          <div
            class="relative bg-white px-2 py-3 lg:min-h-[140px] transition-colors border border-neutral-100 focus:outline-neutral-300"
            [ngClass]="{
              'cursor-not-allowed pointer-events-none ': isFuture(day),
            }"
            [attr.aria-disabled]="isFuture(day) ? 'true' : null"
            (click)="!isFuture(day) && onCalendarCellClick()(day)"
            (keypress)="!isFuture(day) && onCalendarCellClick()(day)"
            tabindex="{{ isFuture(day) ? -1 : 0 }}"
          >
            <div
              class="flex md:justify-between justify-center items-center flex-wrap"
            >
              <time
                [attr.datetime]="day | date: 'yyyy-MM-dd'"
                class="flex h-6 w-6 items-center justify-center rounded-full text-sm p-4 font-semibold leading-none text-center text-neutral-700 mb-1"
                [ngClass]="{
                  'bg-primary-500 text-white ': isToday(day),
                  'opacity-30': isFuture(day),
                }"
                >{{ day | date: 'd' }}</time
              >
              <div class="flex gap-1 items-center justify-center">
                <ng-container
                  *ngTemplateOutlet="
                    cellHeaderTemplate();
                    context: { $implicit: day }
                  "
                ></ng-container>
              </div>
            </div>
            <ng-container
              *ngTemplateOutlet="
                cellContentTemplate();
                context: { $implicit: day }
              "
            ></ng-container>
          </div>
        } @else {
          <div
            class="bg-white border border-neutral-100 lg:min-h-[140px]"
          ></div>
        }
      }
    </div>
  </div>
</div>
