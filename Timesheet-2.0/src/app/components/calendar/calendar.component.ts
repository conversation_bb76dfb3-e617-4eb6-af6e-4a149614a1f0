import { CommonModule } from '@angular/common';
import {
  Component,
  input,
  OnInit,
  output,
  signal,
  TemplateRef,
} from '@angular/core';
import {
  addDays,
  endOfMonth,
  endOfWeek,
  isFuture,
  isSameMonth,
  isToday,
  startOfMonth,
  startOfWeek,
} from 'date-fns';
import { ButtonModule } from 'primeng/button';

/*
 * It also involves navigation between different months
 */
@Component({
  selector: 'tms-calendar',
  standalone: true,
  imports: [CommonModule, ButtonModule],
  templateUrl: './calendar.component.html',
  styleUrl: './calendar.component.css',
})
export class CalendarComponent implements OnInit {
  cellContentTemplate = input<TemplateRef<any> | null>(null);
  cellHeaderTemplate = input<TemplateRef<any> | null>(null);
  calendarHeaderTemplate = input<TemplateRef<any> | null>(null);
  onCalendarCellClick = input<(date: Date) => void>(() => () => {});
  onDateChange = output<Date>();

  /**
   * The date currently selected or viewed in the calendar.
   * Defaults to the current system date.
   */
  currentDate = signal<Date>(new Date());

  /**
   * The list of days to be displayed in the calendar grid.
   * This should include all days in the current view, including
   * those from previous and next months to fill out the grid.
   */
  allDays: Date[] = [];

  ngOnInit(): void {
    this.onDateChange.emit(this.currentDate());
    this.generateCalendarDays();
  }

  /**
   * Generates all the days of a particular month based on the
   * current date such that the month starts from monday and ends
   * on sunday
   */
  generateCalendarDays = () => {
    // Get the first and last day of the current month
    const firstDayOfMonth = startOfMonth(this.currentDate());
    const lastDayOfMonth = endOfMonth(this.currentDate());

    // Get the start and end dates of the week surrounding the month
    const startDate = startOfWeek(firstDayOfMonth, { weekStartsOn: 1 });
    const endDate = endOfWeek(lastDayOfMonth, { weekStartsOn: 1 });

    // Generate all the days between the start and end dates
    this.allDays = this.getDatesBetween(startDate, endDate);
  };

  /**
   * Returns all the dates from the start of the month and
   * till the end of that month
   */
  getDatesBetween = (start: Date, end: Date): Date[] => {
    const dates: Date[] = [];
    let currentDate = new Date(start);
    while (currentDate <= end) {
      dates.push(new Date(currentDate));
      currentDate = addDays(currentDate, 1);
    }
    return dates;
  };

  /**
   * Navigates to the next month or the previous month
   */
  navigateMonth = (direction: 'prev' | 'next') => {
    this.currentDate.set(
      new Date(
        this.currentDate().getFullYear(),
        this.currentDate().getMonth() + (direction === 'next' ? 1 : -1)
      )
    );
    this.generateCalendarDays();
    this.onDateChange.emit(this.currentDate());
  };

  /**
   * Returns to the current Date
   */
  goToToday() {
    this.currentDate.set(new Date());
    this.generateCalendarDays();
    this.onDateChange.emit(this.currentDate());
  }

  isSameMonth = isSameMonth;
  isToday = isToday;
  isFuture = isFuture;
}
