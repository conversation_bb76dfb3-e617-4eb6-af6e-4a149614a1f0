import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ComponentRef } from '@angular/core';
import { By } from '@angular/platform-browser';
import { CalendarComponent } from './calendar.component';
import { provideHttpClient, withFetch } from '@angular/common/http';
import {
  QueryClient,
  provideTanStackQuery,
} from '@tanstack/angular-query-experimental';

/**
 * Test suite for Calendar Component
 */

describe('CalendarComponent', () => {
  let component: CalendarComponent;
  let fixture: ComponentFixture<CalendarComponent>;
  let componentRef: ComponentRef<CalendarComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [CalendarComponent],
      providers: [
        provideHttpClient(withFetch()),
        provideTanStackQuery(new QueryClient()),
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(CalendarComponent);
    component = fixture.componentInstance;
    componentRef = fixture.componentRef;
    fixture.detectChanges();
  });

  // Test case: Verifies that the component is created successfully.
  it('should create', () => {
    expect(component).toBeTruthy();
  });

  // Test case: Verifies that the current date is highlighted correctly with specific styles.
  it('should highlight the current date with a blue circular background and white text', () => {
    const today = new Date();

    component.allDays = [today];
    fixture.detectChanges();

    const currentDateElement = fixture.debugElement.query(
      By.css('.bg-primary-500.text-white')
    );

    expect(currentDateElement).toBeTruthy();

    expect(currentDateElement.nativeElement.textContent.trim()).toBe(
      today.getDate().toString()
    );
  });

  // Test case: Verifies the functionality of 'isToday' method.
  it('should correctly identify today using isToday method', () => {
    const today = new Date();
    expect(component.isToday(today)).toBeTrue();

    const notToday = new Date(
      today.getFullYear(),
      today.getMonth(),
      today.getDate() - 1
    );
    expect(component.isToday(notToday)).toBeFalse();
  });

  // Test case: Verifies the functionality of 'isSameMonth' method.
  it('should correctly identify dates from the same month using isSameMonth method', () => {
    const date1 = new Date(2024, 10, 1);
    const date2 = new Date(2024, 10, 15);
    const differentMonthDate = new Date(2024, 9, 30);

    expect(component.isSameMonth(date1, date2)).toBeTrue();
    expect(component.isSameMonth(date1, differentMonthDate)).toBeFalse();
  });

  // Test case: Verifies the generation of the calendar days for the month
  it('should generate calendar days for the current month on initialization', () => {
    const currentMonth = component.currentDate().getMonth();
    const currentYear = component.currentDate().getFullYear();
    // First day should be within the previous week's range
    const firstDay = component.allDays[0];
    const lastDay = component.allDays[component.allDays.length - 1];

    expect(firstDay.getDay()).toEqual(1);
    expect(lastDay.getDay()).toEqual(0);

    // Validate first and last days' month range
    expect(
      firstDay.getMonth() === currentMonth ||
        firstDay.getMonth() === (currentMonth - 1 + 12) % 12
    ).toBeTrue();

    expect(
      lastDay.getMonth() === currentMonth ||
        lastDay.getMonth() === (currentMonth + 1) % 12
    ).toBeTrue();
  });

  // Test case: Verifies the navigation to the previous month
  it('should navigate to the previous month when navigateMonth is called with "prev"', () => {
    const initialDate = component.currentDate();
    const initialMonth = initialDate.getMonth();
    const initialYear = initialDate.getFullYear();

    component.navigateMonth('prev');
    fixture.detectChanges();

    const updatedDate = component.currentDate();
    const expectedMonth = initialMonth === 0 ? 11 : initialMonth - 1; // Wrap to December if January
    const expectedYear = initialMonth === 0 ? initialYear - 1 : initialYear; // Adjust year if wrapped

    expect(updatedDate.getMonth()).toBe(expectedMonth);
    expect(updatedDate.getFullYear()).toBe(expectedYear);
  });

  // Test case: Verifies the navigation to the next month
  it('should navigate to the next month when navigateMonth is called with "next"', () => {
    const initialDate = component.currentDate();
    const initialMonth = initialDate.getMonth();
    const initialYear = initialDate.getFullYear();

    component.navigateMonth('next');
    fixture.detectChanges();

    const updatedDate = component.currentDate();
    const expectedMonth = initialMonth === 11 ? 0 : initialMonth + 1; // Wrap to January if December
    const expectedYear = initialMonth === 11 ? initialYear + 1 : initialYear; // Adjust year if wrapped

    expect(updatedDate.getMonth()).toBe(expectedMonth);
    expect(updatedDate.getFullYear()).toBe(expectedYear);
  });

  // Test case: Verifies the navigation to today
  it('should go to today when goToToday is called', () => {
    const currentDate = new Date();
    component.navigateMonth('prev');
    fixture.detectChanges();

    component.goToToday();
    fixture.detectChanges();

    expect(component.currentDate().toDateString()).toEqual(
      currentDate.toDateString()
    );
  });

  // Test case: Verifies the navigation across different years
  it('should correctly handle edge cases when navigating across years', () => {
    component.currentDate.set(new Date(2024, 0, 1)); // January 2024
    component.navigateMonth('prev');
    fixture.detectChanges();

    expect(component.currentDate().getFullYear()).toBe(2023);
    expect(component.currentDate().getMonth()).toBe(11);

    component.navigateMonth('next');
    component.navigateMonth('next');
    fixture.detectChanges();

    expect(component.currentDate().getFullYear()).toBe(2024);
    expect(component.currentDate().getMonth()).toBe(1);
  });

  // Test case: Verifies the display of correct header for the current month and year
  it('should display the correct header for the current month and year', () => {
    const compiled = fixture.nativeElement as HTMLElement;
    const header = compiled.querySelector('h1')?.textContent?.trim();
    const currentMonthYear = component.currentDate().toLocaleString('default', {
      month: 'long',
      year: 'numeric',
    });
    expect(header).toBe(currentMonthYear);
  });

  // Test case: Verifies that the days are updated when navigated across different months
  it('should correctly update allDays when navigateMonth is called', () => {
    const initialDays = [...component.allDays];
    component.navigateMonth('next');
    fixture.detectChanges();
    expect(component.allDays).not.toEqual(initialDays);
  });

  it('should handle empty calendar states gracefully', () => {
    component.allDays = [];
    fixture.detectChanges();

    expect(component.allDays.length).toBe(0);
  });
});
