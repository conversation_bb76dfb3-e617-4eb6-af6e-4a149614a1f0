import { Component, signal, computed, inject, input, ChangeDetectionStrategy } from '@angular/core';
import { ClientReportContract } from '../../services/client/client.model';
import { DatePipe } from '@angular/common';
import { convertMinutesToHours } from '../../utils/date.utils';
import { DateTableComponent } from '../date-table/date-table.component';
import { TableModule } from 'primeng/table';
import { TooltipModule } from 'primeng/tooltip';
import { SkeletonModule } from 'primeng/skeleton';
import { ContractService } from '../../services/contract/contract.service';

interface ContractResourceColumn {
  field: string;
  header: string;
}

@Component({
  selector: 'tms-contract-resources-worklogs',
  imports: [DatePipe, DateTableComponent, TableModule, TooltipModule, SkeletonModule],
  templateUrl: './contract-resources-worklogs.component.html',
  styleUrls: ['./contract-resources-worklogs.component.css'],
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ContractResourcesWorklogsComponent {
  contract = input.required<ClientReportContract>();
  cappedHours = input<boolean>(false);
  kekaId = input<boolean>(false);
  currentDate = input<Date>(new Date()); // input to receive date from parent

  pageSize = 6;
  private contractService = inject(ContractService);

  columnHeader = computed<ContractResourceColumn[]>(() => {
    if (!this.contract()?.resourceList?.length) return [];
    const startDate = this.currentDate(); // Use currentDate input instead of local date signal
    const year = startDate.getFullYear();
    const month = startDate.getMonth();
    const daysInMonth = new Date(year, month + 1, 0).getDate();
    const cols: ContractResourceColumn[] = [
      { field: 'name', header: 'Resource Name' },
    ];
    for (let i = 1; i <= daysInMonth; i++) {
      const day = new Date(year, month, i).toLocaleString('default', { weekday: 'short' });
      cols.push({ field: `day${i}`, header: `${i} ${day.charAt(0)}` });
    }
    cols.push({ field: 'totalHours', header: 'Total Hours' });
    return cols;
  });

  fetchResourceDetails = computed(() => {
    return this.contract()?.resourceList?.map(resource => ({
      ...resource,
      resourceName: resource.resourceName,
      kekaId: resource.resourceId,
      totalMinutes: resource.totalLoggedMinutes,
      resourceWorklog: resource.resourceWorklog ?? [],
      leaveDetails: resource.leaveDetails ?? [],
    })) ?? [];
  });

  convertMinutesToHours = convertMinutesToHours;

  getDateFromField(field: string): Date | null {
    if (!field.startsWith('day')) return null;
    const day = parseInt(field.replace('day', ''), 10);
    const base = this.currentDate(); // Use currentDate input instead of local date signal
    return new Date(base.getFullYear(), base.getMonth(), day);
  }

  skeletonRows = Array(3);

  get hasWorklogs(): boolean {
    return (this.contract()?.resourceList?.length ?? 0) > 0;
  }

  async downloadContractReport() {
    if (!this.contract()) return;
    const contractId = this.contract().id;
    const date = this.currentDate(); // Use currentDate input instead of local date signal
    const month = date.getMonth() + 1;
    const year = date.getFullYear();
    const mode = this.cappedHours() ? 'cappedHours' : 'actualHours';
    try {
      const response = await this.contractService.getContractReportDownloadUrl(
        contractId,
        month,
        year,
        mode,
        this.kekaId()
      );
      if (response && response.data && response.data.downloadUrl) {
        window.open(response.data.downloadUrl, '_blank');
      }
    } catch (error) {
      console.error('Failed to download contract report:', error);
    }
  }
}
