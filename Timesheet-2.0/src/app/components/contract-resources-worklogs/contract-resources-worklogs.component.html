<div
  class="flex items-center justify-between w-full p-4 rounded-lg bg-gray-50 border mt-2"
>
  <!-- Left: Contract Info -->
  <div class="flex flex-col">
    <span class="font-semibold text-lg">{{ contract().contractId }}</span>
    <div class="flex items-center gap-4 mt-1 text-gray-500 text-sm">
      <span
        >{{ contract().numberOfResource }}
        {{ contract().numberOfResource === 1 ? 'Resource' : 'Resources' }}</span
      >
      <span>|</span>
      <span>Since {{ contract().startDate | date: 'dd-MM-yyyy' }}</span>
    </div>
  </div>
  <!-- Right: Info and Download -->
  <div class="flex flex-col items-end gap-2">
    <div class="flex items-center gap-2">
      <i class="pi pi-info-circle text-blue-400"></i>
      <span class="text-gray-600 text-sm"
        >Only approved worklogs will be displayed</span
      >
    </div>
    <button
      pButton
      type="button"
      class="p-button p-button-sm p-button-outlined mt-1"
      [class.opacity-40]="!hasWorklogs"
      [class.cursor-not-allowed]="!hasWorklogs"
      [disabled]="!hasWorklogs"
      (click)="downloadContractReport()"
    >
      Download
    </button>
  </div>
</div>

<div class="w-full bg-white overflow-hidden shadow rounded-xl mt-4">
  <p-table
    #reportSheetTable
    [columns]="columnHeader()"
    [value]="fetchResourceDetails()"
    [tableStyle]="{ 'min-width': '50rem' }"
    [showLoader]="false"
    [paginator]="fetchResourceDetails().length > pageSize"
    [rows]="pageSize"
  >
    <ng-template pTemplate="header">
      <tr class="bg-transparent">
        @for (col of columnHeader(); track col.field) {
          <th class="p-1.5 text-center text-sm bg-primary-50">
            @if (col.field === 'name') {
              <div class="flex gap-2 text-start">
                {{ col.header }}
              </div>
            } @else {
              <div class="flex flex-col">
                <span>{{ col.header.split(' ')[0] }}</span>
                <span>{{ col.header.split(' ')[1] }}</span>
              </div>
            }
          </th>
        }
      </tr>
    </ng-template>

    <ng-template pTemplate="body" let-resource>
      <tr class="hover:bg-gray-100">
        @for (col of columnHeader(); track col.field) {
          <td class="text-center py-2 px-0">
            @if (col.field === 'name') {
              <div class="flex items-center gap-2 text-sm text-start ml-1.5">
                <span
                  #resourceName
                  class="truncate max-w-32"
                  [pTooltip]="
                    resourceName.offsetWidth < resourceName.scrollWidth
                      ? resource.resourceName
                      : ''
                  "
                  [tooltipStyleClass]="
                    'text-xs text-gray-700  whitespace-pre-wrap'
                  "
                  tooltipPosition="top"
                  >{{ resource.resourceName || '-' }}</span
                >
              </div>
              <div class="text-gray-500 text-xs text-start ml-1.5 italic">
                {{ resource.designation || '-' }}
              </div>
            } @else if (col.field === 'id') {
              <div class="text-gray-700 text-sm">
                {{ resource.kekaId || '-' }}
              </div>
            } @else if (col.field === 'totalHours') {
              <div class="text-sm font-semibold">
                {{ convertMinutesToHours(resource.totalMinutes) || '-' }}
              </div>
            } @else {
              <tms-date-table
                [worklogs]="resource.resourceWorklog"
                [leaveDetails]="resource.leaveDetails"
                [worklogDate]="getDateFromField(col.field)"
              ></tms-date-table>
            }
          </td>
        }
      </tr>
    </ng-template>

    <ng-template pTemplate="emptymessage">
      <tr>
        <td [attr.colspan]="columnHeader().length" class="text-center p-4">
          No worklogs found.
        </td>
      </tr>
    </ng-template>
    <ng-template pTemplate="loadingbody">
      <tr>
        <td
          [attr.colspan]="columnHeader().length"
          class="text-center text-gray-500 p-4"
        >
          <i class="pi pi-spin pi-spinner mr-2"></i> Loading worklogs...
        </td>
      </tr>
      @for (_ of skeletonRows; track $index) {
        <tr>
          @for (col of columnHeader(); track $index) {
            <td class="text-sm">
              <p-skeleton width="100%" height="1.5rem"></p-skeleton>
            </td>
          }
        </tr>
      }
    </ng-template>
  </p-table>
</div>
