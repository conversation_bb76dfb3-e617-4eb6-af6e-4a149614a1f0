@if (!worklogs().length && effortsMissing()) {
  <div
    class="flex items-center justify-center truncate text-xs hover:text-white cursor-pointer rounded-lg p-1.5 md:py-1 md:px-2 first:mb-1.5 transition-colors gap-2 bg-red-100 text-red-600 hover:bg-red-500"
  >
    <span class="text-center font-semibold">Efforts are missing </span>
  </div>
} @else {
  @for (worklog of worklogs().slice(0, WORKLOG_DISPLAY_LIMIT); track $index) {
    <tms-worklog-entry [worklog]="worklog" />
  }
}

@if (worklogs().length > WORKLOG_DISPLAY_LIMIT) {
  <p-button
    (onClick)="op.toggle($event); $event.stopPropagation()"
    link="true"
    [label]="buttonLabel"
    class="show-more-btn"
    [attr.aria-label]="'Show more'"
  />

  <p-overlayPanel #op>
    @for (worklog of worklogs().slice(WORKLOG_DISPLAY_LIMIT); track $index) {
      <div class="py-1 px-0.5 pb-0 rounded-lg">
        <tms-worklog-entry [worklog]="worklog" />
      </div>
    }
  </p-overlayPanel>
}
