import { Component } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { ChipModule } from 'primeng/chip';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { Worklog } from '../../services/worklog/worklog.model';
import { TagComponent } from '../tag/tag.component';
import { getWorklogChipDetails } from '../../utils/chip.utils';

@Component({
  selector: 'tms-worklog-display',
  standalone: true,
  imports: [ChipModule, ButtonModule, TagComponent],
  template: `<div class="w-full flex flex-col justify-between items-start">
    <div
      class="w-full flex flex-row-reverse items-center justify-between gap-2"
    >
      <p-button
        (click)="onClose()"
        styleClass="text-gray-600 hover:text-gray-800 mx-3"
        icon="pi pi-times"
        [text]="true"
        [rounded]="true"
      >
      </p-button>
      @if (worklog.workLogStatus) {
        <tms-tag
          [label]="getWorklogChipDetails(worklog)?.chipText ?? ''"
          [styles]="getWorklogChipDetails(worklog)?.chipClass ?? ''"
          class="text-xs"
        />
      }
    </div>
    <h1 class="text-xl font-semibold">Edit Worklog</h1>
  </div> `,
})
export class EditDialogHeader {
  worklog: Worklog;
  getWorklogChipDetails = getWorklogChipDetails;

  constructor(
    private dialogRef: DynamicDialogRef,
    private dialogConfig: DynamicDialogConfig
  ) {
    this.worklog = this.dialogConfig?.data?.worklog;
  }

  onClose() {
    this.dialogRef.close();
  }
}
