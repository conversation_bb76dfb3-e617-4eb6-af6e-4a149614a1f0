import { Component, input, ViewEncapsulation } from '@angular/core';
import { MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { InputGroupModule } from 'primeng/inputgroup';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { Worklog } from '../../services/worklog/worklog.model';
import { formatHoursAndMinutes } from '../../utils/date.utils';
import { WorklogEntryComponent } from '../worklog-entry/worklog-entry.component';
import { WORKLOG_DISPLAY_LIMIT } from '../../settings';

@Component({
  selector: 'tms-worklog-display',
  standalone: true,
  imports: [
    OverlayPanelModule,
    InputGroupModule,
    ButtonModule,
    WorklogEntryComponent,
  ],
  providers: [DynamicDialogRef, DialogService],
  templateUrl: './worklog-display.component.html',
  styleUrls: ['./worklog-display.component.css'],
  encapsulation: ViewEncapsulation.None,
})
export class WorklogDisplayComponent {
  readonly WORKLOG_DISPLAY_LIMIT = WORKLOG_DISPLAY_LIMIT;
  worklogs = input.required<Worklog[]>();
  effortsMissing = input.required<boolean>();
  formatHoursAndMinutes = formatHoursAndMinutes;

  get buttonLabel(): string {
    return `+${this.worklogs().length - this.WORKLOG_DISPLAY_LIMIT} more`;
  }
}
