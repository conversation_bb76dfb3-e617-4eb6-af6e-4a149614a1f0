import { ComponentFixture, TestBed } from '@angular/core/testing';

import { WorklogDisplayComponent } from './worklog-display.component';
import { Worklog } from '../../services/worklog/worklog.model';
import { MessageService } from 'primeng/api';
import { DialogService } from 'primeng/dynamicdialog';
import { ToastModule } from 'primeng/toast';

let worklogs: Worklog[] = [];

describe('WorklogDisplayComponent', () => {
  let component: WorklogDisplayComponent;
  let fixture: ComponentFixture<WorklogDisplayComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [WorklogDisplayComponent, ToastModule],
      providers: [MessageService, DialogService],
    }).compileComponents();

    fixture = TestBed.createComponent(WorklogDisplayComponent);
    component = fixture.componentInstance;
    fixture.componentRef.setInput('worklogs', []);
    fixture.componentRef.setInput('effortsMissing', false);
    fixture.detectChanges();
    worklogs = [
      {
        id: '1',
        workDate: new Date('2024-12-10T08:00:00Z'),
        startDate: new Date('2024-12-10T09:00:00Z'),
        endDate: new Date('2024-12-10T17:00:00Z'),
        clientId: '123',
        projectId: '456',
        employeeId: '789',
        managerId: '987',
        taskId: '101112',
        description: 'Completed module implementation and bug fixes.',
        minutes: 480,
        createdAt: new Date('2024-12-10T07:00:00Z'),
        updatedAt: new Date('2024-12-10T18:00:00Z'),
        deleted: false,
        isOnLeave: false,
        isOnFirstHalfLeave: false,
        isOnSecondHalfLeave: false,
        isWeekOff: false,
        isCompanyOff: false,
        employeeResource: {
          name: 'John Doe',
        },
        project: {
          startDate: new Date('2023-01-01T00:00:00Z'),
          endDate: new Date('2023-12-31T00:00:00Z'),
          projectName: 'Mobile App Development',
          billable: true,
          projectStatus: 'active',
        },
        client: {
          name: 'Acme Corp',
          status: true,
        },
        workLogStatus: {
          status: 'approved',
          remarks: 'Good progress made on the project.',
        },
        task: {
          taskName: 'UI Design',
          contract: {
            customContractId: 'cont-001',
          },
        },
      },
      {
        id: '2',
        workDate: new Date('2024-12-11T08:00:00Z'),
        startDate: new Date('2024-12-11T09:00:00Z'),
        endDate: new Date('2024-12-11T16:00:00Z'),
        clientId: '124',
        projectId: '457',
        employeeId: '790',
        managerId: '988',
        taskId: '101113',
        description: 'Refactored API endpoints to improve performance.',
        minutes: 420,
        createdAt: new Date('2024-12-11T07:00:00Z'),
        updatedAt: new Date('2024-12-11T16:30:00Z'),
        deleted: false,
        isOnLeave: false,
        isOnFirstHalfLeave: false,
        isOnSecondHalfLeave: false,
        isWeekOff: false,
        isCompanyOff: false,
        employeeResource: {
          name: 'Jane Smith',
        },
        project: {
          startDate: new Date('2023-01-01T00:00:00Z'),
          endDate: new Date('2023-12-31T00:00:00Z'),
          projectName: 'API Integration',
          billable: false,
          projectStatus: 'completed',
        },
        client: {
          name: 'Tech Solutions',
          status: false,
        },
        workLogStatus: {
          status: 'submitted',
          remarks: 'Awaiting manager review.',
        },
        task: {
          taskName: 'API Optimization',
          contract: {
            customContractId: 'cont-002',
          },
        },
      },
      {
        id: '3',
        workDate: new Date('2024-12-12T08:00:00Z'),
        startDate: new Date('2024-12-12T10:00:00Z'),
        endDate: new Date('2024-12-12T15:00:00Z'),
        clientId: '125',
        projectId: '458',
        employeeId: '791',
        managerId: null,
        taskId: '101114',
        description: 'Prepared project documentation and user manuals.',
        minutes: 300,
        createdAt: new Date('2024-12-12T09:00:00Z'),
        updatedAt: new Date('2024-12-12T15:30:00Z'),
        deleted: false,
        isOnLeave: false,
        isOnFirstHalfLeave: false,
        isOnSecondHalfLeave: false,
        isWeekOff: false,
        isCompanyOff: false,
        employeeResource: {
          name: 'Alice Johnson',
        },

        project: {
          startDate: new Date('2023-01-01T00:00:00Z'),
          endDate: new Date('2023-12-31T00:00:00Z'),
          projectName: 'User Research',
          billable: true,
          projectStatus: 'in-progress',
        },
        client: {
          name: 'Innovate Inc.',
          status: true,
        },
        workLogStatus: {
          status: 'rejected',
          remarks: 'Documentation ready for client review.',
        },
        task: {
          taskName: 'Documentation',
          contract: {
            customContractId: 'cont-003',
          },
        },
      },
    ];
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it("Lenght of the component's project should be equal to assigned project lenght", () => {
    fixture.componentRef.setInput('worklogs', worklogs);
    expect(component.worklogs().length).toBe(worklogs.length);
  });

  it('formatHoursAndMinutes should give formatted date and hours', () => {
    expect(component.formatHoursAndMinutes(480)).toBe('08:00');
    expect(component.formatHoursAndMinutes(100)).toBe('01:40');
    expect(component.formatHoursAndMinutes(300)).toBe('05:00');
    expect(component.formatHoursAndMinutes(240)).toBe('04:00');
  });
});
