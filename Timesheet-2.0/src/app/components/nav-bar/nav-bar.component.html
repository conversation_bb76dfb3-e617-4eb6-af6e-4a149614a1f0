<!--
  Main Navigation Bar (NavBarComponent)
  This navigation bar consists of two sections: 
  - Left section: Back button for navigating to the previous page.
  - Right section: Notification icon and user profile dropdown menu.

  The component also includes a dynamic notification badge and a toggleable profile menu.
-->

<div class="nav-bar flex justify-between px-4 py-2 shadow-md bg-white rounded-md">
  <!-- Left Section: Back Button -->
  <div class="nav-bar-left flex items-center">
    <!-- The back button uses an arrow icon and triggers the goBack method when clicked -->
    <p-button icon="pi pi-bars" (onClick)="toggleSidebar()()"
      styleClass="py-2 hover:bg-gray-100 text-neutral-600  sidebar-button" [rounded]="true" [text]="true"
      size="large" />
  </div>
  <!-- Right Section: Notification and Profile -->
  <div class="navbar-right flex space-x-2 items-center">
    <!-- Notification Icon with Badge -->
    <p-button icon="pi pi-bell" rounded="true" text="true" class="notification-button"
      styleClass="text-gray-600 hover:bg-gray-100 bg-gray-100"
      badgeClass=" bg-red-500 top-0.5 right-1 absolute text-white" [badge]="badge()"></p-button>
    <!-- Profile Dropdown -->
    <p-menu [model]="menuItems" [popup]="true" #menu>
      <ng-template pTemplate="start">
        <div class="p-4 flex items-center space-x-4 border-b">
          <p-avatar icon="pi pi-user" shape="circle" styleClass="bg-primary-500 text-white size-10"></p-avatar>
          <div class="overflow-hidden">
            <h4 class="text-lg font-bold">{{ userDetails()?.name }}</h4>   
            <p class="text-sm text-gray-500 truncate">{{userDetails()?.email}}</p>
            <!-- <span class="text-xs text-gray-400">Additional Info</span> -->
          </div>
        </div>
      </ng-template>
    </p-menu>
    <!-- Profile icon that toggles the menu dropdown -->
    <div (mouseup)="menu.toggle($event)" class="cursor-pointer">
      <p-avatar icon=" pi pi-user" shape="circle" styleClass="bg-primary-500 text-white size-10"></p-avatar>
    </div>
  </div>
</div>