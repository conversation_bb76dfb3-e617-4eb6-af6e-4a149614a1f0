import {
  Component,
  computed,
  input,
  Input,
  ViewEncapsulation,
} from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { CommonModule, Location } from '@angular/common';
import { MenuModule } from 'primeng/menu';
import { Router } from '@angular/router';
import { MenuItem } from 'primeng/api';
import { AuthService } from '../../services/auth/auth-service';
import { AvatarModule } from 'primeng/avatar';
import { AvatarGroupModule } from 'primeng/avatargroup';
import { ResourceService } from '../../services/profile/profile.service';

/**
 * NavBarComponent
 *
 * The navigation bar for the application, providing essential navigation actions such as:
 * - Back button for navigating to the previous page.
 * - Notification icon with a dynamic badge.
 * - Profile icon with a dropdown menu containing Profile and Logout options.
 *
 * This component is used to enhance the user experience with quick access to navigation actions.
 *
 **/

@Component({
  selector: 'tms-nav-bar',
  standalone: true,
  imports: [
    ButtonModule,
    MenuModule,
    CommonModule,
    AvatarModule,
    AvatarGroupModule,
  ],
  templateUrl: './nav-bar.component.html',
  styleUrl: './nav-bar.component.css',
  encapsulation: ViewEncapsulation.None,
})
export class NavBarComponent {
  /* Function from sidebar component for toggling sidebar visibility */
  toggleSidebar = input(() => {});

  badge = input<string>(''); // Badge for notification icon

  menuItems: MenuItem[] | undefined = [
    {
      label: 'Profile', 
      icon: 'pi pi-user',
      command: () => this.navigateToProfile(),
    },
    {
      separator: true,
    },
    { label: 'Logout', icon: 'pi pi-sign-out', command: () => this.logout() },
  ];

  constructor(
    private authService: AuthService,
    private router: Router,
    private resourceService: ResourceService
  ) {}

  userDetails = computed(() =>
    this.resourceService.ResourceDetailsQuery.data()
  );

  /**
   * Placeholder function for navigating to the profile page.
   * To be implemented with actual navigation logic.
   */
  navigateToProfile() {
    // TODO: Implement actual navigation logic for profile route
    this.router.navigate(['/dashboard/profile']);
  }

  /**
   * Placeholder function for logging out the user.
   * Logout will clear the cookies and will redirect to the login page
   */
  logout() {
    this.authService.logOut().subscribe(() => {
      this.router.navigate(['/login']); // Redirect to the login page after logout
    });
  }
}
