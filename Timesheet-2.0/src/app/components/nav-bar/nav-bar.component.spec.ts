import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Location } from '@angular/common';
import { NavBarComponent } from './nav-bar.component';
import { By } from '@angular/platform-browser';
import { ButtonModule } from 'primeng/button';
import { MenuModule } from 'primeng/menu';
import { HttpClientModule } from '@angular/common/http';
import { QueryClient } from '@tanstack/angular-query-experimental';

describe('NavBarComponent', () => {
  let component: NavBarComponent;
  let fixture: ComponentFixture<NavBarComponent>;
  let locationSpy: jasmine.SpyObj<Location>;

  // Setup the test environment and component before each test
  beforeEach(async () => {
    // Create a spy for the Location service to mock its behavior
    locationSpy = jasmine.createSpyObj('Location', ['back']);
    // Configure the TestBed for the component and its dependencies
    await TestBed.configureTestingModule({
      imports: [NavBarComponent, ButtonModule, MenuModule, HttpClientModule],
      providers: [{ provide: Location, useValue: locationSpy }, QueryClient],
    }).compileComponents();

    // Create the component instance and trigger change detection
    fixture = TestBed.createComponent(NavBarComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  // Test 1: Component creation
  it('should create', () => {
    expect(component).toBeTruthy();
  });

  // Test 2: Notification button visibility and badge display
  it('should display the notification icon with the correct badge', () => {
    const notificationButton = fixture.debugElement.query(
      By.css('.notification-button')
    );
    expect(notificationButton).toBeTruthy();
  });

  // Test 3: Back button visibility
  it('should display the sidebar button', () => {
    const sidebarButton = fixture.debugElement.query(By.css('.sidebar-button'));
    expect(sidebarButton).toBeTruthy();
  });

  // Test 4: Profile dropdown menu toggling
  it('should toggle the profile dropdown menu when the profile icon is clicked', () => {
    const profileButton = fixture.debugElement.query(
      By.css('.navbar-right .cursor-pointer')
    );
    const menu = fixture.debugElement.query(By.css('p-menu'));
    const menuInstance = menu.componentInstance;
    spyOn(menuInstance, 'toggle').and.callThrough();
    profileButton.triggerEventHandler('mouseup', {});
    expect(menuInstance.toggle).toHaveBeenCalled();
  });

  // Test 5: GoBack method call on back button click
  it('should call toggleSidebar when the sidebar button is clicked', () => {
    const toggleSidebarSpy = spyOn(
      component,
      'toggleSidebar'
    ).and.callThrough();
    const sidebarButton = fixture.debugElement.query(
      By.css('.nav-bar-left p-button')
    );
    sidebarButton.triggerEventHandler('onClick', {});
    expect(toggleSidebarSpy).toHaveBeenCalled();
  });
});
