import { CommonModule } from '@angular/common';
import { Component, computed, input } from '@angular/core';
import { ConfirmationService, Message, MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { SkeletonModule } from 'primeng/skeleton';
import { ToastModule } from 'primeng/toast';
import { TooltipModule } from 'primeng/tooltip';
import { ProjectDetailsById } from '../../services/project/project.model';
import { ProjectsService } from '../../services/project/project.service';
import { StringUtilsService } from '../../services/stringutils/stringutils.service';
import { TimelineHistoryViewComponent } from '../timeline-history-view/timeline-history-view.component';

@Component({
  selector: 'tms-project-info-card',
  standalone: true,
  imports: [
    TimelineHistoryViewComponent,
    ConfirmDialogModule,
    ToastModule,
    ButtonModule,
    TooltipModule,
    CommonModule,
    SkeletonModule,
  ],
  providers: [ConfirmationService, MessageService],
  templateUrl: './project-info-card.component.html',
})
export class ProjectInfoCardComponent {
  project = input<ProjectDetailsById>();

  projectId = computed(() => this.project()?.id || '');

  errorMessage: Message[] = [
    { severity: 'error', detail: 'Failed to fetch project history' },
  ];

  isProjectHistoryError = computed(() => this.projectHistoryQuery().isError());
  isProjectHistoryLoading = computed(() =>
    this.projectHistoryQuery().isLoading()
  );
  constructor(
    private projectsService: ProjectsService,
    public stringUtilsService: StringUtilsService
  ) {}

  /**
   * Query to fetch project history data based on the project ID.
   *
   * This query uses Angular Query to asynchronously fetch contract history
   * records and store them in the projectHistoryEvents signal.
   */
  projectHistoryQuery = computed(() =>
    this.projectsService.projectsHistoryQuery(this.projectId())
  );
}
