@if (project()) {
  <div class="flex justify-between">
    <div class="flex items-end gap-4 mb-8">
      <h1 class="text-4xl font-bold text-neutral-700">
        {{ project()?.projectName }}
      </h1>
    </div>
  </div>
  <div class="shadow p-6 rounded-xl bg-white">
    <div
      class="flex justify-between md:flex-row flex-col gap-2 w-full items-center"
    >
      <div class="flex flex-col gap-4">
        <div class="flex gap-10 items-center flex-wrap">
          <div class="flex gap-2 items-center text-gray-700">
            <i class="pi pi-calendar"></i>
            <span class="">{{ project()?.startDate | date: 'MMM d, y' }}</span>
            <span>&ndash;</span>
            <span class="">
              @if (project()?.endDate) {
                {{ project()?.endDate | date: 'MMM d, y' }}
              } @else {
                <span class="italic text-gray-400">Pending (based on contract)</span>
              }
            </span>
          </div>

          <div class="flex gap-2 items-center justify-center">
            <i class="text-gray-700 pi pi-user"></i>
            <span class="text-gray-700 truncate" [pTooltip]=""
              >Owner: {{ project()?.contractResource?.[0]?.name }}</span
            >
          </div>

          <div class="flex gap-2 items-center justify-center">
            <i class="text-gray-700 pi pi-phone"></i>

            <a
              href="tel:{{ project()?.contractResource?.[0]?.phoneNumber }}"
              class="text-primary-500 hover:underline"
              >{{ project()?.contractResource?.[0]?.phoneNumber }}</a
            >
          </div>
          <div class="flex gap-2 items-center justify-center">
            <i class="text-gray-700 pi pi-envelope"></i>
            <a
              [href]="'mailto:' + project()?.contractResource?.[0]?.email"
              class="text-primary-500 hover:underline"
              >{{ project()?.contractResource?.[0]?.email }}</a
            >
          </div>
        </div>
      </div>
      <tms-timeline-history-view
        timelineHeader="Project History"
        [projectId]="projectId()"
        [timelineHistoryEvents]="projectHistoryQuery().data() ?? []"
        [errorMessage]="errorMessage"
        [isLoading]="isProjectHistoryLoading()"
        [hasError]="isProjectHistoryError()"
      ></tms-timeline-history-view>
    </div>
    <p-toast></p-toast>
    <p-confirmDialog></p-confirmDialog>
    <hr class="my-4" />

    <span class="text-gray-700">{{ project()?.description }}</span>
  </div>
} @else {
  <!-- Loading skeleton -->
  <div class="shadow-md p-6 rounded-xl bg-white">
    <div class="flex gap-4 justify-between items-center md:items-start w-full">
      <div class="flex gap-2 w-full">
        <div class="flex gap-4 items-center">
          <p-skeleton class="h-6 w-16"></p-skeleton>
          <p-skeleton class="h-6 w-16"></p-skeleton>
          <p-skeleton class="h-6 w-16"></p-skeleton>
        </div>
      </div>
      <p-skeleton class="h-10 w-24"></p-skeleton>
    </div>

    <hr class="my-4" />
    <div class="flex gap-4">
      <p-skeleton class="h-4 w-24"></p-skeleton>
      <p-skeleton class="h-4 w-24"></p-skeleton>
      <p-skeleton class="h-4 w-24"></p-skeleton>
      <p-skeleton class="h-4 w-24"></p-skeleton>
    </div>
  </div>
}

