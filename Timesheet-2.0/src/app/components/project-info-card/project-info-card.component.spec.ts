import { provideHttpClient, withFetch } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import {
  QueryClient,
  provideTanStackQuery,
} from '@tanstack/angular-query-experimental';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ProjectsService } from '../../services/project/project.service';
import { ProjectInfoCardComponent } from './project-info-card.component';

const mockProject = {
  id: 'be9bf3c7-d0c5-4d88-a9b6-89e77caa1d7c',
  projectName: 'Time Sheet',
  contactName: 'Praveen',
  contactEmail: '<EMAIL>',
  contactPhoneNumber: '+919988776655',
  description: 'Time sheet description',
  startDate: '2024-12-27T07:12:53.125Z',
  endDate: '2024-12-27T07:12:53.125Z',
  billable: true,
  createdBy: 'be9bf3c7-d0c5-4d88-a9b6-89e77caa1d7c',
  clientId: 'be9bf3c7-d0c5-4d88-a9b6-89e77caa1d7c',
  projectStatus: 'active',
  createdAt: '2024-12-27T07:12:53.125Z',
  updatedAt: '2024-12-27T07:12:53.125Z',
  deleted: true,
  client: {
    name: 'CodeCraft',
  },
  contractResource: [
    {
      id: 'cfb8182a-ae05-4a7f-a396-66fe866fe3a6',
      name: 'Prasanna',
      role: 'manager',
    },
  ],
  resourcesList: [
    {
      name: 'Azhan',
      designation: 'Backend developer',
      profilePicUrl:
        'https:///profile/aa1b9531-6301-4041-bc17-94b9183990cc.png',
    },
  ],
  projectBudgetDetails: [
    {
      id: 'cfb8182a-ae05-4a7f-a396-66fe866fe3a6',
      billingSettings: 'fixed',
      amount: 100,
    },
  ],
};

describe('ProjectInfoCardComponent', () => {
  let component: ProjectInfoCardComponent;
  let fixture: ComponentFixture<ProjectInfoCardComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ProjectInfoCardComponent],
      providers: [
        provideHttpClient(withFetch()),
        provideTanStackQuery(new QueryClient()),
        ProjectsService,
        ConfirmationService,
        MessageService,
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ProjectInfoCardComponent);
    component = fixture.componentInstance;
    fixture.componentRef.setInput('project', mockProject);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('Project should be defined', () => {
    expect(component.project()).toBeTruthy();
  });
});
