import { Component, computed, OnDestroy, signal, OnInit } from '@angular/core';
import {
  Form<PERSON>uilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { injectMutation } from '@tanstack/angular-query-experimental';
import { AvatarModule } from 'primeng/avatar';
import { BadgeModule } from 'primeng/badge';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { DropdownModule } from 'primeng/dropdown';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { InputGroupModule } from 'primeng/inputgroup';
import { InputGroupAddonModule } from 'primeng/inputgroupaddon';
import { InputMaskModule } from 'primeng/inputmask';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { acceptedIsoCodes } from '../../constants/ISOCodes';
import { TECHNOLOGY_LIST } from '../../constants/constant';
import { AuthService } from '../../services/auth/auth-service';
import { Client } from '../../services/client/client.model';
import { ClientService } from '../../services/client/client.service';
import { Option, ValidatorTypes } from '../../services/common.model';
import {
  CreateProject,
  EditProjectByResourceId,
  ProjectByResourceId,
  UpdateProject,
} from '../../services/project/project.model';
import { ProjectsService } from '../../services/project/project.service';
import { Resource } from '../../services/resource/resource.model';
import { ResourceService } from '../../services/resource/resource.service';
import { FormSubmitButtonLabelService } from '../../services/formsubmitbuttonlabel/formsubmitbuttonlabel.service';
import { DividerModule } from 'primeng/divider';
import { SELECTED_PROJECTS } from '../../settings';
import { MultiSelectModule } from 'primeng/multiselect';
import { CommonModule } from '@angular/common';
import { Subscription } from 'rxjs';

/**
 * Interface representing the structure of the project form data.
 */
interface ProjectFormData {
  client: Client;
  projectName: string;
  contactName: string;
  contactEmail: string;
  countryCode: Option;
  contactPhoneNumber: string;
  description: string;
  startDate: Date;
  endDate: Date;
  projectManager: Resource;
  technologies: string[];
}

type ErrorMessage = {
  [K in keyof ProjectFormData]?: { [key in ValidatorTypes]?: string };
};

/**
 * Component for managing project creation.
 */
@Component({
  selector: 'tms-project-form',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    DropdownModule,
    InputTextModule,
    InputTextareaModule,
    CalendarModule,
    ButtonModule,
    InputGroupModule,
    InputGroupAddonModule,
    AvatarModule,
    BadgeModule,
    InputMaskModule,
    DividerModule,
    MultiSelectModule,
    CommonModule,
  ],
  templateUrl: './project-form.component.html',
  styleUrl: './project-form.component.css',
})
export class ProjectFormComponent implements OnInit,OnDestroy {
  projectFormGroup: FormGroup;
  isSubmitting: boolean = false;
  managers: Resource[] | undefined;
  isEditMode: boolean = false;
  countryCodes: Option[];
  technology: string[];
  startDateSelected = signal<boolean>(false);
  private startDateSub?: Subscription;
  private endDateSub?: Subscription;

  // Property to hold the end date warning
  endDateWarning: string | null = null;
  private originalEndDate: Date | null = null;

  errorMessages: ErrorMessage = {
    projectName: {
      minlength: 'Project name is too short',
      maxlength: 'Project name is too long',
      pattern: 'Project name cannot contain only space',
    },
    contactName: {
      pattern: 'Contact name cannot contain only space',
    },
    contactEmail: {
      email: 'Enter valid email address',
    },
    contactPhoneNumber: {
      pattern: 'Enter valid contact number',
    },
    description: {
      minlength: 'Description is too short',
      maxlength: 'Description is too long',
      pattern: 'Description cannot contain only space',
    },
  };

  onStartDateSelected() {
    this.startDateSelected.set(true);
  }

  constructor(
    private clientService: ClientService,
    private resourceService: ResourceService,
    private authService: AuthService,
    private projectService: ProjectsService,
    private dialogConfig: DynamicDialogConfig,
    public dialogRef: DynamicDialogRef,
    private formBuilder: FormBuilder,
    public formSubmitButtonLabelService: FormSubmitButtonLabelService
  ) {
    this.countryCodes = acceptedIsoCodes;
    this.technology = TECHNOLOGY_LIST;

    this.isEditMode = dialogConfig.data?.isEditMode;

    this.projectFormGroup = this.formBuilder.group({
      client: [undefined, Validators.required],
      projectName: [
        '',
        [
          Validators.required,
          Validators.minLength(2),
          Validators.maxLength(30),
          Validators.pattern(/^(?!\s*$).+/),
        ],
      ],
      contactName: [
        '',
        [Validators.required, Validators.pattern(/^(?!\s*$).+/)],
      ],
      contactEmail: ['', [Validators.required, Validators.email]],
      countryCode: [
        this.countryCodes.find(
          (countryCode) => countryCode.label === '+91 India'
        ),
      ],
      contactPhoneNumber: [
        '',
        {
          validators: [Validators.pattern('^\\d{10}$')],
          updateOn: 'blur',
        },
      ],
      description: [
        '',
        [
          Validators.required,
          Validators.minLength(2),
          Validators.maxLength(200),
          Validators.pattern(/^(?!\s*$).+/),
        ],
      ],
      startDate: ['', Validators.required],
      endDate: [
        {
          value: '',
          disabled: true,
        },
        //Validators.required,
      ],
      projectManager: ['', Validators.required],
      technologies: ['', Validators.required],
    });

    /**
     * Listens for changes on the 'startDate' form control.
     * Enables and sets validation for the 'endDate' control when a valid 'startDate' is provided.
     */
    if(this.isEditMode){
    this.startDateSub = this.projectFormGroup
      .get('startDate')
      ?.valueChanges.subscribe((startDateValue) => {
        // Retrieve reference to the 'endDate' form control
        const endDateControl = this.projectFormGroup.get('endDate');

        if (startDateValue) {
          // If 'startDate' has a valid value:
          // - Enable the 'endDate' control
          endDateControl?.enable();
        }

        // If start date is more than end date clear the end date
        if (endDateControl?.value && startDateValue > endDateControl.value) {
          endDateControl?.setValue('');
        }

        // - Update the field to re-evaluate validations
        endDateControl?.updateValueAndValidity();
      });
    }
  }

  /**
   * Populate the form with the data on initialization.
   */
  ngOnInit() {
    this.loadProjectData();
  }

  /**
   * Unsubscribes from the endDate valueChanges observable to prevent memory leaks.
   */
  ngOnDestroy() {
    this.startDateSub?.unsubscribe();
    this.endDateSub?.unsubscribe();
  }

  /**
   * Loads existing form data into the reactive form if provided.
   */
  private loadProjectData() {
    const projectData: EditProjectByResourceId =
      this.dialogConfig.data?.projectData;
    const clientPrefilledData = this.dialogConfig.data?.clientPrefilledData;
    if (projectData && this.isEditMode) {
      const { matchedCode, phoneNumber } = this.loadPhoneNumberDetails(
        projectData.contactPhoneNumber ?? ''
      );

      this.projectFormGroup.patchValue({
        client: projectData.client,
        projectName: projectData.projectName,
        projectManager: projectData.projectManagerDetails,
        contactName: projectData.contactName,
        contactEmail: projectData.contactEmail,
        countryCode: matchedCode,
        contactPhoneNumber: (phoneNumber ?? '').trim(),
        startDate: new Date(projectData.startDate),
        endDate: new Date(projectData.endDate ?? new Date()),
        description: projectData.description,
        technologies: projectData.technologies ?? [],
      });

      // Set originalEndDate and subscribe to changes for warning logic
      const endDateControl = this.projectFormGroup.get('endDate');
      this.originalEndDate = endDateControl?.value ? new Date(endDateControl.value) : null;
      this.endDateSub = endDateControl?.valueChanges.subscribe((newValue) => {
        if (
          this.originalEndDate &&
          newValue &&
          new Date(newValue).getTime() !== this.originalEndDate.getTime()
        ) {
          this.endDateWarning =
            'Warning: Changing the project end date requires manual actions on associated contracts.';
        } else {
          this.endDateWarning = null;
        }
      });
    } else if (clientPrefilledData) {
      const { matchedCode, phoneNumber } = this.loadPhoneNumberDetails(
        clientPrefilledData.contactPhoneNumber ?? ''
      );

      this.projectFormGroup.patchValue({
        client: clientPrefilledData.client,
        contactName: clientPrefilledData.contactName,
        contactEmail: clientPrefilledData.contactEmail,
        countryCode: matchedCode,
        contactPhoneNumber: phoneNumber?.trim(),
      });
    }
  }

  private loadPhoneNumberDetails(fullPhoneNumber: string) {
    let matchedCode;
    let phoneNumber;
    for (const code of this.countryCodes) {
      if (fullPhoneNumber?.startsWith(code.value)) {
        matchedCode = code;
        phoneNumber = fullPhoneNumber.substring(code.value.length);
      }
    }
    return { matchedCode, phoneNumber };
  }

  /**
   * Query to fetch list of clients.
   */
  clientsQuery = computed(() => this.clientService.getClientsQuery().data());

  /**
   * Query to fetch list of resources.
   */
  resourcesQuery = computed(() =>
    this.resourceService.getResourcesQuery().data()
  );

  /**
   * Mutation hook for adding a new project.
   * It triggers a mutation to create a new project and handles success/error responses.
   */
  addProjectQuery = injectMutation(() => ({
    mutationKey: ['project', this.authService.userId()],
    mutationFn: (projectData: CreateProject) =>
      this.projectService.createProject(projectData),
    onSuccess: () => {
      this.projectService.employeeProjectsQuery().refetch();
      this.dialogRef.close({
        type: 'success',
        severity: 'success',
        message: 'Project added successfully!',
        summary: 'Project added.',
      });
    },
    onError: (error) => {
      console.error('Error adding project:', error);
      // Check if error.message is an array
      const errorMessages = Array.isArray(error.message)
        ? error.message
        : [error.message];

      errorMessages.forEach((msg: string) => {
        // Show a separate toast message for each error
        this.dialogRef.close({
          type: 'error',
          severity: 'error',
          message: msg,
          summary: 'Failed to create a project',
        });
      });
    },
  }));

  /**
   * Mutation to update an existing project based on form input.
   */
  updateProjectQuery = injectMutation(() => ({
    mutationKey: ['updateProject', this.authService.userId()],
    mutationFn: (projectData: UpdateProject) =>
      this.projectService.updateProject(projectData),
    onSuccess: () => {
      this.projectService.employeeProjectsQuery().refetch();
      this.dialogRef.close({
        type: 'success',
        severity: 'success',
        message: 'Project updated successfully!',
        summary: 'Project Updated.',
      });
      try {
        const projectName = this.projectFormGroup.get('projectName')?.value;
        const projectId = this.dialogConfig.data?.projectData?.id;
        const projects = localStorage.getItem(SELECTED_PROJECTS);
        if (projects) {
          const selectedProjects = JSON.parse(projects);
          const updatedProjects = selectedProjects.map(
            (selectedProject: Option) => {
              if (selectedProject.value === projectId) {
                return {
                  ...selectedProject,
                  label: projectName.trim(),
                };
              }
              return selectedProject;
            }
          );
          localStorage.setItem(
            SELECTED_PROJECTS,
            JSON.stringify(updatedProjects)
          );
        }
      } catch (error) {
        console.error('Failed to update the project in local storage:', error);
        localStorage.removeItem(SELECTED_PROJECTS);
      }
    },
    onError: (error) => {
      console.error('Error updating project:', error);
      // Check if error.message is an array
      const errorMessages = Array.isArray(error.message)
        ? error.message
        : [error.message];

      errorMessages.forEach((msg: string) => {
        // Show a separate toast message for each error
        this.dialogRef.close({
          type: 'error',
          severity: 'error',
          message: msg,
          summary: 'Failed to update a project',
        });
      });
    },
  }));

  /**
   * Check for errors in the form and return an error message for a specific control.
   */
  checkErrors(fieldName: keyof ProjectFormData): string | null {
    const control = this.projectFormGroup.get(fieldName);

    if (control?.invalid && control?.touched && control.errors) {
      const fieldErrors = this.errorMessages[fieldName];
      const firstErrorKey = Object.keys(control.errors)[0];

      return fieldErrors?.[firstErrorKey as ValidatorTypes] ?? null;
    }

    return null;
  }

  /**
   * Submits the form if valid and triggers the mutation to add a new project.
   */
  onSubmit() {
    if (this.projectFormGroup.valid) {
      this.isSubmitting = true;
      const formValue: ProjectFormData = this.projectFormGroup.value;
      const projectDataByResource: ProjectByResourceId =
        this.dialogConfig.data.projectData;

      // Append country code to the phone number
      const fullPhoneNumber = formValue.contactPhoneNumber.trim()
        ? `${formValue.countryCode.value}${formValue.contactPhoneNumber.trim()}`
        : null;
      const technologies = (formValue.technologies || []).map((tech: any) =>
        typeof tech === 'string' ? tech : tech.value
      );

      const projectData: CreateProject = {
        projectName: formValue.projectName.trim(),
        description: formValue.description.trim(),
        startDate: formValue.startDate.toISOString(),
        endDate: formValue.endDate ? formValue.endDate.toISOString() : null,
        contactName: formValue.contactName.trim(),
        contactPhoneNumber: fullPhoneNumber,
        contactEmail: formValue.contactEmail.trim(),
        clientId: formValue.client?.id,
        projectManagerId: formValue.projectManager?.id,
        createdBy: this.authService.userId(),
        technologies: technologies,
      };

      if (this.isEditMode) {
        const updateProjectData: UpdateProject = {
          projectId: projectDataByResource.id,
          ...projectData,
        };
        this.updateProjectQuery.mutate(updateProjectData);
      } else {
        this.addProjectQuery.mutate(projectData);
      }

      this.projectFormGroup.disable();
    }
  }
}
