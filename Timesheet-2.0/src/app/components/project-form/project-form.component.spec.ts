import { provideHttpClientTesting } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms';
import { QueryClient } from '@tanstack/angular-query-experimental';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { AuthService } from '../../services/auth/auth-service';
import { ClientService } from '../../services/client/client.service';
import { ProjectsService } from '../../services/project/project.service';
import { ResourceService } from '../../services/resource/resource.service';
import { ProjectFormComponent } from './project-form.component';

// Mock services
class MockClientService {
  getClientsQuery() {
    return {
      data: () => [],
    };
  }
}

class MockResourceService {
  getResourcesQuery() {
    return {
      data: () => [],
    };
  }
}
class MockAuthService {
  userId() {
    return 'test-user-id'; // Mock user ID
  }
}
class MockProjectsService {}
class MockDynamicDialogRef {}

describe('ProjectFormComponent', () => {
  let component: ProjectFormComponent;
  let fixture: ComponentFixture<ProjectFormComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReactiveFormsModule, ProjectFormComponent],
      providers: [
        { provide: ClientService, useClass: MockClientService },
        { provide: ResourceService, useClass: MockResourceService },
        { provide: AuthService, useClass: MockAuthService },
        { provide: ProjectsService, useClass: MockProjectsService },
        { provide: DynamicDialogRef, useClass: MockDynamicDialogRef },
        provideHttpClientTesting(),
        QueryClient,
        FormBuilder,
        {
          provide: DynamicDialogConfig,
          useValue: {
            data: {
              isEditMode: 'true',
              projectData: 'projectData',
            },
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ProjectFormComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should have required validators on form controls', () => {
    const projectNameControl = component.projectFormGroup.get('projectName');
    const contactNameControl = component.projectFormGroup.get('contactName');
    const contactEmailControl = component.projectFormGroup.get('contactEmail');
    const contactPhoneNumberControl =
      component.projectFormGroup.get('contactPhoneNumber');

    expect(projectNameControl?.hasValidator(Validators.required)).toBeTrue();
    expect(contactNameControl?.hasValidator(Validators.required)).toBeTrue();
    expect(contactEmailControl?.hasValidator(Validators.required)).toBeTrue();
    expect(
      contactPhoneNumberControl?.hasValidator(Validators.required)
    ).toBeTrue();
  });

  it('should display error messages for invalid controls', () => {
    const contactPhoneNumberControl =
      component.projectFormGroup.get('contactPhoneNumber');

    // Mark control as touched to trigger validation messages
    contactPhoneNumberControl?.markAsTouched();
    contactPhoneNumberControl?.setValue('987563');

    // Check for error message
    const errorMessage = component.checkErrors('contactPhoneNumber');

    expect(errorMessage).toBe('Enter valid phone number.');

    // Set a valid value and check again
    contactPhoneNumberControl?.setValue('9875634456');

    const noErrorMessage = component.checkErrors('contactPhoneNumber');

    expect(noErrorMessage).toBeNull(); // Should return null if no errors exist
  });
});
