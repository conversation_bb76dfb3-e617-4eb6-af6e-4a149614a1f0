<form
  [formGroup]="projectFormGroup"
  (ngSubmit)="onSubmit()"
  class="flex flex-col space-y-2 w-full"
>
  <div class="flex flex-col space-y-1">
    <div>
      <p class="font-semibold">Project Details:</p>
      <p-divider styleClass="m-0  mt-1 p-0" />
    </div>
    <div class="flex flex-col space-y-1">
      <div class="grid md:grid-cols-2 gap-4">
        <div class="flex flex-col gap-1">
          <label for="client" class="text-sm text-neutral-500">
            Client <span class="text-lg text-red-500">*</span>
          </label>
          <p-dropdown
            formControlName="client"
            [options]="clientsQuery()"
            optionLabel="name"
            id="client"
            placeholder="Select client"
            [style]="{ width: '100%' }"
            [autofocus]="true"
          />
        </div>

        <div class="flex flex-col gap-1">
          <label for="contactName" class="text-sm text-neutral-500">
            Contact Name <span class="text-lg text-red-500">*</span>
          </label>
          <input
            pInputText
            id="contactName"
            formControlName="contactName"
            placeholder="Enter contact name"
            [style]="{ width: '100%' }"
          />
          <small class="text-red-500 min-h-[1rem] block">{{
            checkErrors('contactName') || '\u00A0'
          }}</small>
        </div>
      </div>

      <div class="grid md:grid-cols-2 gap-4">
        <div class="flex flex-col gap-1">
          <label for="contactEmail" class="text-sm text-neutral-500">
            Contact Email
            <span class="text-red-500 text-lg leading-none">*</span>
          </label>
          <input
            pInputText
            id="contactEmail"
            formControlName="contactEmail"
            placeholder="Enter email address"
            [style]="{ width: '100%' }"
          />
          <small class="text-red-500 min-h-[1rem] block">{{
            checkErrors('contactEmail') || '\u00A0'
          }}</small>
        </div>

        <div class="flex flex-col w-full gap-1">
          <label for="contactPhoneNumber" class="text-sm text-neutral-500">
            Contact Number
          </label>

          <div class="flex w-full" id="contactPhoneNumber">
            <p-dropdown
              formControlName="countryCode"
              [options]="countryCodes"
              optionLabel="value"
              [style]="{ width: '100%' }"
              styleClass="border-r-0 rounded-r-none px-0"
              [dropdownIcon]="'pi pi-angle-down'"
            >
              <ng-template pTemplate="selectedItem" let-selected>
                <div class="flex items-center">
                  <div>{{ selected.value }}</div>
                </div>
              </ng-template>
              <ng-template let-country pTemplate="item">
                <div class="flex items-center">
                  <div>{{ country.label }}</div>
                </div>
              </ng-template>
            </p-dropdown>

            <input
              pInputText
              formControlName="contactPhoneNumber"
              type="tel"
              maxlength="10"
              [style]="{ width: '100%' }"
              placeholder="Enter contact number"
              class="rounded-l-none pl-1"
            />
          </div>
          <small class="text-red-500">
            {{ checkErrors('contactPhoneNumber') || '\u00A0' }}
            {{ checkErrors('countryCode') || '\u00A0' }}
          </small>
        </div>
      </div>
    </div>

    <div class="flex flex-col space-y-1">
      <div class="flex flex-col gap-1">
        <label for="projectName" class="text-sm text-neutral-500"
          >Project Name<span class="text-lg text-red-500">*</span></label
        >
        <input
          pInputText
          id="projectName"
          formControlName="projectName"
          placeholder="Enter project name"
          [style]="{ width: '100%' }"
        />
        <small class="text-red-500 min-h-[1rem] block">{{
          checkErrors('projectName') || '\u00A0'
        }}</small>
      </div>

      <div class="flex flex-col gap-1 pb-5">
        <label for="projectManager" class="text-sm text-neutral-500">
          Project Manager<span class="text-lg text-red-500">*</span>
        </label>
        <p-dropdown
          formControlName="projectManager"
          [options]="resourcesQuery()"
          optionLabel="name"
          id="projectManager"
          placeholder="Select project manager"
          [filter]="true"
          filterBy="name"
          [style]="{ width: '100%' }"
        >
          <!-- Template for selected item -->
          <ng-template pTemplate="selectedItem" let-selected>
            <div class="flex items-center space-x-2">
              @if (selected.profilePicUrl) {
                <p-avatar
                  [image]="selected.profilePicUrl"
                  styleClass=""
                  shape="circle"
                />
              } @else {
                <p-avatar
                  [label]="selected.name.charAt(0)"
                  styleClass=""
                  shape="circle"
                />
              }
              <div>{{ selected.name }}</div>
            </div>
          </ng-template>

          <!-- Template for dropdown options -->
          <ng-template pTemplate="item" let-resource>
            <div class="flex items-center space-x-2">
              @if (resource.profilePicUrl) {
                <p-avatar
                  [image]="resource.profilePicUrl"
                  styleClass=""
                  shape="circle"
                />
              } @else {
                <p-avatar
                  [label]="resource.name.charAt(0)"
                  styleClass=""
                  shape="circle"
                />
              }
              <div>{{ resource.name }}</div>
            </div>
          </ng-template>
        </p-dropdown>
      </div>

      <div class="grid md:grid-cols-2 gap-4 pb-5">
        <div class="flex flex-col gap-1">
          <label for="startDate" class="text-sm text-neutral-500">
            Start Date <span class="text-lg text-red-500">*</span>
          </label>
          <p-calendar
            #startDate
            formControlName="startDate"
            id="startDate"
            placeholder="Select start date"
            [appendTo]="'body'"
            [contentEditable]="false"
            [readonlyInput]="true"
            [showIcon]="true"
            [iconDisplay]="'input'"
            dateFormat="dd/mm/yy"
            [style]="{ width: '100%' }"
            (onSelect)="onStartDateSelected()"
          ></p-calendar>
        </div>
        <div class="flex flex-col gap-1">
          <label for="endDate" class="text-sm text-neutral-500">
            End Date<span class="text-lg text-red-500"></span>
          </label>
          <p-calendar
            formControlName="endDate"
            id="endDate"
            [placeholder]="'Select end date'"
            [appendTo]="'body'"
            [contentEditable]="false"
            [readonlyInput]="true"
            [showIcon]="true"
            [iconDisplay]="'input'"
            dateFormat="dd/mm/yy"
            [minDate]="startDate.value"
            [style]="{ width: '100%' }"
            [disabled]="!isEditMode"
          ></p-calendar>
          @if (!isEditMode) {
            <small class="flex items-center bg-gray-100 border border-gray-300 text-gray-700 rounded-md px-2 py-1 mt-1">
              <i class="pi pi-info-circle mr-2"></i>End date is calculated based on the contract end date.
            </small>
          }
          @if (isEditMode && endDateWarning) {
            <small class="flex items-start bg-amber-100 border border-amber-300 text-amber-800 rounded-md px-2 py-1 mt-1 text-xs">
             {{ endDateWarning }}
            </small>
          }
        </div>
      </div>

      <div class="flex flex-col gap-1 pb-5">
        <label for="technologies" class="text-sm text-neutral-500">
          Technology <span class="text-lg text-red-500">*</span>
        </label>

        <p-multiSelect
          formControlName="technologies"
          [options]="technology"
          optionLabel=""
          [style]="{ width: '100%' }"
          [defaultLabel]="'Select Technologies'"
        >
          <ng-template let-tech pTemplate="item">
            {{ tech }}
          </ng-template>
        </p-multiSelect>
      </div>

      <div class="flex flex-col gap-1">
        <label for="description" class="text-sm text-neutral-500"
          >Description<span class="text-lg text-red-500">*</span></label
        >
        <textarea
          pInputTextarea
          id="description"
          formControlName="description"
          rows="4"
          placeholder="Enter project description"
          [style]="{ width: '100%' }"
        ></textarea>
        <small class="text-red-500 min-h-[1rem] block">{{
          checkErrors('description') || '\u00A0'
        }}</small>
      </div>
    </div>
  </div>

  <div class="flex flex-row-reverse">
    <p-button
      type="submit"
      [label]="
        formSubmitButtonLabelService.getFormSubmitButtonLabel(
          isSubmitting,
          isEditMode
        )
      "
      [icon]="
        formSubmitButtonLabelService.getFormSubmitButtonIcon(isSubmitting)
      "
      [disabled]="
        projectFormGroup.invalid ||
        isSubmitting ||
        !projectFormGroup.get('projectName')?.value.trim() ||
        !projectFormGroup.get('description')?.value.trim() ||
        !projectFormGroup.get('contactName')?.value.trim()
      "
      styleClass="submit-button disabled:cursor-not-allowed disabled:bg-neutral-400 disabled:border-neutral-400"
      aria-label="Submit"
    ></p-button>
  </div>
</form>

