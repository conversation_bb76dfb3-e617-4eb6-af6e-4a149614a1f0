<!--
  This template provides a user interface for activating a contract. It confirms the user's
  intent to activate the contract and, if the current date is past the contract's end date,
  prompts the user to select a new end date using a calendar component. -->
<form [formGroup]="contractActivationForm" (ngSubmit)="onSubmit()">
  <!-- Confirmation Message -->
  <div class="flex items-center space-x-2">
    <i class="pi pi-exclamation-circle"></i>
    <p>
      Are you sure you want to activate contract
      <b>{{ contract.contractId }}</b>
    </p>
  </div>
  <!-- Conditional Section: Displayed if Today's Date is After contract's End Date -->
  @if (getCurrentDate() > getContractEndDate()) {
    <div class="mt-5">
      <p-divider />
      <!-- Display Current End Date -->
      <div class="flex justify-between">
        <span>Current End Date</span>
        <span class="font-bold">{{
          getContractEndDate() | date: 'MMM d, y'
        }}</span>
      </div>
      <p-divider />

      <!-- Information Message -->
      <small
        class="flex items-center bg-amber-100 border border-amber-300 text-amber-800 rounded-md px-2 py-1"
        ><i class="pi pi-info-circle mr-2"></i>End Date has already passed.
        Please select new End Date</small
      >
      <!-- Calendar Component for Selecting New End Date -->
      <p-calendar
        [autofocus]="false"
        [showIcon]="true"
        [minDate]="minDate"
        [appendTo]="'body'"
        styleClass="w-full mt-3"
        placeholder="Select End Date"
        formControlName="endDate"
      ></p-calendar>
    </div>
  }
  <!-- Action Buttons: Cancel and Activate -->
  <div class="flex justify-end mt-10 space-x-3">
    <p-button label="Cancel" (onClick)="onCancel()"></p-button>
    <p-button
      type="submit"
      label="Activate"
      styleClass="p-button-success"
      [disabled]="
        getCurrentDate() > getContractEndDate() &&
        contractActivationForm.invalid
      "
    ></p-button>
  </div>
</form>
