import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ContractActivationDialogComponent } from './contract-activation-dialog.component';
import { ReactiveFormsModule } from '@angular/forms';
import { CalendarModule } from 'primeng/calendar';
import { ButtonModule } from 'primeng/button';
import { DividerModule } from 'primeng/divider';
import { DynamicDialogRef, DynamicDialogConfig } from 'primeng/dynamicdialog';
import { ContractService } from '../../services/contract/contract.service';
import { MessageService } from 'primeng/api';

/**
 * Test suite for ContractActivationDialogComponent.
 */
describe('ContractActivationDialogComponent', () => {
  let component: ContractActivationDialogComponent;
  let fixture: ComponentFixture<ContractActivationDialogComponent>;
  let dialogRefSpy: jasmine.SpyObj<DynamicDialogRef>;

  beforeEach(async () => {
    dialogRefSpy = jasmine.createSpyObj('DynamicDialogRef', ['close']);

    await TestBed.configureTestingModule({
      imports: [
        ContractActivationDialogComponent,
        ReactiveFormsModule,
        CalendarModule,
        ButtonModule,
        DividerModule,
      ],
      providers: [
        { provide: DynamicDialogRef, useValue: dialogRefSpy },
        {
          provide: DynamicDialogConfig,
          useValue: {
            data: {
              contract: {
                id: '123',
                contractId: 'C123',
                endDate: '2024-02-25',
              },
              projectId: 'P123',
            },
          },
        },
        { provide: ContractService, useValue: {} },
        MessageService,
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ContractActivationDialogComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  /**
   * Test that the component is created successfully.
   */
  it('should create', () => {
    expect(component).toBeTruthy();
  });

  /**
   * Test that the end date field is displayed if the current date is past the contract end date.
   */
  it('should display end date field if current date is past contract end date', () => {
    spyOn(component, 'getCurrentDate').and.returnValue(new Date('2024-02-26')); // Simulate a future date
    fixture.detectChanges();

    const endDateInput = fixture.nativeElement.querySelector('p-calendar');
    expect(endDateInput).toBeTruthy();
  });

  /**
   * Test that the "Activate" button is disabled if the end date field is invalid.
   */
  it('should disable "Activate" button if end date field is invalid', () => {
    spyOn(component, 'getCurrentDate').and.returnValue(new Date('2024-02-26')); // Simulate a future date
    fixture.detectChanges();

    const activateButton = fixture.nativeElement.querySelector(
      'p-button[label="Activate"]'
    );
    expect(activateButton.disabled).toBeTrue();
  });

  /**
   * Test that clicking the "Cancel" button calls onCancel and closes the dialog.
   */
  it('should call onCancel and close the dialog when "Cancel" button is clicked', () => {
    const cancelButton = fixture.nativeElement.querySelector(
      'p-button[label="Cancel"]'
    );
    cancelButton.click();
    expect(dialogRefSpy.close).toHaveBeenCalled();
  });

  /**
   * Test that submitting the form calls onSubmit and activates the contract when the form is valid.
   */
  it('should call onSubmit and activate the contract when form is valid', () => {
    spyOn(component, 'getCurrentDate').and.returnValue(new Date('2024-02-26')); // Simulate a future date
    spyOn(component, 'activateContract');

    component.contractActivationForm.setValue({ endDate: '2024-03-01' });
    fixture.detectChanges();

    const form = fixture.nativeElement.querySelector('form');
    form.dispatchEvent(new Event('submit'));

    expect(component.activateContract).toHaveBeenCalledWith({
      contractId: '123',
      endDate: '2024-03-01',
    });
  });
});
