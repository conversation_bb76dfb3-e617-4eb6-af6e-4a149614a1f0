import {
  Component,
  computed,
  Injector,
  runInInjectionContext,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { CommonModule } from '@angular/common';
import { DividerModule } from 'primeng/divider';
import { CalendarModule } from 'primeng/calendar';
import { injectMutation } from '@tanstack/angular-query-experimental';
import {
  ActivateContractPayload,
  ContractsById,
} from '../../services/contract/contract.model';
import { ContractService } from '../../services/contract/contract.service';

/**
 * Component for displaying a dialog to activate a contract.
 *
 * @selector tms-contract-activation-dialog
 */
@Component({
  selector: 'tms-contract-activation-dialog',
  standalone: true,
  imports: [
    ButtonModule,
    FormsModule,
    CommonModule,
    ReactiveFormsModule,
    DividerModule,
    CalendarModule,
  ],
  providers: [MessageService],
  templateUrl: './contract-activation-dialog.component.html',
  styleUrl: './contract-activation-dialog.component.css',
})
export class ContractActivationDialogComponent {
  contract: ContractsById;
  contractActivationForm: FormGroup;
  projectId: string;
  minDate: Date = new Date();
  constructor(
    private contractService: ContractService,
    private injector: Injector,
    private fb: FormBuilder,
    private dialogConfig: DynamicDialogConfig,
    private dialogRef: DynamicDialogRef
  ) {
    this.contract = this.dialogConfig.data.contract;
    this.projectId = this.dialogConfig.data.projectId;
    this.contractActivationForm = this.fb.group({
      endDate: ['', Validators.required],
    });
  }

  /**
   * Computed property to fetch contracts by project ID.
   */
  contractsQuery = computed(() =>
    this.contractService.contractsByProjectIdQuery(this.projectId)
  );

  public getCurrentDate() {
    return new Date();
  }

  /**
   * Retrieves the end date of the contract.
   *
   * This function checks if the contract's end date is a valid string and converts it
   * to a Date object. It throws an error if the date format is invalid.
   *
   * @returns The end date of the contract as a Date object.
   * @throws {Error} If the end date format is invalid.
   */
  getContractEndDate() {
    if (typeof this.contract.endDate !== 'string') {
      throw new Error('Invalid date format');
    }
    return new Date(this.contract.endDate);
  }

  /**
   * Activates a contract using the provided payload.
   *
   * @param activateContractPayload Payload containing contract ID and end date.
   */
  activateContract(activatecontractPayload: ActivateContractPayload) {
    const activateContractQueryOptions =
      this.contractService.activateContractQueryOptions(
        activatecontractPayload
      );

    runInInjectionContext(this.injector, () => {
      const activateContractMutation = injectMutation(
        () => activateContractQueryOptions
      );
      activateContractMutation.mutate(activatecontractPayload, {
        onSuccess: (data) => {
          this.contractsQuery().refetch();
          this.dialogRef.close({
            type: 'success',
            severity: data.status,
            message: data.message,
            summary: 'Contract Activated.',
          });
        },
        onError: (error: Error) =>
          this.dialogRef.close({
            type: 'error',
            severity: 'error',
            message: error.message,
            summary: 'Contract Activation Failed',
          }),
      });
    });
  }

  /**
   * Handles form submission for activating the project.
   *
   * This function constructs the payload for activating the project based on the form values
   * and calls the `activateProject` method with that payload.
   */
  onSubmit() {
    const currentDate = this.getCurrentDate();
    const endDate =
      currentDate > this.getContractEndDate()
        ? this.contractActivationForm.value.endDate
        : this.getContractEndDate();
    const activateContractPayload: ActivateContractPayload = {
      contractId: this.contract.id,
      endDate: endDate,
    };
    this.activateContract(activateContractPayload);
  }

  /**
   * Cancels the activation process and closes the dialog.
   */
  onCancel() {
    this.dialogRef.close();
  }
}
