import { ComponentFixture, TestBed } from '@angular/core/testing';

import { WorklogEntryComponent } from './worklog-entry.component';
import { MessageService } from 'primeng/api';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { QueryClient } from '@tanstack/angular-query-experimental';

const worklog = {
  id: '3',
  workDate: new Date('2024-12-12T08:00:00Z'),
  startDate: new Date('2024-12-12T10:00:00Z'),
  endDate: new Date('2024-12-12T15:00:00Z'),
  clientId: '125',
  projectId: '458',
  employeeId: '791',
  managerId: null,
  taskId: '101114',
  description: 'Prepared project documentation and user manuals.',
  minutes: 300,
  createdAt: new Date('2024-12-12T09:00:00Z'),
  updatedAt: new Date('2024-12-12T15:30:00Z'),
  deleted: false,
  isOnLeave: false,
  isOnFirstHalfLeave: false,
  isOnSecondHalfLeave: false,
  isWeekOff: false,
  isCompanyOff: false,
  employeeResource: {
    name: '<PERSON>',
  },
  project: {
    projectName: 'User Research',
    billable: true,
    projectStatus: 'in-progress',
  },
  client: {
    name: 'Innovate Inc.',
    status: true,
  },
  workLogStatus: {
    status: 'rejected',
    remarks: 'Documentation ready for client review.',
  },
  task: {
    taskName: 'Documentation',
    contract: {
      customContractId: 'cont-003',
    },
  },
};

describe('WorklogEntryComponent', () => {
  let component: WorklogEntryComponent;
  let fixture: ComponentFixture<WorklogEntryComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [WorklogEntryComponent],
      providers: [QueryClient, MessageService, DialogService, DynamicDialogRef],
    }).compileComponents();

    fixture = TestBed.createComponent(WorklogEntryComponent);
    component = fixture.componentInstance;
    fixture.componentRef.setInput('worklog', {});
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('Worklog should be defined', () => {
    fixture.componentRef.setInput('worklog', worklog);
    expect(component.worklog).toBeDefined();
  });

  it('formatHoursAndMinutes should give formatted date and hours', () => {
    expect(component.formatHoursAndMinutes(480)).toBe('08:00');
    expect(component.formatHoursAndMinutes(100)).toBe('01:40');
    expect(component.formatHoursAndMinutes(300)).toBe('05:00');
    expect(component.formatHoursAndMinutes(240)).toBe('04:00');
  });
});
