<div
  class="flex items-center justify-start truncate text-xs hover:text-white cursor-pointer rounded-lg p-1.5 md:py-1 md:px-2 first:mb-1.5 transition-colors gap-2"
  [ngClass]="{
    'bg-primary-100 text-primary-600 hover:bg-primary-500':
      worklog().workLogStatus?.status === 'submitted',
    'bg-green-100 text-green-600 hover:bg-green-500':
      worklog().workLogStatus?.status === 'approved',
    'bg-red-100 text-red-600 hover:bg-red-500':
      worklog().workLogStatus?.status === 'rejected',
    'bg-purple-100 text-purple-600 hover:bg-purple-500':
      worklog().workLogStatus?.status === 'revised',
  }"
  (click)="onWorklogEntryClick(worklog(), $event)"
  (keypress)="onWorklogEntryClick(worklog(), $event)"
  tabindex="0"
  [pTooltip]="tooltipContent"
  [autoHide]="false"
  [tooltipPosition]="worklog().isWeekOff ? 'left' : 'right'"
>
  <span class="text-center"
    >{{ formatHoursAndMinutes(worklog().minutes) }}
  </span>
  <span>&ndash;</span>
  <span class="font-semibold">{{ worklog().project?.projectName }}</span>
</div>
<ng-template #tooltipContent>
  <tms-worklog-popup-view [worklog]="worklog()" />
</ng-template>
