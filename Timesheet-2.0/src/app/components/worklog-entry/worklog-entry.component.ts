import { CommonModule } from '@angular/common';
import { Component, input } from '@angular/core';
import { MessageService } from 'primeng/api';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { TooltipModule } from 'primeng/tooltip';
import { Worklog } from '../../services/worklog/worklog.model';
import { formatHoursAndMinutes } from '../../utils/date.utils';
import { EditDialogHeader } from '../worklog-display/edit-dialog-header.component';
import { WorklogFormComponent } from '../worklog-form/worklog-form.component';
import { WorklogPopupViewComponent } from '../worklog-popup-view/worklog-popup-view.component';

@Component({
  selector: 'tms-worklog-entry',
  standalone: true,
  imports: [WorklogPopupViewComponent, TooltipModule, CommonModule],
  templateUrl: './worklog-entry.component.html',
})
export class WorklogEntryComponent {
  worklog = input.required<Worklog>();
  formatHoursAndMinutes = formatHoursAndMinutes;

  constructor(
    private messageService: MessageService,
    private dialogService: DialogService,
    private dialogRef: DynamicDialogRef
  ) {}

  onWorklogEntryClick(worklog: Worklog, event: Event) {
    event.stopPropagation();
    if (worklog.workLogStatus?.status === 'approved') {
      this.messageService.add({
        severity: 'info',
        summary: 'Edit Worklog',
        detail: 'Cannot edit approved worklogs.',
      });
      return;
    }

    this.dialogRef = this.dialogService.open(WorklogFormComponent, {
      data: {
        date: worklog.workDate,
        worklog,
      },
      templates: {
        header: EditDialogHeader,
      },
      styleClass: 'custom-header-dialog',
      header: 'Edit Worklog',
      dismissableMask: false,
      closable: true,
    });
    this.dialogRef.onClose.subscribe((result) => {
      if (result) {
        if (result.type === 'success') {
          this.messageService.add({
            severity: result.severity,
            summary: result.summary,
            detail: result.message,
            life: 3000,
          });
        } else if (result.type === 'error') {
          this.messageService.add({
            severity: result.severity,
            summary: result.summary,
            detail: result.message,
            life: 3000,
          });
        }
      }
    });
  }
}
