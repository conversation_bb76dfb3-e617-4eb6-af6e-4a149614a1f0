import { CommonModule } from '@angular/common';
import { Component, input, ViewEncapsulation } from '@angular/core';
import { format } from 'date-fns';
import { ButtonModule } from 'primeng/button';
import { DialogModule } from 'primeng/dialog';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { Worklog } from '../../services/worklog/worklog.model';
import { formatHoursAndMinutes } from '../../utils/date.utils';
import { ScrollPanelModule } from 'primeng/scrollpanel';
import { TagComponent } from '../tag/tag.component';
import { getWorklogChipDetails } from '../../utils/chip.utils';
import { WORKLOG_LOCATION } from '../../constants/constant';

@Component({
  selector: 'tms-worklog-popup-view',
  standalone: true,
  imports: [
    DialogModule,
    ButtonModule,
    CommonModule,
    OverlayPanelModule,
    ScrollPanelModule,
    TagComponent,
  ],
  templateUrl: './worklog-popup-view.component.html',
  styleUrl: './worklog-popup-view.component.css',
  encapsulation: ViewEncapsulation.None,
})
export class WorklogPopupViewComponent {
  worklog = input.required<Worklog>();
  getWorklogChipDetails = getWorklogChipDetails;
  formatHoursAndMinutes = formatHoursAndMinutes;
  format = format;

  getWorkedHours(minutes: number) {
    const formattedTime = formatHoursAndMinutes(minutes);
    const [hours, mins] = formattedTime.split(':').map(Number);
    const hourString = `${hours}h`;
    const minuteString = mins ? `:${mins}m` : '';
    return `${hourString}${minuteString}`;
  }
  getLocationLabel(value: string): string {
    const match = WORKLOG_LOCATION.find(option => option.value === value);
    return match ? match.label : value;
  }
}
