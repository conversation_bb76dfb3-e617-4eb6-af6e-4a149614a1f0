<div class="flex flex-col gap-2 worklog-tooltip">
  <div class="flex justify-between gap-4 items-center mb-1.5 flex-wrap">
    <span class="text-xl font-semibold text-neutral-700">{{
      this.worklog().project?.projectName
    }}</span>
    <tms-tag
      [label]="getWorklogChipDetails(worklog())?.chipText ?? ''"
      [styles]="getWorklogChipDetails(worklog())?.chipClass ?? ''"
      class="text-xs"
    />
  </div>

  <div class="flex flex-col gap-2">
    <div class="flex items-center gap-1 text-primary-500 text-sm">
      <span class="truncate">{{ worklog().task?.taskName }}</span>
      <span>|</span>
      <span class="text-nowrap">{{ getWorkedHours(worklog().minutes) }}</span>
    </div>
    <div *ngIf="worklog()?.location" class="flex items-center gap-1 text-gray-500 text-sm">
      <i class="pi pi-map-marker text-gray-500"></i>
      <span class="truncate">
        {{ getLocationLabel(worklog().location!) }}
      </span>
    </div>     

    @if (worklog().description) {
      <p-scrollPanel
        [style]="{ maxHeight: '150px' }"
        class="text-neutral-500 text-sm font-thin"
        >{{ worklog().description }}</p-scrollPanel
      >
    }
  </div>
</div>
