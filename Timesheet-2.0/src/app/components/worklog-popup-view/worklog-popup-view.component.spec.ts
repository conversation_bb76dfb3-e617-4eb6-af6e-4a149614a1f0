import { ComponentFixture, TestBed } from '@angular/core/testing';
import { WorklogPopupViewComponent } from './worklog-popup-view.component';
import { Worklog } from '../../services/worklog/worklog.model';

let worklogs: Worklog[] = [
  {
    id: '1',
    workDate: new Date('2024-12-10T08:00:00Z'),
    startDate: new Date('2024-12-10T09:00:00Z'),
    endDate: new Date('2024-12-10T17:00:00Z'),
    clientId: '123',
    projectId: '456',
    employeeId: '789',
    managerId: '987',
    taskId: '101112',
    description: 'Completed module implementation and bug fixes.',
    minutes: 480,
    createdAt: new Date('2024-12-10T07:00:00Z'),
    updatedAt: new Date('2024-12-10T18:00:00Z'),
    deleted: false,
    isOnLeave: false,
    isOnFirstHalfLeave: false,
    isOnSecondHalfLeave: false,
    isWeekOff: false,
    isCompanyOff: false,
    employeeResource: {
      name: '<PERSON>',
    },
    project: {
      startDate: new Date('2023-01-01T00:00:00Z'),
      endDate: new Date('2023-12-31T00:00:00Z'),
      projectName: 'Mobile App Development',
      billable: true,
      projectStatus: 'active',
    },
    client: {
      name: 'Acme Corp',
      status: true,
    },
    workLogStatus: {
      status: 'approved',
      remarks: 'Good progress made on the project.',
    },
    task: {
      taskName: 'UI Design',
      contract: {
        customContractId: 'cont-001',
      },
    },
  },
  {
    id: '2',
    workDate: new Date('2024-12-11T08:00:00Z'),
    startDate: new Date('2024-12-11T09:00:00Z'),
    endDate: new Date('2024-12-11T16:00:00Z'),
    clientId: '124',
    projectId: '457',
    employeeId: '790',
    managerId: '988',
    taskId: '101113',
    description: 'Refactored API endpoints to improve performance.',
    minutes: 420,
    createdAt: new Date('2024-12-11T07:00:00Z'),
    updatedAt: new Date('2024-12-11T16:30:00Z'),
    deleted: false,
    isOnLeave: false,
    isOnFirstHalfLeave: false,
    isOnSecondHalfLeave: false,
    isWeekOff: false,
    isCompanyOff: false,
    employeeResource: {
      name: 'Jane Smith',
    },
    project: {
      startDate: new Date('2023-01-01T00:00:00Z'),
      endDate: new Date('2023-12-31T00:00:00Z'),
      projectName: 'API Integration',
      billable: false,
      projectStatus: 'completed',
    },
    client: {
      name: 'Tech Solutions',
      status: false,
    },
    workLogStatus: {
      status: 'submitted',
      remarks: 'Awaiting manager review.',
    },
    task: {
      taskName: 'API Optimization',
      contract: {
        customContractId: 'cont-002',
      },
    },
  },
  {
    id: '3',
    workDate: new Date('2024-12-12T08:00:00Z'),
    startDate: new Date('2024-12-12T10:00:00Z'),
    endDate: new Date('2024-12-12T15:00:00Z'),
    clientId: '125',
    projectId: '458',
    employeeId: '791',
    managerId: null,
    taskId: '101114',
    description: 'Prepared project documentation and user manuals.',
    minutes: 300,
    createdAt: new Date('2024-12-12T09:00:00Z'),
    updatedAt: new Date('2024-12-12T15:30:00Z'),
    deleted: false,
    isOnLeave: false,
    isOnFirstHalfLeave: false,
    isOnSecondHalfLeave: false,
    isWeekOff: false,
    isCompanyOff: false,
    employeeResource: {
      name: 'Alice Johnson',
    },
    project: {
      startDate: new Date('2023-01-01T00:00:00Z'),
      endDate: new Date('2023-12-31T00:00:00Z'),
      projectName: 'User Research',
      billable: true,
      projectStatus: 'in-progress',
    },
    client: {
      name: 'Innovate Inc.',
      status: true,
    },
    workLogStatus: {
      status: 'rejected',
      remarks: 'Documentation ready for client review.',
    },
    task: {
      taskName: 'Documentation',
      contract: {
        customContractId: 'cont-003',
      },
    },
  },
];

describe('WorklogPopupViewComponent', () => {
  let component: WorklogPopupViewComponent;
  let fixture: ComponentFixture<WorklogPopupViewComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [WorklogPopupViewComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(WorklogPopupViewComponent);
    component = fixture.componentInstance;
    fixture.componentRef.setInput('worklog', worklogs[0]);
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should return formatted hours and minutes when minutes are non-zero', () => {
    const result = component.getWorkedHours(150);
    expect(result).toBe('2h:30m');
  });

  it('should return only hours when minutes are zero', () => {
    const result = component.getWorkedHours(180);
    expect(result).toBe('3h');
  });

  it('should handle single-digit minutes correctly', () => {
    const result = component.getWorkedHours(65);
    expect(result).toBe('1h:5m');
  });

  it('should handle edge case of zero minutes', () => {
    const result = component.getWorkedHours(0);
    expect(result).toBe('0h');
  });

  it('should handle large values correctly', () => {
    const result = component.getWorkedHours(6015);
    expect(result).toBe('100h:15m');
  });
});
