import { CommonModule } from '@angular/common';
import {
  Component,
  computed,
  OnInit,
  output,
  signal,
  viewChild,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CalendarModule } from 'primeng/calendar';
import { MultiSelectChangeEvent, MultiSelectModule } from 'primeng/multiselect';
import { TableModule, Table } from 'primeng/table';
import { InputTextModule } from 'primeng/inputtext';
import { ButtonModule } from 'primeng/button';
import { ResourceReportService } from '../../services/resource-report/resource-report.service';
import { Option } from '../../services/common.model';
import { SkeletonModule } from 'primeng/skeleton';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ToastModule } from 'primeng/toast';
import { getDaysInMonth } from 'date-fns';
import { TooltipModule } from 'primeng/tooltip';
import { LogsheetsDetailedViewComponent } from '../../components/logsheets-detailedview/logsheets-detailedview.component';
import { Dropdown, DropdownModule } from 'primeng/dropdown';
import { MultiSelect } from 'primeng/multiselect';
import { DepartmentService } from '../../services/department/department.service';
import { DateTableComponent } from '../date-table/date-table.component';
import { convertMinutesToHours } from '../../utils/date.utils';

interface Column {
  field: string;
  header: string;
}

export interface WorklogDayData {
  totalHours: string;
  statuses: WorklogStatus[];
  totalMinutes: number;
  date: Date;
  resourceId: string;
}

export type WorklogStatus =
  | 'no worklogs'
  | 'holiday'
  | 'weekoff'
  | 'leave'
  | 'halfDayLeave'
  | 'submitted'
  | 'approved'
  | 'revised'
  | 'rejected';

@Component({
  selector: 'tms-resource-report-page',
  standalone: true,
  imports: [
    TableModule,
    CommonModule,
    DropdownModule,
    FormsModule,
    CalendarModule,
    InputTextModule,
    MultiSelectModule,
    ButtonModule,
    SkeletonModule,
    ConfirmDialogModule,
    ToastModule,
    TooltipModule,
    LogsheetsDetailedViewComponent,
    DateTableComponent,
  ],
  providers: [ConfirmationService, MessageService],
  templateUrl: './resource-report.component.html',
  styleUrl: './resource-report.component.css',
})
export class ResourceReportComponent implements OnInit {
  table = viewChild<Table>('reportSheetTable');
  departmentDropdown = viewChild<MultiSelect>('departmentDropdown');
  statusDropdown = viewChild<Dropdown>('statusDropdown');
  convertMinutesToHours = convertMinutesToHours;

  readonly skeletonRows = Array(3);
  readonly date = signal<Date>(new Date());
  readonly maxDate = new Date();
  readonly columnHeader = signal<Column[]>([]);
  readonly selectedMonth = signal<string>('');
  readonly selectedYear = signal<string>('');
  readonly selectedDepartments = signal<string[]>([]);
  readonly selectedStatus = signal<string>('');
  readonly selectedResourcesIds = signal<string[]>([]);
  readonly dateRange = computed(() => this.getDateRange(this.date()));
  private selectedMonthIndex = 0;
  private selectedYearNumber = 0;
  pageSize = 10;
  rowsPerPageOptions = [10, 20, 50, 100];

  worklogDetails = output<WorklogDayData>();
  storedResources: Option[] = [];

  statusOptions: Option[] = [
    { label: 'All', value: 'all' },
    { label: 'Active', value: 'active' },
    { label: 'InActive', value: 'inactive' },
  ];

  /**
   * Computes and retrieves the resource details based on the selected resource IDs.
   * If no resource IDs are selected, it returns all resources.
   *
   * @returns {Array} An array of resource objects with additional properties like `resourceName`, `kekaId`, `department`, etc.
   */
  readonly fetchResourceDetails = computed(() => {
    const resourceData = this.getResourcesQuery().data();
    const selectedIds = this.selectedResourcesIds();

    if (!resourceData?.data) return [];

    let resources = resourceData.data.map((resource) => ({
      ...resource,
      resourceName: resource.resourceName,
      kekaId: resource.kekaId,
      department: resource.department,
      totalMinutes: resource.totalMinutes,
      totalDays: resource.totalDays,
      resourceWorklog: resource.resourcesWorklog ?? [],
      leaveDetails: resource.leaveDetails ?? [],
    }));

    if (selectedIds.length > 0) {
      resources = resources.filter((res) =>
        selectedIds.includes(res.resourceId)
      );
    }

    return resources;
  });

  /**
   * Fetches the list of all departments using the `DepartmentService`.
   *
   * @returns {Query} A query object containing the list of departments.
   */
  readonly departmentQuery = computed(() =>
    this.departmentService.listAllDepartmentsQuery()
  );

  /**
   * Fetches the resource overview data based on the selected filters such as month, year, billing status, resource status, and department.
   *
   * @returns {Query} A query object containing the resource overview data.
   */
  readonly getResourcesQuery = computed(() => {
    const selectedDepartments = this.selectedDepartments();

    return this.resourceReportService.getResourceOverviewQuery({
      month: this.dateRange().startDate.getMonth() + 1,
      year: this.dateRange().startDate.getFullYear(),
      resourceStatus: this.selectedStatus(),
      department:
        selectedDepartments.length > 0 ? selectedDepartments : undefined,
    });
  });

  constructor(
    private readonly departmentService: DepartmentService,
    private readonly resourceReportService: ResourceReportService
  ) {}

  ngOnInit(): void {
    this.selectedStatus();
    this.updateColumns(this.date());
  }

  onDateSelect(selectedDate: Date): void {
    this.date.set(selectedDate);
    this.updateColumns(selectedDate);
  }

  /**
   * Retrieves the options for the department dropdown.
   *
   * @returns {Option[]} An array of department options with `label` and `value` properties.
   */
  getDepartmentDropdownOptions(): Option[] {
    return (
      this.departmentQuery()
        .data()
        ?.map((department) => ({
          label: department.departmentName,
          value: department.id,
        })) ?? []
    );
  }

  getResourceDropdownOptions(): Option[] {
    return (
      this.getResourcesQuery()
        .data()
        ?.data?.map((resource) => ({
          label: resource.resourceName,
          value: resource.resourceId,
        })) ?? []
    );
  }
  /**
   * Handles the resource selection change event and updates the selected resource IDs.
   *
   * @param {MultiSelectChangeEvent} event - The event object containing the selected resources.
   */

  onResourceChange(event: MultiSelectChangeEvent): void {
    this.selectedResourcesIds.set(
      event.value.map((option: Option) => option.value)
    );

  }

  /**
   * Handles the department selection change event and updates the selected departments and resources.
   *
   * @param {MultiSelectChangeEvent} event - The event object containing the selected departments.
   */
  onDepartmentChange(event: MultiSelectChangeEvent): void {
    const selectedDepartmentIds = event.value || [];
    this.selectedDepartments.set(selectedDepartmentIds);

    // Clear resources when departments change
    this.selectedResourcesIds.set([]);
    this.storedResources = [];
  }

  /**
   * Handles the department clear event
   */
  onDepartmentClear(): void {
    this.selectedDepartments.set([]);
    this.selectedResourcesIds.set([]);
    this.storedResources = [];
    this.pageSize = 10;
  }

  onStatusChange(event: { value: string }) {
    this.selectedStatus.set(event.value);
    // Filter storedResources to only include resources present in the new options
    const availableOptions = this.getResourceDropdownOptions();
    this.storedResources = this.storedResources.filter((opt) =>
      availableOptions.find((availableOpt) => availableOpt.value === opt.value)
    );
    this.selectedResourcesIds.set(this.storedResources.map((opt) => opt.value));
    this.pageSize = 10;

  }

  onPageChange(event: { rows: number }) {
    this.pageSize = event.rows;
  }
  


  /**
   * Computes the date range (start date, end date, and days in the month) for a given date.
   *
   * @param {Date} date - The date for which the range is computed.
   * @returns {{ startDate: Date, endDate: Date, daysInMonth: number }} An object containing the start date, end date, and number of days in the month.
   */
  private getDateRange(date: Date) {
    const year = date.getFullYear();
    const month = date.getMonth();

    return {
      startDate: new Date(year, month, 1), // Use local time, not UTC
      endDate: new Date(year, month + 1, 0, 23, 59, 59, 999), // Use local time, not UTC
      daysInMonth: getDaysInMonth(date),
    };
  }

  /**
   * Updates the table columns based on the selected date.
   *
   * @param {Date} date - The selected date.
   */
  private updateColumns(date: Date): void {
    const monthIndex = date.getMonth();
    const year = date.getFullYear();

    this.selectedMonthIndex = monthIndex;
    this.selectedYearNumber = year;

    this.selectedMonth.set(date.toLocaleString('default', { month: 'long' }));
    this.selectedYear.set(year.toString());

    const daysInMonth = new Date(year, monthIndex + 1, 0).getDate();
    const newCols: Column[] = [
      { field: 'name', header: 'Resource Name' },
      ...this.generateDayColumns(daysInMonth, year, monthIndex),
      { field: 'totalHours', header: 'Total Hours' },
    ];

    this.columnHeader.set(newCols);
  }

  /**
   * Generates the columns for each day of the month.
   *
   * @param {number} daysInMonth - The number of days in the month.
   * @param {number} year - The year of the selected date.
   * @param {number} monthIndex - The zero-based index of the month.
   * @returns {Column[]} An array of columns for each day of the month.
   */
  private generateDayColumns(
    daysInMonth: number,
    year: number,
    monthIndex: number
  ): Column[] {
    return Array.from({ length: daysInMonth }, (_, i) => {
      const currentDate = new Date(year, monthIndex, i + 1);
      const day = currentDate.toLocaleString('default', { weekday: 'short' });
      return {
        field: `day${i + 1}`,
        header: `${i + 1} ${day.charAt(0)}`,
      };
    });
  }

  getDateFromField(field: string): Date | null {
    const match = field.match(/^day(\d{1,2})$/);
    if (!match) return null;

    const day = Number(match[1]);

    return new Date(this.selectedYearNumber, this.selectedMonthIndex, day);
  }
  /**
   * Downloads the resource report based on the selected filters and resource IDs.
   */
  downloadResourceReport() {
    const resourceIds = this.selectedResourcesIds();
    const selectedDepartments = this.selectedDepartments();

    this.resourceReportService
      .fetchResourceReportDownloadUrl({
        queryString: this.createResourceIdQueryString(resourceIds),
        department:
          selectedDepartments.length > 0 ? selectedDepartments : undefined,
        month: this.selectedMonthIndex + 1,
        year: this.selectedYearNumber,
        resourceStatus: this.selectedStatus(),
        mode: 'actualHours',
      })
      .then((res) => {
        window.open(res.downloadUrl, '_blank');
      })
      .catch((err: unknown) => {
        const message =
          err instanceof Error
            ? `Some error occurred: ${err.message}`
            : 'Unknown error occurred while downloading report';
        console.error(message);
      });
  }
  /**
   * Clears all filters (billing type, department, status, resources) and resets the table to its default state.
   */
  clearFilters() {
    this.selectedDepartments.set([]);
    this.selectedStatus.set('');
    this.storedResources = [];
    const defaultDate = new Date(
      new Date().getFullYear(),
      new Date().getMonth()
    );
    this.date.set(defaultDate);
    this.selectedResourcesIds.set([]);
    this.departmentDropdown()?.resetFilter();
    this.statusDropdown()?.resetFilter();
    this.updateColumns(new Date());
    this.pageSize = 10;
  }

  onResourceClear() {
    this.selectedResourcesIds.set([]);
  }
  /**
   * Creates a query string from the selected resource IDs.
   *
   * @param {string[]} resourceIds - An array of resource IDs.
   * @returns {string} A query string with the resource IDs.
   */
  createResourceIdQueryString(resourceIds: string[]): string {
    return resourceIds
      .map((id) => `resourceIds=${encodeURIComponent(id)}`)
      .join('&');
  }
}
