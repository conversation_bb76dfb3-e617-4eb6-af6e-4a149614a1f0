<div class="flex flex-col gap-4">
  <div class="flex items-center flex-wrap justify-between gap-2">
    <div class="flex items-center flex-wrap gap-2">
      <p-dropdown
        #departmentDropdown
        placeholder="Department"
        [options]="getDepartmentDropdownOptions()"
        [(ngModel)]="selectedDepartment"
        (onChange)="onDepartmentChange($event)"
        optionLabel="label"
        optionValue="value"
        styleClass="w-36 rounded-lg"
        [filter]="true"
        filterPlaceholder="Department"
        [resetFilterOnHide]="true"
        [panelStyleClass]="'w-64'"
      />
      <p-multiSelect
        #resourceDropdown
        placeholder="Resources"
        filterPlaceholder="Resources"
        [options]="getResourceDropdownOptions()"
        [ngModel]="getResourcesQuery().isLoading() ? [] : storedResources"
        (ngModelChange)="storedResources = $event"
        optionLabel="label"
        id="resource"
        (onChange)="onResourceChange($event)"
        [loading]="getResourcesQuery().isLoading()"
        [emptyMessage]="'No Resource Found.'"
        styleClass="w-38 rounded-lg"
        [resetFilterOnHide]="true"
        [showClear]="true"
        (onClear)="onResourceClear(); resourceDropdown.hide()"
        [panelStyleClass]="'w-64'"
      >
        <ng-template let-option pTemplate="item">
          <div class="whitespace-normal text-base">
            {{ option.label }}
          </div>
        </ng-template>

        <ng-template pTemplate="clearicon">
          <i class="pi pi-filter-slash absolute right-1/2"></i>
        </ng-template>
      </p-multiSelect>

      <p-calendar
        [(ngModel)]="date"
        view="month"
        dateFormat="M yy"
        inputStyleClass="outline-none shadow-none ring-0"
        (onSelect)="onDateSelect($event)"
        [maxDate]="maxDate"
        [showIcon]="true"
        styleClass="w-36 rounded-lg"
      ></p-calendar>

      <p-dropdown
        #statusDropdown
        placeholder="Status"
        [options]="statusOptions"
        [(ngModel)]="selectedStatus"
        (onChange)="onStatusChange($event)"
        optionLabel="label"
        optionValue="value"
        styleClass="w-32 rounded-lg"
      />
      <p-button
        label="Clear Filters"
        aria-label="Clear all filters"
        icon="pi pi-filter-slash"
        (onClick)="clearFilters()"
        outlined="true"
      ></p-button>
      <p-button
        label="Download"
        aria-label="Download report"
        icon="pi pi-download"
        (onClick)="downloadResourceReport()"
        styleClass="p-button-primary"
        styleClass="w-32 rounded-lg"
      ></p-button>
    </div>
  </div>

  <div class="flex items-center flex-wrap gap-4">
    <div class="flex items-center gap-2">
      <div class="h-3 w-3 rounded-full bg-purple-200"></div>
      <span class="text-gray-500">Half day</span>
    </div>
    <div class="flex items-center gap-2">
      <div class="h-3 w-3 rounded-full bg-green-200"></div>
      <span class="text-gray-500">Full day</span>
    </div>
    <div class="flex items-center r gap-2">
      <div class="h-3 w-3 rounded-full bg-yellow-100"></div>
      <span class="text-gray-500">Weekend</span>
    </div>
    <div class="flex items-center gap-2">
      <div class="h-3 w-3 rounded-full bg-blue-200"></div>
      <span class="text-gray-500">Holiday </span>
    </div>
    <div class="flex items-center gap-2">
      <div class="h-3 w-3 rounded-full bg-red-200"></div>
      <span class="text-gray-500">Leave</span>
    </div>
    <div class="flex items-center gap-2">
      <div class="h-3 w-3 rounded-full bg-slate-100"></div>
      <span class="text-gray-500">Future Dates</span>
    </div>
  </div>

  <div class="w-full bg-white overflow-hidden shadow rounded-xl">
    <p-table
      #reportSheetTable
      [columns]="columnHeader()"
      [value]="fetchResourceDetails()"
      [tableStyle]="{ 'min-width': '50rem' }"
      [showLoader]="false"
      [paginator]="fetchResourceDetails().length > pageSize"
      [rows]="pageSize"
    >
      <ng-template pTemplate="header">
        <tr class="bg-transparent">
          @for (col of columnHeader(); track col.field) {
            <th class="p-1.5 text-center text-sm bg-primary-50">
              @if (col.field === 'name') {
                <div class="flex gap-2 text-start">
                  {{ col.header }}
                </div>
              } @else {
                <div class="flex flex-col">
                  <span>{{ col.header.split(' ')[0] }}</span>
                  <span>{{ col.header.split(' ')[1] }}</span>
                </div>
              }
            </th>
          }
        </tr>
      </ng-template>

      <ng-template pTemplate="body" let-resource>
        <tr class="hover:bg-gray-100">
          @for (col of columnHeader(); track col.field) {
            <td class="text-center py-2 px-0">
              @if (col.field === 'name') {
                <div class="flex items-center gap-2 text-sm text-start ml-1.5">
                  <span
                    #resourceName
                    class="truncate max-w-32"
                    [pTooltip]="
                      resourceName.offsetWidth < resourceName.scrollWidth
                        ? resource.resourceName
                        : ''
                    "
                    [tooltipStyleClass]="
                      'text-xs text-gray-700  whitespace-pre-wrap'
                    "
                    tooltipPosition="top"
                    >{{ resource.resourceName || '-' }}</span
                  >
                </div>
                <div class="text-gray-500 text-xs text-start ml-1.5 italic">
                  {{ resource.department }}
                </div>
              } @else if (col.field === 'id') {
                <div class="text-gray-700 text-sm">
                  {{ resource.kekaId || '-' }}
                </div>
              } @else if (col.field === 'totalHours') {
                <div class="text-sm font-semibold">
                  {{ convertMinutesToHours(resource.totalMinutes) || '-' }}
                </div>
              } @else {
                <tms-date-table
                  [worklogs]="resource.resourceWorklog"
                  [leaveDetails]="resource.leaveDetails"
                  [worklogDate]="getDateFromField(col.field)"
                ></tms-date-table>
              }
            </td>
          }
        </tr>
      </ng-template>

      <ng-template pTemplate="emptymessage">
        <tr>
          <td [attr.colspan]="columnHeader().length" class="text-center p-4">
            No worklogs found.
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="loadingbody">
        <tr>
          <td
            [attr.colspan]="columnHeader().length"
            class="text-center text-gray-500 p-4"
          >
            <i class="pi pi-spin pi-spinner mr-2"></i> Loading worklogs...
          </td>
        </tr>
        @for (_ of skeletonRows; track $index) {
          <tr>
            @for (col of columnHeader(); track $index) {
              <td class="text-sm">
                <p-skeleton width="100%" height="1.5rem"></p-skeleton>
              </td>
            }
          </tr>
        }
      </ng-template>
    </p-table>
  </div>

  <p-toast preventOpenDuplicates="true" />
  <p-confirmDialog />
</div>
