import { ComponentFixture, TestBed } from '@angular/core/testing';

import { provideHttpClient, withFetch } from '@angular/common/http';
import {
  provideTanStackQuery,
  QueryClient,
} from '@tanstack/angular-query-experimental';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { ContractFormComponent } from './contract-form.component';
import { Validators } from '@angular/forms';
import { ContractService } from '../../services/contract/contract.service';

let mockContractService: jasmine.SpyObj<ContractService>;

describe('ContractFormComponent', () => {
  mockContractService = jasmine.createSpyObj('ContractService', [
    'addContract',
    'contractsByProjectIdQuery',
  ]);
  let component: ContractFormComponent;
  let fixture: ComponentFixture<ContractFormComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ContractFormComponent],
      providers: [
        provideHttpClient(withFetch()),
        provideTanStackQuery(new QueryClient()),
        DynamicDialogRef,
        {
          provide: DynamicDialogConfig,
          useValue: {
            data: {
              project: { id: 'ad9e05db-4a42-4fa6-b939-0322722a27d2' },
            },
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ContractFormComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with default values', () => {
    const formValue = component.contractFormGroup.value;
    expect(formValue.contractId).toBe('');
    expect(formValue.startDate).toBe('');
    expect(formValue.endDate).toBeUndefined();
    expect(formValue.contractManagers).toEqual([]);
    expect(formValue.budgetSetting).toBe('');
    expect(formValue.billingSetting).toBeUndefined();
    expect(formValue.amount).toBeUndefined();
    expect(formValue.contractType).toBe('');
    expect(formValue.totalEstimatedEffort).toBeUndefined();
  });

  it('should have required validators on form controls', () => {
    const controls = component.contractFormGroup.controls;
    expect(controls['contractId'].hasValidator(Validators.required)).toBeTrue();
    expect(controls['startDate'].hasValidator(Validators.required)).toBeTrue();
    expect(controls['endDate'].hasValidator(Validators.required)).toBeTrue();
    expect(
      controls['contractManagers'].hasValidator(Validators.required)
    ).toBeTrue();
    expect(
      controls['budgetSetting'].hasValidator(Validators.required)
    ).toBeTrue();
  });

  it('should enable amount fields when budgetSetting is selected as Billable and Billing setting as fixed', () => {
    component.contractFormGroup
      .get('budgetSetting')
      ?.setValue({ name: 'Billable', value: true });
    component.contractFormGroup
      .get('billingSetting')
      ?.setValue({ name: 'Fixed', value: 'fixed' });
    expect(
      component.contractFormGroup.get('billingSetting')?.enabled
    ).toBeTrue();
    expect(component.contractFormGroup.get('amount')?.enabled).toBeTrue();
  });

  it('should disable amount fields when budgetSetting is selected as Billable and Billing setting as daily', () => {
    component.contractFormGroup
      .get('budgetSetting')
      ?.setValue({ name: 'Billable', value: true });
    component.contractFormGroup
      .get('billingSetting')
      ?.setValue({ name: 'Daily', value: 'daily' });
    expect(
      component.contractFormGroup.get('billingSetting')?.enabled
    ).toBeTrue();
    expect(component.contractFormGroup.get('amount')?.disabled).toBeTrue();
  });

  it('should disable amount fields when budgetSetting is selected as Billable and Billing setting as monthly', () => {
    component.contractFormGroup
      .get('budgetSetting')
      ?.setValue({ name: 'Billable', value: true });
    component.contractFormGroup
      .get('billingSetting')
      ?.setValue({ name: 'Monthly', value: 'monthly' });
    expect(
      component.contractFormGroup.get('billingSetting')?.enabled
    ).toBeTrue();
    expect(component.contractFormGroup.get('amount')?.disabled).toBeTrue();
  });

  it('should disable amount fields when budgetSetting is selected as Billable and Billing setting as quarterly', () => {
    component.contractFormGroup
      .get('budgetSetting')
      ?.setValue({ name: 'Billable', value: true });
    component.contractFormGroup
      .get('billingSetting')
      ?.setValue({ name: 'Quarterly', value: 'quarterly' });
    expect(
      component.contractFormGroup.get('billingSetting')?.enabled
    ).toBeTrue();
    expect(component.contractFormGroup.get('amount')?.disabled).toBeTrue();
  });

  it('should disable billingSetting and amount fields when budgetSetting is selected as Non-Billable', () => {
    component.contractFormGroup
      .get('budgetSetting')
      ?.setValue({ name: 'Non-Billable', value: false });
    expect(
      component.contractFormGroup.get('billingSetting')?.disabled
    ).toBeTrue();
    expect(component.contractFormGroup.get('amount')?.disabled).toBeTrue();
  });

  it('should enable endDate when startDate is selected', () => {
    const startDateControl = component.contractFormGroup.get('startDate');
    const endDateControl = component.contractFormGroup.get('endDate');

    startDateControl?.setValue(new Date('2024-01-01'));
    expect(endDateControl?.enabled).toBeTrue();
  });

  it('should clear endDate if startDate is after endDate', () => {
    const startDateControl = component.contractFormGroup.get('startDate');
    const endDateControl = component.contractFormGroup.get('endDate');

    startDateControl?.setValue(new Date('2024-02-01'));
    endDateControl?.setValue(new Date('2024-01-01'));
    startDateControl?.updateValueAndValidity();
    fixture.detectChanges();
    expect(endDateControl?.value).toBe('');
  });
});
