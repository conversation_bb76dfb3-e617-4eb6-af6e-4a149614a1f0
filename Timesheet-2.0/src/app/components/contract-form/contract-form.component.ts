import { Component, computed } from '@angular/core';
import {
  Form<PERSON>uilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { injectMutation } from '@tanstack/angular-query-experimental';
import { AvatarModule } from 'primeng/avatar';
import { BadgeModule } from 'primeng/badge';
import { ButtonModule } from 'primeng/button';
import { CalendarModule } from 'primeng/calendar';
import { DropdownModule } from 'primeng/dropdown';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { InputNumberInputEvent, InputNumberModule } from 'primeng/inputnumber';
import { InputTextModule } from 'primeng/inputtext';
import { MultiSelectModule } from 'primeng/multiselect';
import { AuthService } from '../../services/auth/auth-service';
import {
  ContractsById,
  CreateContract,
  UpdateContract,
} from '../../services/contract/contract.model';
import { ContractService } from '../../services/contract/contract.service';
import { FormSubmitButtonLabelService } from '../../services/formsubmitbuttonlabel/formsubmitbuttonlabel.service';
import { ProjectDetailsById, ProjectStatus } from '../../services/project/project.model';
import { ResourceService } from '../../services/resource/resource.service';
import { CONTRACT_ID_PATTERN, MAX_INT_VALUE } from '../../settings';
import { currencyLocaleCodes } from '../../constants/ISOCodes';
import { Option, ValidatorTypes } from '../../services/common.model';

export interface Resource {
  id: string;
  name: string;
  profilePicUrl?: string | null;
}
interface ContractFormData {
  contractId: string;
  startDate: Date;
  endDate: Date;
  contractManagers: Resource[];
  budgetSetting: BudgetSetting;
  billingSetting: BillingSetting;
  contractType: ContractTypeSelection;
  amount: number;
  totalEstimatedEffort: number;
}

type ErrorMessage = {
  [K in keyof ContractFormData]?: { [key in ValidatorTypes]?: string };
};

interface BudgetSetting {
  name: string;
  value: boolean;
}

interface BillingSetting {
  name: string;
  value: string;
}

interface ContractTypeSelection {
  name: 'Fixed' | 'Time and Material';
  value: 'fixed' | 'timeAndMaterial';
}
@Component({
  selector: 'tms-contract-form',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    DropdownModule,
    InputTextModule,
    CalendarModule,
    ButtonModule,
    AvatarModule,
    BadgeModule,
    InputNumberModule,
    MultiSelectModule,
  ],
  templateUrl: './contract-form.component.html',
})
export class ContractFormComponent {
  contractFormGroup: FormGroup;
  isSubmitting = false;
  isEditMode = false;
  project: ProjectDetailsById;
  resources = computed(() =>
    this.resourceService.resourcesQuery.data()?.map((resource) => ({
      id: resource.id,
      name: resource.name,
      profilePicUrl: resource.profilePicUrl,
    }))
  );
  contractManagers: Resource[] = [];
  contract: ContractsById;
  budgetSettingsOptions: BudgetSetting[] = [
    { name: 'Billable', value: true },
    { name: 'Non-Billable', value: false },
  ];
  billingSettingsOptions: BillingSetting[] = [
    { name: 'Fixed', value: 'fixed' },
    { name: 'Daily', value: 'daily' },
    { name: 'Monthly', value: 'monthly' },
    { name: 'Quarterly', value: 'quarterly' },
  ];

  contractType: ContractTypeSelection[] = [
    { name: 'Fixed', value: 'fixed' },
    { name: 'Time and Material', value: 'timeAndMaterial' },
  ];

  contractsQuery = computed(() =>
    this.contractService.contractsByProjectIdQuery(
      this.contract?.project.id ?? this.project?.id
    )
  );

  constructor(
    private authService: AuthService,
    private contractService: ContractService,
    private dialogRef: DynamicDialogRef,
    private formBuilder: FormBuilder,
    private dialogConfig: DynamicDialogConfig,
    private resourceService: ResourceService,
    public formSubmitButtonLabelService: FormSubmitButtonLabelService
  ) {
    this.project = this.dialogConfig.data?.project;
    this.contract = this.dialogConfig.data?.contract;
    this.isEditMode = !!this.contract;
    this.contractManagers =
      this.contract?.contractManagers?.map((manager) => ({
        id: manager.id ?? '',
        name: manager.name ?? '',
        profilePicUrl: manager.profilePicUrl ?? null,
      })) || [];

    this.contractFormGroup = this.initializeForm();
    this.setupFormListeners();
  }

  removeSelectedItem(manager: Resource, event: Event) {
    event.stopPropagation();

    const currentValue =
      this.contractFormGroup.get('contractManagers')?.value || [];
    const updatedValue = currentValue.filter(
      (item: Resource) => item.id !== manager.id
    );

    this.contractFormGroup.patchValue({
      contractManagers: updatedValue,
    });
  }

  private initializeForm(): FormGroup {
    // First find the matching options from the arrays

    const selectedBudgetSetting = this.budgetSettingsOptions.find(
      (option) =>
        option.value ===
        (this.contract?.contractBudgetDetail?.[0]?.isBillable ? true : false)
    );

    const selectedBillingSetting = this.billingSettingsOptions.find(
      (option) =>
        option.value ===
        this.contract?.contractBudgetDetail?.[0]?.billingSettings
    );

    const selectedContractType = this.contractType.find(
      (option) => option.value === this.contract?.contractTypeSelection
    );

    const form = this.formBuilder.group({
      contractId: [this.contract?.contractId ?? '', Validators.required],
      startDate: [
        this.contract?.startDate ? new Date(this.contract?.startDate) : '',
        Validators.required,
      ],
      endDate: [
        {
          value: this.contract?.endDate ? new Date(this.contract?.endDate) : '',
          disabled: !this.contract?.startDate,
        },
        Validators.required,
      ],

      contractManagers: [this.contractManagers, Validators.required],
      budgetSetting: [
        this.contract ? selectedBudgetSetting : '',
        Validators.required,
      ],
      billingSetting: [
        {
          value: selectedBillingSetting || '',
          disabled: !selectedBudgetSetting?.value,
        },
      ],
      amount: [
        {
          value: this.contract?.contractBudgetDetail?.[0]?.amount ?? '',
          disabled: selectedBillingSetting?.value !== 'fixed',
        },
      ],
      contractType: [selectedContractType ?? '', Validators.required],
      totalEstimatedEffort: [
        {
          value: this.contract?.totalEstimatedEffort ?? '',
          disabled: selectedContractType?.value !== 'fixed',
        },
      ],
    });

    // If we have a contract and it's billable, enable the billing setting
    if (this.contract?.contractBudgetDetail?.[0]?.isBillable) {
      form.get('billingSetting')?.enable();
    }

    // If we have a contract with fixed billing, enable the amount field
    if (this.contract?.contractBudgetDetail?.[0]?.billingSettings === 'fixed') {
      form.get('amount')?.enable();
    }

    // If we have a contract with fixed contract type, enable totalEstimatedEffort
    if (this.contract?.contractTypeSelection === 'fixed') {
      form.get('totalEstimatedEffort')?.enable();
    }

    return form;
  }

  /**
   * Sets up reactive form listeners to handle dynamic validation and control enabling/disabling
   * based on user input for specific fields in the contract form.
   */
  private setupFormListeners(): void {
    this.setupBudgetSettingListener();
    this.setupBillingSettingListener();
    this.setupContractTypeListener();
    this.setupStartDateListener();
  }

  /**
   * Listens for changes on the 'budgetSetting' form control.
   * Depending on the selected budget setting option, it dynamically updates the validators
   * and enables/disables 'billingSetting' and 'amount' fields.
   */
  private setupBudgetSettingListener(): void {
    this.contractFormGroup
      .get('budgetSetting')
      ?.valueChanges.subscribe((option: BudgetSetting) => {
        // Retrieve references to the related form controls
        const billingSetting = this.contractFormGroup.get('billingSetting');
        const amount = this.contractFormGroup.get('amount');

        if (option?.value) {
          // If 'budgetSetting' has a valid value:
          billingSetting?.enable();

          // - Apply validation rules
          billingSetting?.setValidators(Validators.required);
          amount?.setValidators([Validators.required, Validators.min(1)]);
        } else {
          // If 'budgetSetting' has no value:
          // - Clear the values of 'billingSetting' and 'amount'
          this.contractFormGroup.patchValue({ billingSetting: '', amount: '' });

          // - Disable 'billingSetting' and 'amount' controls
          billingSetting?.disable();
          amount?.disable();

          // - Remove any previously set validators
          billingSetting?.clearValidators();
          amount?.clearValidators();
        }
        // - Update the  field to re-evaluate validations
        billingSetting?.updateValueAndValidity();
        amount?.updateValueAndValidity();
      });
  }

  /**
   * Listens for changes on the 'billingSetting' form control.
   * Enables and sets validation for the 'amount' control when a 'fixed' billingSetting is selected
   */
  private setupBillingSettingListener(): void {
    this.contractFormGroup
      .get('billingSetting')
      ?.valueChanges.subscribe((billingSetting: BillingSetting) => {
        const amount = this.contractFormGroup.get('amount');
        if (billingSetting?.value === 'fixed') {
          amount?.enable();
          amount?.setValidators([
            Validators.required,
            Validators.min(1),
            Validators.max(MAX_INT_VALUE),
          ]);
        } else {
          amount?.setValue('');
          amount?.disable();
          amount?.clearValidators();
        }
        // - Update the  field to re-evaluate validations
        amount?.updateValueAndValidity();
      });
  }

  /**
   * Listens for changes on the 'contractType' form control.
   * Enables and sets validation for the 'totalEstimatedEffort' control when a valid 'contractType' is selected as 'fixed'.
   */
  private setupContractTypeListener(): void {
    this.contractFormGroup
      .get('contractType')
      ?.valueChanges.subscribe((contractType: ContractTypeSelection) => {
        const totalEstimatedEffort = this.contractFormGroup.get(
          'totalEstimatedEffort'
        );
        if (contractType?.value === 'fixed') {
          totalEstimatedEffort?.enable();
          totalEstimatedEffort?.setValidators([
            Validators.required,
            Validators.min(1),
          ]);
        } else {
          totalEstimatedEffort?.setValue('');
          totalEstimatedEffort?.disable();
          totalEstimatedEffort?.clearValidators();
        }
        totalEstimatedEffort?.updateValueAndValidity();
      });
  }

  private setupStartDateListener(): void {
    /**
     * Listens for changes on the 'startDate' form control.
     * Enables and sets validation for the 'endDate' control when a valid 'startDate' is provided.
     */
    this.contractFormGroup
      .get('startDate')
      ?.valueChanges.subscribe((startDateValue) => {
        // Retrieve reference to the 'endDate' form control
        const endDateControl = this.contractFormGroup.get('endDate');
        if (startDateValue) {
          // - Enable the 'endDate' control
          endDateControl?.enable();
        }
        // If start date is more than end date clear the end date
        if (endDateControl?.value && startDateValue > endDateControl.value) {
          endDateControl?.setValue('');
        }
        // - Update the  field to re-evaluate validations
        endDateControl?.updateValueAndValidity();
      });
  }

  onSubmit() {
    if (this.contractFormGroup.valid) {
      this.isSubmitting = true;
      const formValue: ContractFormData = this.contractFormGroup.value;
      const startDate = formValue.startDate;
      const endDate = formValue.endDate;
      const currentDate = new Date();

      // Normalize both dates to the start of the day (00:00:00) to compare only the date part
      endDate.setHours(0, 0, 0, 0); 
      currentDate.setHours(0, 0, 0, 0);  

      // Determine contract status based on the end date and start date
      let contractStatus:ProjectStatus = ProjectStatus.active;
      if(startDate > currentDate || endDate < currentDate)
      {
        contractStatus = ProjectStatus.inActive;
      }
      else{
        contractStatus = ProjectStatus.active;
      }

      const contractData = {
        customContractId: formValue.contractId.trim(),
        startDate: formValue.startDate.toISOString(),
        endDate: formValue.endDate.toISOString(),
        contractStatus:contractStatus,
        projectId: this.contract?.project?.id ?? this.project?.id ?? '',
        assignedBy: this.authService.userId(),
        contractManagerIds: formValue.contractManagers.map(
          (manager) => manager.id
        ),
        resourceIds: this.contract?.contractResources ?? [],
        isBillable: formValue.budgetSetting.value,
        amount: formValue.amount ?? null,
        billingSetting: formValue.billingSetting?.value ?? null,
        contractTypeSelection: formValue.contractType?.value ?? null,
        totalEstimatedEffort: formValue.totalEstimatedEffort ?? null,
      };

      this.isEditMode
        ? this.updateContractQuery.mutate(contractData)
        : this.createContractQuery.mutate(contractData);
      this.contractFormGroup.disable();
    }
  }

  /**
   * Mutation hook for create a new contract.
   * It triggers a mutation to create a new contract and handles success/error responses.
   */
  createContractQuery = injectMutation(() => ({
    mutationKey: ['contract', this.authService.userId()],
    mutationFn: (contractData: CreateContract) =>
      this.contractService.addContract(contractData),
    onSuccess: (data) => {
      this.contractsQuery().refetch();
      this.dialogRef.close({
        type: 'success',
        severity: data.status,
        message: data.message,
        summary: 'Contract created.',
      });
    },
    onError: (error) => {
      console.error('Error creating contract:', error);
      this.dialogRef.close({
        type: 'error',
        severity: 'error',
        message: error.message,
        summary: 'Error',
      });
    },
  }));

  /**
   * Mutation hook for create a new contract.
   * It triggers a mutation to create a new contract and handles success/error responses.
   */
  updateContractQuery = injectMutation(() => ({
    mutationKey: ['contract', this.authService.userId()],
    mutationFn: (contractData: UpdateContract) =>
      this.contractService.updateContract(contractData, this.contract?.id),
    onSuccess: (data) => {
      this.contractsQuery().refetch();
      this.dialogRef.close({
        type: 'success',
        severity: data.status,
        message: data.message,
        summary: 'Contract updated.',
      });
    },
    onError: (error) => {
      console.error('Error updating contract:', error);
      this.dialogRef.close({
        type: 'error',
        severity: 'error',
        message: error.message,
        summary: 'Error',
      });
    },
  }));

  get isSubmitButtonDisabled() {
    return (
      this.contractFormGroup.invalid ||
      this.isSubmitting ||
      !this.contractFormGroup.get('contractId')?.value.trim()
    );
  }

  /**
   * Check for errors in the form and return an error message for a specific control.
   */
  checkErrors(fieldName: keyof ContractFormData): string | null {
    const control = this.contractFormGroup.get(fieldName);
    const errorMessage: ErrorMessage = {
      amount: {
        min: 'Amount cannot be zero or negative.',
        max: `Amount entered is too large.`,
      },
      totalEstimatedEffort: {
        min: 'Efforts cannot be zero.',
      },
      contractId: {
        pattern:
          'Contract ID can only contain: letters, numbers, - _ / # space & .',
      },
    };

    if (control?.invalid && control?.touched && control.errors) {
      const fieldErrors = errorMessage[fieldName];
      const firstErrorKey = Object.keys(control.errors)[0];

      return fieldErrors?.[firstErrorKey as ValidatorTypes] ?? null;
    }

    return null;
  }

  onAmountInput(event: InputNumberInputEvent): void {
    const inputValue = Number(event.value);
    if (inputValue === 0) {
      this.contractFormGroup.get('amount')?.setErrors({ min: true });
    } else if (inputValue > MAX_INT_VALUE) {
      this.contractFormGroup.get('amount')?.setErrors({ max: true });
    }
  }
  onEffortsInput(event: InputNumberInputEvent): void {
    const inputValue = event.value;
    if (inputValue === 0) {
      this.contractFormGroup
        .get('totalEstimatedEffort')
        ?.setErrors({ min: true });
    }
  }

  onEffortsBlur(event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    const inputValue = +inputElement.value;
    if (inputValue === 0) {
      this.contractFormGroup
        .get('totalEstimatedEffort')
        ?.setErrors({ min: true });
    }
  }

  onAmountBlur(event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    const inputValue = +inputElement.value;
    if (inputValue === 0) {
      this.contractFormGroup.get('amount')?.setErrors({ min: true });
    }
  }

  onContractIdInput(event: Event) {
    const inputElement = event.target as HTMLInputElement;
    const inputValue = inputElement.value;
    const control = this.contractFormGroup.get('contractId');
    if (!CONTRACT_ID_PATTERN.test(inputValue)) {
      control?.setErrors({ pattern: true });
      control?.markAsTouched();
    }
  }

  getCurrencyCode(): Option {
    const clientCurrency = this.project.client?.currency;
    const defaultCurrency: Option = {
      label: 'INR',
      value: 'en-IN',
    };

    if (!clientCurrency) {
      return defaultCurrency;
    }

    return (
      currencyLocaleCodes.find((code) => code.label === clientCurrency) ??
      defaultCurrency
    );
  }
}
