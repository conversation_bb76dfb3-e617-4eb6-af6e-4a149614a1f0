<form
  [formGroup]="contractFormGroup"
  (ngSubmit)="onSubmit()"
  class="flex flex-col space-y-6 w-full"
>
  <div class="flex flex-col gap-2">
    <label for="contractId" class="text-sm text-neutral-500"
      >Contract ID<span class="font-bold text-red-500">*</span></label
    >
    <input
      pInputText
      id="contractId"
      formControlName="contractId"
      placeholder="Enter contract ID"
      [style]="{ width: '100%' }"
      (input)="onContractIdInput($event)"
    />
    @if (checkErrors('contractId')) {
      <small class="text-xs text-red-500">{{
        checkErrors('contractId')
      }}</small>
    }
  </div>

  <div class="flex flex-col gap-2">
    <label for="contractManagers" class="text-sm text-neutral-500">
      Contract Managers<span class="font-bold text-red-500">*</span>
    </label>
    <p-multiSelect
      formControlName="contractManagers"
      [options]="resources()"
      optionLabel="name"
      id="contractManagers"
      placeholder="Select employee"
      [filter]="true"
      filterBy="name"
      [showClear]="true"
      [style]="{ width: '100%' }"
      display="chip"
      [resetFilterOnHide]="true"
      [showClear]="false"
    >
      <!-- Selected Items Template -->
      <ng-template let-value pTemplate="selectedItems">
        <div class="flex items-center justify-center w-max">
          @for (option of value; track $index) {
            <div class="inline-flex align-items-center gap-2 px-1">
              <div
                class="flex items-center justify-center space-x-2 bg-gray-50 rounded-full px-2 py-0.5"
              >
                @if (option.profilePicUrl) {
                  <p-avatar [image]="option.profilePicUrl" shape="circle" />
                } @else {
                  <p-avatar [label]="option.name.charAt(0)" shape="circle" />
                }
                <div>{{ option.name }}</div>
                <i
                  class="pi pi-times cursor-pointer hover:bg-gray-200 p-1.5 rounded-full transition-colors"
                  (click)="removeSelectedItem(option, $event)"
                  aria-hidden="true"
                ></i>
              </div>
            </div>
          }
        </div>

        @if (!value || value.length === 0) {
          <div>Select employee</div>
        }
      </ng-template>

      <!-- Template for dropdown options -->
      <ng-template pTemplate="item" let-resource>
        <div class="flex items-center space-x-2">
          @if (resource.profilePicUrl) {
            <p-avatar [image]="resource.profilePicUrl" shape="circle" />
          } @else {
            <p-avatar [label]="resource.name.charAt(0)" shape="circle" />
          }
          <div>{{ resource.name }}</div>
        </div>
      </ng-template>
    </p-multiSelect>
  </div>

  <div class="grid md:grid-cols-2 gap-4">
    <div class="flex flex-col gap-2 w-full">
      <label for="budgetSetting" class="text-sm text-neutral-500">
        Budget settings<span class="font-bold text-red-500">*</span>
      </label>
      <p-dropdown
        #budget
        formControlName="budgetSetting"
        [options]="budgetSettingsOptions"
        optionLabel="name"
        placeholder="Select budget settings"
        [style]="{ width: '100%' }"
      />
    </div>

    <div class="flex flex-col gap-2 w-full">
      <label for="billingSetting" class="text-sm text-neutral-500">
        Billing settings
        @if (contractFormGroup.get('billingSetting')?.enabled) {
          <span class="font-bold text-red-500">*</span>
        }
      </label>
      <p-dropdown
        formControlName="billingSetting"
        [options]="billingSettingsOptions"
        optionLabel="name"
        placeholder="Select billing settings"
        [style]="{ width: '100%' }"
      />
    </div>
  </div>

  <div class="flex flex-col gap-2 w-full">
    <label for="amount" class="text-sm text-neutral-500">
      Contract Amount ({{ project.client?.currency ?? 'INR' }})
      @if (contractFormGroup.get('amount')?.enabled) {
        <span class="font-bold text-red-500">*</span>
      }
    </label>
    <p-inputNumber
      id="amount"
      inputId="integeronly"
      formControlName="amount"
      [min]="0"
      placeholder="Enter amount"
      [style]="{ width: '100%' }"
      [maxlength]="10"
      [locale]="getCurrencyCode().value"
      (onInput)="onAmountInput($event)"
      (onBlur)="onAmountBlur($event)"
    >
    </p-inputNumber>
    @if (checkErrors('amount')) {
      <small class="text-xs text-red-500">{{ checkErrors('amount') }}</small>
    }
  </div>

  <div class="grid md:grid-cols-2 gap-2">
    <div class="flex flex-col gap-2 w-full">
      <label for="contractType" class="text-sm text-neutral-500">
        Contract Type<span class="font-bold text-red-500">*</span>
      </label>
      <p-dropdown
        formControlName="contractType"
        [options]="contractType"
        optionLabel="name"
        placeholder="Select contract type"
        [style]="{ width: '100%' }"
      />
    </div>

    <div class="flex flex-col gap-2 w-full">
      <label for="totalEstimatedEffort" class="text-sm text-neutral-500">
        Total Estimated Effort
        @if (contractFormGroup.get('totalEstimatedEffort')?.enabled) {
          <span class="font-bold text-red-500">*</span>
        }
      </label>
      <p-inputNumber
        id="totalEstimatedEffort"
        inputId="integeronly"
        [min]="0"
        formControlName="totalEstimatedEffort"
        placeholder="Enter estimated effort"
        [style]="{ width: '100%' }"
        [maxlength]="4"
        (onInput)="onEffortsInput($event)"
        (onBlur)="onEffortsBlur($event)"
      >
      </p-inputNumber>
      @if (checkErrors('totalEstimatedEffort')) {
        <small class="text-xs text-red-500">{{
          checkErrors('totalEstimatedEffort')
        }}</small>
      }
    </div>
  </div>

  <div class="grid md:grid-cols-2 gap-2">
    <div class="flex flex-col gap-2">
      <label for="startDate" class="text-sm text-neutral-500"
        >Start Date<span class="font-bold text-red-500">*</span></label
      >
      <p-calendar
        #startDate
        formControlName="startDate"
        id="startDate"
        placeholder="Select start date"
        [appendTo]="'body'"
        [contentEditable]="false"
        [readonlyInput]="true"
        [showIcon]="true"
        [iconDisplay]="'input'"
        dateFormat="dd/mm/yy"
        [style]="{ width: '100%' }"
      ></p-calendar>
    </div>

    <div class="flex flex-col gap-2">
      <label for="endDate" class="text-sm text-neutral-500"
        >End Date<span class="font-bold text-red-500">*</span></label
      >
      <p-calendar
        formControlName="endDate"
        id="endDate"
        placeholder="Select end date"
        [appendTo]="'body'"
        [contentEditable]="false"
        [readonlyInput]="true"
        [showIcon]="true"
        [iconDisplay]="'input'"
        dateFormat="dd/mm/yy"
        [minDate]="startDate.value"
        [style]="{ width: '100%' }"
      ></p-calendar>
    </div>
  </div>

  <div class="flex flex-row-reverse">
    <p-button
      type="submit"
      [label]="
        formSubmitButtonLabelService.getFormSubmitButtonLabel(
          isSubmitting,
          isEditMode
        )
      "
      [icon]="
        formSubmitButtonLabelService.getFormSubmitButtonIcon(isSubmitting)
      "
      [disabled]="isSubmitButtonDisabled"
      styleClass="submit-button disabled:cursor-not-allowed disabled:bg-neutral-400 disabled:border-neutral-400"
    ></p-button>
  </div>
</form>
