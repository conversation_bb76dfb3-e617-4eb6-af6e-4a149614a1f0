<div
  tmsWindowResize
  (onResize)="onWindowResize()"
  class="bg-white text-neutral-700 transition-all duration-300 ease-in-out overflow-hidden"
  [ngClass]="{ 'md:min-w-56 md:max-w-56': isSidebarVisible() }"
>
  <p-sidebar
    #sidebarElement
    [visible]="isSidebarVisible()"
    styleClass="p-sidebar-sm"
    [modal]="isSmallScreen()"
    [dismissible]="isSmallScreen()"
    (onHide)="closeSidebar()"
  >
    <ng-template pTemplate="headless">
      <div class="flex flex-col h-full bg-white text-neutral-700 p-3 py-4">
        <div class="flex items-end justify-end">
          <p-button
            icon="pi pi-times"
            [rounded]="true"
            [text]="true"
            [severity]="'secondary'"
            class="ml-3 text-neutral-500"
            (click)="closeSidebar()"
            [attr.aria-label]="'Close sidebar'"
          />
        </div>
        <div class="w-full flex items-baseline justify-between px-4 mb-6">
          <span class="text-2xl font-bold text-primary-500">Time Manage</span>
          <span class="text-primary-500 text-xs ml-1">{{
            releaseVersion || ''
          }}</span>
        </div>
        <div class="overflow-y-auto">
          @for (section of menuItems(); track $index) {
            <div class="mb-4">
              <h3 class="text-xs font-semibold text-neutral-500 px-4 mb-2">
                {{ section.title }}
              </h3>
              <ul class="list-none p-0 m-0">
                @for (item of section.items; track $index) {
                  <li>
                    <a
                      [routerLink]="item.url"
                      routerLinkActive="font-bold bg-primary-100 text-primary-500"
                      class="flex items-center px-4 py-3 text-sm cursor-pointer transition-colors duration-150 hover:bg-neutral-100 rounded-md w-full"
                      (click)="
                        toggleSubmenu(item); isSmallScreen() && closeSidebar()
                      "
                    >
                      <i
                        [class]="item.icon + ' mr-3 text-lg text-primary-500'"
                      ></i>
                      <span>{{ item.label }}</span>
                      <div class="ml-3">
                        @if (item.badge) {
                          <p-badge
                            class="text-sm"
                            [value]="item.badge"
                            severity="info"
                          />
                        }
                      </div>
                      @if (item.items) {
                        <i
                          class="pi pi-chevron-down ml-auto"
                          [class.rotate-180]="item.expanded"
                        ></i>
                      }
                    </a>
                    @if (item.items && item.expanded) {
                      <ul
                        class="list-none pl-6"
                        [@submenuAnimation]="
                          item.expanded ? 'expanded' : 'collapsed'
                        "
                      >
                        @for (subItem of item.items; track $index) {
                          <li>
                            <a
                              [routerLink]="subItem.url"
                              routerLinkActive="font-bold bg-primary-100 text-primary-500"
                              class="flex items-center px-4 py-2 text-sm hover:bg-gray-100 cursor-pointer transition-colors duration-150 rounded-md"
                            >
                              <i
                                [class]="
                                  subItem.icon +
                                  ' mr-3 text-lg text-primary-500'
                                "
                              ></i>
                              <span> {{ subItem.label }}</span>
                            </a>
                          </li>
                        }
                      </ul>
                    }
                  </li>
                }
              </ul>
            </div>
          }
        </div>
        <!--Notifications and profile icon in sidebar-->
        <!--TODO: Implement the functionality for notifications-->
        <div class="mt-auto px-1 pt-3 w-full">
          @if (role() !== 'employee') {
            <div
              class="flex items-center justify-between px-4 py-3 text-sm cursor-pointer transition-colors duration-150 hover:bg-neutral-100 rounded-md w-full"
            >
              <div class="flex items-center">
                <i class="pi pi-bell text-lg text-primary-500 mr-3"></i>
                <span>Notifications</span>
              </div>
            </div>
          }

          <!-- This is a temporary button to switch back to Timesheet-v1 -->

          <a
            [href]="timesheetV1Url"
            routerLinkActive="font-bold bg-primary-100 text-primary-500"
            class="rounded-md"
          >
            <div
              class="flex items-center justify-center px-4 py-3 my-2 text-sm cursor-pointer transition-colors duration-150 bg-primary-500 hover:bg-primary-600 rounded-md w-full"
            >
              <i
                class="pi pi-arrow-right-arrow-left mr-3 text-sm text-white"
              ></i>
              <span class="text-white">Switch to Timesheet V1</span>
            </div>
          </a>

          <p-divider styleClass="mt-1"></p-divider>
          <div
            (mouseup)="menu.toggle($event)"
            class="flex max-w-48 items-center px-1 py-1 text-sm cursor-pointer transition-colors duration-150 hover:bg-neutral-100 rounded-md"
          >
            <p-avatar
              [image]="userDetails()?.profilePicurl ?? undefined"
              [icon]="userDetails()?.profilePicurl ? '' : 'pi pi-user'"
              shape="circle"
              styleClass="bg-primary-500 text-white mr-3 size-10"
            ></p-avatar>
            <div class="flex flex-col overflow-hidden">
              <p class="text-sm font-medium text-neutral-800 truncate">
                {{
                  stringUtilsService.capitalizeFirstLetter(userDetails()?.name)
                }}
              </p>
              <p class="text-sm text-neutral-500 truncate">
                {{ userDetails()?.email }}
              </p>
            </div>
          </div>
        </div>
      </div>
      <p-menu #menu [model]="profileMenuItems" [popup]="true"></p-menu>
    </ng-template>
  </p-sidebar>
</div>
