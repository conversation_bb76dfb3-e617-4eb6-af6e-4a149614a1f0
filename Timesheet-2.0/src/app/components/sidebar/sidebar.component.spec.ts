import { ComponentFixture, TestBed } from '@angular/core/testing';
import { SidebarComponent } from './sidebar.component';
import { MenuItem } from 'primeng/api';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ActivatedRoute, RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { BadgeModule } from 'primeng/badge';
import { ButtonModule } from 'primeng/button';
import { RippleModule } from 'primeng/ripple';
import { SidebarModule } from 'primeng/sidebar';
import { of } from 'rxjs';
import { HttpClientModule } from '@angular/common/http';
import { ComponentRef } from '@angular/core';
import { QueryClient } from '@tanstack/angular-query-experimental';

describe('SidebarComponent', () => {
  let component: SidebarComponent;
  let componentRef: ComponentRef<SidebarComponent>;
  let fixture: ComponentFixture<SidebarComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        SidebarModule,
        RippleModule,
        ButtonModule,
        RouterModule,
        BadgeModule,
        CommonModule,
        BrowserModule,
        BrowserAnimationsModule,
        HttpClientModule,
      ],
      providers: [
        QueryClient,
        {
          provide: ActivatedRoute,
          useValue: {
            params: of({}),
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(SidebarComponent);
    component = fixture.componentInstance;
    componentRef = fixture.componentRef;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should display menu items when provided', () => {
    component.isSidebarVisible.set(true);
    const menuItems: MenuItem[] = [
      {
        title: 'Group 1',
        items: [
          {
            label: 'Item 1',
            icon: 'pi pi-objects-column',
            url: '/dashboard',
          },
          { label: 'Item 2', icon: 'pi pi-stopwatch', url: '/tracker' },
        ],
      },
    ];
    componentRef.setInput('menuItems', menuItems);
    component.isSidebarVisible.set(true);
    fixture.detectChanges();

    const itemElements = fixture.nativeElement.querySelectorAll('li');
    expect(itemElements.length).toBe(2);
    expect(itemElements[0].textContent).toContain('Item 1');
    expect(itemElements[1].textContent).toContain('Item 2');
  });

  it('should toggle submenu visibility when clicked', () => {
    component.isSidebarVisible.set(true);
    const menuItems: MenuItem[] = [
      {
        title: 'DASHBOARDS',
        items: [
          {
            label: 'Dashboard',
            icon: 'pi pi-objects-column',
            url: '/dashboard',
            expanded: false, 
          },
          { label: 'Tracker', icon: 'pi pi-stopwatch', url: '/tracker' },
        ],
      },
    ];

    componentRef.setInput('menuItems', menuItems);
    component.isSidebarVisible.set(true); 
    fixture.detectChanges();

    const linkElement = fixture.nativeElement.querySelector(
      'a.flex.items-center'
    );
    expect(linkElement).toBeTruthy();

    linkElement.click();
    fixture.detectChanges();

    expect(menuItems[0].items![0].expanded).toBeTrue();

    linkElement.click();
    fixture.detectChanges();

    expect(menuItems[0].items![0].expanded).toBeFalse();
  });

  it('should reflect sidebar visibility based on isSidebarVisible input', () => {
    component.isSidebarVisible.set(true);
    fixture.detectChanges();
    const sidebarElement = fixture.nativeElement.querySelector('.p-sidebar-sm');
    expect(sidebarElement).toBeTruthy();
  });
});
