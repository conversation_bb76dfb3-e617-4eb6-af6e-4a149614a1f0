import {
  animate,
  state,
  style,
  transition,
  trigger,
} from '@angular/animations';
import { CommonModule } from '@angular/common';
import {
  Component,
  computed,
  input,
  signal,
  ViewEncapsulation,
} from '@angular/core';
import { Router, RouterModule } from '@angular/router';
import { MenuItem } from 'primeng/api';
import { AvatarModule } from 'primeng/avatar';
import { BadgeModule } from 'primeng/badge';
import { ButtonModule } from 'primeng/button';
import { DividerModule } from 'primeng/divider';
import { MenuModule } from 'primeng/menu';
import { RippleModule } from 'primeng/ripple';
import { SidebarModule } from 'primeng/sidebar';
import { WindowResizeDirective } from '../../directives/window-resize/window-resize.directive';
import { AuthService } from '../../services/auth/auth-service';
import { ResourceService } from '../../services/profile/profile.service';
import { StringUtilsService } from '../../services/stringutils/stringutils.service';
import { SELECTED_PROJECTS } from '../../settings';
import { environment } from '../../../environments/environment';

@Component({
  selector: 'tms-sidebar',
  templateUrl: './sidebar.component.html',
  standalone: true,
  imports: [
    SidebarModule,
    RippleModule,
    ButtonModule,
    AvatarModule,
    RouterModule,
    BadgeModule,
    CommonModule,
    WindowResizeDirective,
    MenuModule,
    DividerModule,
  ],
  styleUrls: ['./sidebar.component.css'],
  animations: [
    trigger('submenuAnimation', [
      state(
        'collapsed',
        style({
          height: '0',
          overflow: 'hidden',
        })
      ),
      state(
        'expanded',
        style({
          height: '*',
        })
      ),
      transition('collapsed <=> expanded', animate('200ms ease-in-out')),
    ]),
  ],
  encapsulation: ViewEncapsulation.None,
})
export class SidebarComponent {
  menuItems = input<MenuItem[]>([]);

  profileMenuItems: MenuItem[] | undefined = [
    {
      label: 'View Profile',
      icon: 'pi pi-user',
      command: () => {
        if (this.isSmallScreen()) this.closeSidebar();
        this.navigateToProfile();
      },
    },
    {
      separator: true,
    },
    { label: 'Logout', icon: 'pi pi-sign-out', command: () => this.logout() },
  ];
  private windowWidth = signal<number>(window.innerWidth);
  /**
   * Signal indicating whether the screen size is small (less than 768px).
   * Used to adjust the sidebar's behavior responsively.
   */
  isSmallScreen = computed(() => this.windowWidth() < 768);

  /**
   * Signal controlling the visibility of the sidebar.
   */
  isSidebarVisible = signal<boolean>(false);

  role = computed(() => this.authService.userRole());

  timesheetV1Url = environment.timesheetV1Url || '#';

  releaseVersion = `v${environment.version}`;

  constructor(
    private router: Router,
    private authService: AuthService,
    private resourceService: ResourceService,
    public stringUtilsService: StringUtilsService
  ) {}

  userDetails = computed(() =>
    this.resourceService.ResourceDetailsQuery.data()
  );
  /**
   * Handles window resize events emitted by the directive.
   */
  onWindowResize() {
    this.windowWidth.set(window.innerWidth);
    this.isSmallScreen = computed(() => this.windowWidth() < 768);
    if (this.isSmallScreen()) {
      this.isSidebarVisible.set(false);
    }
  }

  /**
   * Toggles the visibility of the sidebar.
   */
  public toggleSidebar() {
    this.isSidebarVisible.update((visible) => !visible);
  }

  /**
   * Closes the sidebar by setting its visibility to `false`.
   */
  public closeSidebar() {
    this.isSidebarVisible.set(false);
  }

  /**
   * Toggles the expanded state of a submenu item.
   */
  public toggleSubmenu(item: MenuItem) {
    item.expanded = !item.expanded;
  }

  /**
   * Function for navigating to the profile page.
   */
  navigateToProfile() {
    this.router.navigate(['/dashboard/profile']);
  }

  /**
   * Function for logging out the user.
   * Logout will clear the cookies and will redirect to the login page
   */
  logout() {
    if (localStorage.getItem(SELECTED_PROJECTS) !== null) {
      localStorage.removeItem(SELECTED_PROJECTS);
    }
    this.authService.logOut().subscribe(() => {
      this.router.navigate(['/login']); // Redirect to the login page after logout
    });
  }
}
