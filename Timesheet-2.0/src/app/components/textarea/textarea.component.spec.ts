import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import {
  ReactiveFormsModule,
  FormGroup,
  FormControl,
  Validators,
} from '@angular/forms';
import { TextareaComponent } from './textarea.component';
import { Component } from '@angular/core';

@Component({
  template: `
    <form [formGroup]="formGroup">
      <app-textarea
        formControlName="message"
        [placeholder]="placeholder"
        [maxLength]="maxLength"
        [required]="required"
      ></app-textarea>
    </form>
  `,
})
class TestHostComponent {
  placeholder = 'Enter text here...';
  maxLength = 10;
  required = true;

  formGroup = new FormGroup({
    message: new FormControl('', [
      Validators.required,
      Validators.maxLength(10),
    ]),
  });
}

describe('TextareaComponent within a form', () => {
  let hostComponent: TestHostComponent;
  let hostFixture: ComponentFixture<TestHostComponent>;
  let textareaEl: HTMLTextAreaElement;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ReactiveFormsModule, TextareaComponent],
      declarations: [TestHostComponent],
    }).compileComponents();

    hostFixture = TestBed.createComponent(TestHostComponent);
    hostComponent = hostFixture.componentInstance;
    textareaEl = hostFixture.debugElement.query(
      By.css('textarea')
    ).nativeElement;
    hostFixture.detectChanges();
  });

  it('should create the component within a form', () => {
    expect(hostComponent).toBeTruthy();
  });

  it('should display placeholder text', () => {
    expect(textareaEl.placeholder).toBe(hostComponent.placeholder);
  });

  it('should apply required validator and show error if input is empty', () => {
    hostComponent.formGroup.controls.message.setValue('');
    hostFixture.detectChanges();

    const errors = hostComponent.formGroup.controls.message.errors;
    expect(errors).toEqual({ required: true });
  });

  it('should be valid when input meets requirements', () => {
    hostComponent.formGroup.controls.message.setValue('Valid');
    hostFixture.detectChanges();

    expect(hostComponent.formGroup.controls.message.valid).toBeTrue();
  });

  it('should update form control value when typing', () => {
    textareaEl.value = 'Test';
    textareaEl.dispatchEvent(new Event('input'));
    hostFixture.detectChanges();
    expect(hostComponent.formGroup.controls.message.value).toBe('Test');
  });

  it('should disable the textarea when the control is disabled', () => {
    hostComponent.formGroup.controls['message'].disable();
    textareaEl.disabled = true;
    hostFixture.detectChanges();
    expect(textareaEl.disabled).toBeTrue();
  });
});
