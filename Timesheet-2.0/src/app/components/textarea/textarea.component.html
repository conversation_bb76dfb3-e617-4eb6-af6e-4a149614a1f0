<div class="w-full">
  <div class="relative">
    <textarea
      pInputTextarea
      [(ngModel)]="value"
      (input)="onInputChange(value)"
      [rows]="rows()"
      [placeholder]="placeholder()"
      [disabled]="disabled()"
      [maxlength]="maxLength()"
      [class.border-red-500]="invalid()"
      class="w-full px-3 py-2 text-sm rounded-md border border-gray-300 disabled:bg-neutral-300 disabled:cursor-not-allowed disabled:text-neutral-800"
    ></textarea>
  </div>
  <div class="w-full flex items-center justify-between">
    <div class="text-xs text-red-500 items-center">
      @if (checkErrors && invalid()) {
        <p>{{ error }}</p>
      }
    </div>
    <span class="text-xs text-gray-500 justify-end"
      >{{ value.length }} / {{ maxLength() }}</span
    >
  </div>
</div>
