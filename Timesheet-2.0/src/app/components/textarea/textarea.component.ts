import {
  Component,
  forwardRef,
  Inject,
  INJECTOR,
  Injector,
  input,
  OnInit,
  output,
} from '@angular/core';
import {
  AbstractControl,
  ControlValueAccessor,
  FormsModule,
  NG_VALIDATORS,
  NG_VALUE_ACCESSOR,
  NgControl,
  ValidationErrors,
  Validator,
} from '@angular/forms';

import { InputTextareaModule } from 'primeng/inputtextarea';

@Component({
  selector: 'app-textarea',
  standalone: true,
  imports: [InputTextareaModule, FormsModule],
  templateUrl: './textarea.component.html',
  styleUrl: './textarea.component.css',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => TextareaComponent),
      multi: true,
    },

    {
      provide: NG_VALIDATORS,
      useExisting: forwardRef(() => TextareaComponent),
      multi: true,
    },
  ],
})
export class TextareaComponent
  implements ControlValueAccessor, OnInit, Validator
{
  placeholder = input<string>('Enter text here...');
  rows = input<number>(5);
  disabled = input<boolean>(false);
  invalid = input<boolean>(false);
  required = input<boolean>(false);
  maxLength = input<number>(1000);

  onChange = output<any>();

  public ngControl?: NgControl;
  value: string = '';
  characterCount: number = 0;
  error: string = '';

  constructor(@Inject(INJECTOR) private injector: Injector) {}

  /** Initialize component and obtain form control reference. */
  ngOnInit() {
    this.ngControl = this.injector.get(NgControl, undefined);
  }

  /** Handles input changes, updating the character count and emitting changes. */
  onInputChange(value: string) {
    this.onChangeFn(value);
    this.onChange.emit(value);
  }

  /** Placeholder for the change function registered by the parent form */
  onChangeFn = (value: string) => {};

  /** Placeholder for the touch function registered by the parent form */
  onTouchFn = () => {};

  /** This method is called by the forms API to write to the view when programmatic changes from model to view are requested. */
  writeValue(value: string): void {
    this.value = value || '';
    this.characterCount = this.value.length;
  }

  /** Registers a callback function that is called by the forms API on initialization,
   * The callback function is called when the control's value changes in the UI */
  public registerOnChange(fn: any) {
    this.onChangeFn = fn;
  }

  /** Registers a callback function that is called by the forms API on initialization..
   * The callback function that is called by the forms API to update the form model on blur.
   */
  public registerOnTouched(fn: any) {
    this.onTouchFn = fn;
  }

  /** Validates the input against required and maxLength constraints. */
  validate(control: AbstractControl): ValidationErrors | null {
    const errors: ValidationErrors = {};
    if (this.required() && !this.value) {
      errors['required'] = true;
    }
    if (this.maxLength && this.value.length > this.maxLength()) {
      errors['maxlength'] = {
        requiredLength: this.maxLength,
        actualLength: this.value.length,
      };
    }
    return Object.keys(errors).length ? errors : null;
  }

  /** Checks if any validation errors are present and sets an appropriate error message. */
  get checkErrors(): boolean {
    if (this.ngControl?.control?.errors) {
      this.error = this.ngControl.hasError('required')
        ? 'This field is required.'
        : '';
      return true;
    }
    return false;
  }
}
