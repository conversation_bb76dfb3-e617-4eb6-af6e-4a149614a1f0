import { NgComponentOutlet } from '@angular/common';
import { Component, signal, viewChild, ViewEncapsulation } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { OverlayPanel, OverlayPanelModule } from 'primeng/overlaypanel';

/**
 * PopupComponent is a reusable component that displays dynamic content
 * in an overlay panel. It allows for the rendering of different components
 * based on user interactions.
 */
@Component({
  selector: 'tms-popup',
  standalone: true,
  imports: [NgComponentOutlet, ButtonModule, OverlayPanelModule],
  templateUrl: './popup.component.html',
  styleUrls: ['./popup.component.css'],
  encapsulation: ViewEncapsulation.None,
})
export class PopupComponent {
  overlayPanel = viewChild.required<OverlayPanel>('overlayPanel');
  dynamicComponent = signal<any>(null);

  /**
   * Opens the overlay panel and sets the dynamic component to be rendered.
   */
  openPopup(event: Event, dynamicComponent: any) {
    this.dynamicComponent.set(dynamicComponent);
    this.overlayPanel().toggle(event);
  }
}
