import { ComponentFixture, TestBed } from '@angular/core/testing';
import { PopupComponent } from './popup.component'; // Adjust path as necessary
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { ButtonModule } from 'primeng/button';
import { CommonModule, NgComponentOutlet } from '@angular/common';
import { Component } from '@angular/core';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

@Component({
  selector: 'dynamic-content',
  standalone: true,
  template: `<div class="dynamic-content">Dynamic Content Loaded</div>`,
})
class MockDynamicContentComponent {}

describe('PopupComponent', () => {
  let component: PopupComponent;
  let fixture: ComponentFixture<PopupComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        CommonModule,
        ButtonModule,
        OverlayPanelModule,
        PopupComponent,
        NgComponentOutlet,
        BrowserAnimationsModule,
        MockDynamicContentComponent,
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(PopupComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should open the popup', () => {
    const event = new MouseEvent('click');
    const dynamicTemplate = document.createElement('div');
    const overlayPanelInstance = component.overlayPanel();
    spyOn(overlayPanelInstance, 'toggle');
    component.openPopup(event, dynamicTemplate);

    expect(component.overlayPanel()?.toggle).toHaveBeenCalledWith(event);
  });

  it('should render dynamic content inside the popup', () => {
    const event = new MouseEvent('click');

    component.openPopup(event, MockDynamicContentComponent);
    fixture.detectChanges();

    const dynamicContent =
      fixture.debugElement.nativeElement.querySelector('.dynamic-content');

    expect(dynamicContent).toBeTruthy();
  });
});
