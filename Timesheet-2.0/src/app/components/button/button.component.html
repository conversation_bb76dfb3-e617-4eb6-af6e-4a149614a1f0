<!-- 
  PrimeNG Button component configured with customizable properties. 

  Properties:
  - label: Text displayed on the button.
  - link: If true, styles the button as a link.
  - icon: Adds an icon to the button, positioned to the right.
  - loading: Shows a loading spinner if true.
  - disabled: Disables button interaction if true.
  - severity: Defines the button's color scheme.
  - size: Controls the button's size (e.g., small, large).
  - rounded: Adds rounded corners to the button.
  - text: Renders button without background or border.
  - outlined: Adds an outline instead of a solid fill.
  - badge: Displays badge text on the button.
-->
<p-button
  [label]="label()"
  [link]="isLink()"
  [icon]="icon()"
  iconPos="right"
  [loading]="loading()"
  [disabled]="disabled()"
  [severity]="severity()"
  [size]="size()"
  [rounded]="rounded()"
  [text]="text()"
  [outlined]="outlined()"
  [badge]="badge()"
/>
