import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ButtonComponent } from './button.component';
import { By } from '@angular/platform-browser';
import { ComponentRef } from '@angular/core';

/**
 * Test suite for ButtonComponent
 * This suite covers all major functionality of the ButtonComponent including its rendering, styling, and various states.
 */
describe('ButtonComponent', () => {
  let component: ButtonComponent;
  let componentRef: ComponentRef<ButtonComponent>;
  let fixture: ComponentFixture<ButtonComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ButtonComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(ButtonComponent);
    component = fixture.componentInstance;
    componentRef = fixture.componentRef;
    fixture.detectChanges();
  });

  /**
   * Verifies that the ButtonComponent instance is created successfully.
   */
  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('Button Types', () => {
    /**
     * Ensures the button displays the provided text when a label is set.
     */

    it('should display a basic button with text', () => {
      // Dynamically update the `label` input
      componentRef.setInput('label', 'Submit');
      fixture.detectChanges(); // Ensure changes propagate

      const button = fixture.debugElement.query(
        By.css('p-button')
      ).nativeElement;
      expect(button.textContent).toContain('Submit');
    });

    /**
     * Validates that the button is styled as a link when the `isLink` property is true.
     */
    it('should style as link when isLink is true', () => {
      componentRef.setInput('isLink', true);
      fixture.detectChanges();
      const button = fixture.debugElement.query(
        By.css('p-button')
      ).componentInstance;
      expect(button.link).toBeTrue();
    });

    /**
     * Checks if only an icon is displayed when the label is empty.
     */
    it('should display only an icon when label is empty', () => {
      componentRef.setInput('icon', 'pi pi-user');
      fixture.detectChanges();
      const icon = fixture.debugElement.query(By.css('.pi-user'));
      expect(icon).toBeTruthy();
    });

    /**
     * Verifies that the button displays both an icon and text.
     */
    it('should display icon with text', () => {
      componentRef.setInput('icon', 'pi pi-user');
      componentRef.setInput('label', 'User');
      fixture.detectChanges();
      const button = fixture.debugElement.query(
        By.css('p-button')
      ).nativeElement;
      expect(button.textContent).toContain('User');
      expect(button.querySelector('.pi-user')).toBeTruthy();
    });

    /**
     * Ensures the button displays only text when the icon property is not set.
     */
    it('should display only text when icon is empty', () => {
      componentRef.setInput('icon', '');
      componentRef.setInput('label', 'Text Only');
      fixture.detectChanges();
      const button = fixture.debugElement.query(
        By.css('p-button')
      ).nativeElement;
      expect(button.textContent).toContain('Text Only');
      expect(button.querySelector('.pi')).toBeNull();
    });
  });

  describe('Button Sizes', () => {
    /**
     * Verifies that the button applies 'small' size styling when `size` is set to 'small'.
     */
    it('should apply small size styling', () => {
      componentRef.setInput('size', 'small');
      fixture.detectChanges();
      const button = fixture.debugElement.query(
        By.css('p-button')
      ).componentInstance;
      expect(button.size).toBe('small');
    });

    /**
     * Verifies that the button applies 'large' size styling when `size` is set to 'large'.
     */
    it('should apply large size styling', () => {
      componentRef.setInput('size', 'large');
      fixture.detectChanges();
      const button = fixture.debugElement.query(
        By.css('p-button')
      ).componentInstance;
      expect(button.size).toBe('large');
    });
  });

  describe('Button States', () => {
    /**
     * Confirms that the button enters a loading state when `loading` is set to true.
     */
    it('should show loading state', () => {
      componentRef.setInput('loading', true);
      fixture.detectChanges();
      const button = fixture.debugElement.query(
        By.css('p-button')
      ).componentInstance;
      expect(button.loading).toBeTrue();
    });

    /**
     * Verifies that the button is disabled when `disabled` is set to true.
     */
    it('should be disabled', () => {
      componentRef.setInput('disabled', true);
      fixture.detectChanges();
      const button = fixture.debugElement.query(
        By.css('p-button')
      ).componentInstance;
      expect(button.disabled).toBeTrue();
    });
  });

  describe('Button Severity (Color)', () => {
    /**
     * Tests if the button applies the 'primary' severity style.
     */
    it('should apply primary color', () => {
      componentRef.setInput('severity', 'primary');
      fixture.detectChanges();
      const buttonElement = fixture.debugElement.query(
        By.css('p-button')
      ).componentInstance;
      expect(buttonElement.severity).toBe('primary');
    });
    /**
     * Tests if the button applies the 'success' severity style.
     */
    it('should apply success color', () => {
      componentRef.setInput('severity', 'success');
      fixture.detectChanges();
      const buttonElement = fixture.debugElement.query(
        By.css('p-button')
      ).componentInstance;
      expect(buttonElement.severity).toBe('success');
    });

    /**
     * Tests if the button applies the 'warning' severity style.
     */
    it('should apply warning color', () => {
      componentRef.setInput('severity', 'warning');
      fixture.detectChanges();
      const buttonElement = fixture.debugElement.query(
        By.css('p-button')
      ).componentInstance;
      expect(buttonElement.severity).toBe('warning');
    });

    /**
     * Tests if the button applies the 'danger' severity style.
     */
    it('should apply danger color', () => {
      componentRef.setInput('severity', 'danger');
      fixture.detectChanges();
      const buttonElement = fixture.debugElement.query(
        By.css('p-button')
      ).componentInstance;
      expect(buttonElement.severity).toBe('danger');
    });
  });

  describe('Button Styles', () => {
    /**
     * Checks if the button applies rounded corners when `rounded` is set to true.
     */
    it('should apply rounded corners', () => {
      componentRef.setInput('rounded', true);
      fixture.detectChanges();
      const button = fixture.debugElement.query(
        By.css('p-button')
      ).componentInstance;
      expect(button.rounded).toBeTrue();
    });

    /**
     * Confirms the button is styled as outlined when `outlined` is set to true.
     */
    it('should apply outlined style', () => {
      componentRef.setInput('outlined', true);
      fixture.detectChanges();
      const button = fixture.debugElement.query(
        By.css('p-button')
      ).componentInstance;
      expect(button.outlined).toBeTrue();
    });
  });
});
