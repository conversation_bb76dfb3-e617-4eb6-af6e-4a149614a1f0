import { Component, input, ViewEncapsulation } from '@angular/core';
import { ButtonModule } from 'primeng/button';

export type ButtonSeverity = 'primary' | 'success' | 'warning' | 'danger';

export type ButtonSize = 'small' | 'large';
/**
 * ButtonComponent is a customizable button component that supports various styles
 * such as outlined, rounded, and loading states, leveraging PrimeNG ButtonModule.
 */

@Component({
  selector: 'tms-button',
  standalone: true,
  imports: [ButtonModule],
  templateUrl: './button.component.html',
  styleUrl: './button.component.css',
  encapsulation: ViewEncapsulation.None,
})
export class ButtonComponent {
  /** The label text displayed on the button. */
  label = input<string>('');

  /** Whether the button is styled as a link rather than a standard button. */
  isLink = input<boolean>(false);

  /** Icon class for an icon to display within the button. */
  icon = input<string>('');

  /** Shows a loading spinner within the button when true. */
  loading = input<boolean>(false);

  /** Disables the button when true. */
  disabled = input<boolean>(false);

  /** Sets the severity (e.g., primary, secondary) to control button color scheme. */
  severity = input<ButtonSeverity>('primary');

  /** Defines the button size, such as 'small' or 'large'. */
  size = input<ButtonSize>();

  /** Renders a rounded button when true. */
  rounded = input<boolean>(false);

  /** When true, removes button background for a text-only style. */
  text = input<boolean>(false);

  /** When true, adds an outline around the button instead of a solid fill. */
  outlined = input<boolean>(false);

  /** Badge text displayed on the button. */
  badge = input<string>('');
}
