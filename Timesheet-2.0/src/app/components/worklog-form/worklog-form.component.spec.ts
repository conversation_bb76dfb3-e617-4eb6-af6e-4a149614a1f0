import { DatePipe } from '@angular/common';
import { HttpClient, HttpHandler } from '@angular/common/http';
import { HttpTestingController } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormBuilder, ReactiveFormsModule } from '@angular/forms';
import { By } from '@angular/platform-browser';
import { QueryClient } from '@tanstack/angular-query-experimental';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { AuthService } from '../../services/auth/auth-service';
import { ProjectByResourceId } from '../../services/project/project.model';
import { ProjectsService } from '../../services/project/project.service';
import { TaskService } from '../../services/task/task.service';
import { WorkLogService } from '../../services/worklog/worklog.service';
import { WorklogFormComponent } from './worklog-form.component';

// Mock services
class MockWorkLogService {
  createWorklog() {}
  updateWorklog() {}
  getDayOffType() {
    return null;
  }
  lastEnteredWorklog() {}
}
class MockAuthService {
  userId() {
    return 'test-user-id'; // Mock user ID
  }
}

describe('WorklogFormComponent UI Tests', () => {
  let component: WorklogFormComponent;
  let fixture: ComponentFixture<WorklogFormComponent>;
  let httpTestingController: HttpTestingController;

  const mockDialogRef = new DynamicDialogRef();
  const mockDialogConfig = new DynamicDialogConfig();
  let mockMyProjectService: jasmine.SpyObj<ProjectsService>;
  let mockMyTaskService: jasmine.SpyObj<TaskService>;
  const mockProjects: ProjectByResourceId[] = [
    {
      id: '1',
      projectName: 'Test Project 1',
      contactName: 'John Doe',
      contactEmail: '<EMAIL>',
      contactPhoneNumber: '************',
      description: 'Test project description',
      startDate: new Date('2023-01-01').toISOString(),
      endDate: new Date('2023-12-31').toISOString(),
      contracts: 2,
      clientName: 'Client A',
      totalMinutes: 160,
      projectStatus: 'active',
      billable: true,
      projectManager: [
        {
          email: '<EMAIL>',
          id: '1',
          name: 'Akshay Bhat',
          typeOfResource: 'projectManager',
          phoneNumber: '9087654321',
        },
      ],
    },
    {
      id: '2',
      projectName: 'Test Project 2',
      contactName: 'Jane Smith',
      contactEmail: '<EMAIL>',
      contactPhoneNumber: '************',
      description: 'Another test project description',
      startDate: new Date('2023-02-01').toISOString(),
      endDate: new Date('2023-12-31').toISOString(),
      clientName: 'Client B',
      totalMinutes: null,
      projectStatus: 'completed',
      billable: false,
      projectManager: [
        {
          email: '<EMAIL>',
          id: '1',
          name: 'Akshay Bhat',
          typeOfResource: 'projectManager',
          phoneNumber: '9087654321',
        },
      ],
    },
  ];

  beforeEach(async () => {
    mockMyProjectService = jasmine.createSpyObj(
      'MyTasksService',
      ['getUserRole'],
      {
        projectListQuery: () => ({
          data: jasmine.createSpy('data').and.returnValue(mockProjects),
          isLoading: jasmine.createSpy('isLoading').and.returnValue(false),
          isError: jasmine.createSpy('isError').and.returnValue(false),
          isFetching: jasmine.createSpy('isFetching').and.returnValue(false),
          refetch: jasmine.createSpy('refetch'),
        }),
      }
    );

    mockMyTaskService = jasmine.createSpyObj(
      'TaskService',
      ['tasksByProjectQuery'],
      {
        tasksByProjectQuery: (projectId: string) => ({
          data: jasmine
            .createSpy('data')
            .and.returnValue([{ id: '1', taskName: 'Test Task' }]),
          isLoading: jasmine.createSpy('isLoading').and.returnValue(false),
          isError: jasmine.createSpy('isError').and.returnValue(false),
          isFetching: jasmine.createSpy('isFetching').and.returnValue(false),
          refetch: jasmine.createSpy('refetch'),
        }),
      }
    );

    // Set up mock data for dialog config
    mockDialogConfig.data = {
      worklog: null, // or provide a mock worklog object for edit mode
      date: new Date(),
    };

    await TestBed.configureTestingModule({
      imports: [WorklogFormComponent, ReactiveFormsModule],
      providers: [
        { provide: WorkLogService, useClass: MockWorkLogService },
        { provide: ProjectsService, useValue: mockMyProjectService },
        { provide: TaskService, useValue: mockMyTaskService },
        { provide: DynamicDialogRef, useValue: mockDialogRef },
        { provide: DynamicDialogConfig, useValue: mockDialogConfig },
        { provide: AuthService, useClass: MockAuthService },
        DatePipe,
        FormBuilder,
        HttpClient,
        HttpHandler,
        QueryClient,
        HttpTestingController,
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(WorklogFormComponent);
    component = fixture.componentInstance;
    httpTestingController = TestBed.inject(HttpTestingController);

    fixture.detectChanges();
  });

  afterEach(() => {});

  it('should create the component', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize the form correctly', () => {
    expect(component.worklogForm).toBeTruthy();
    expect(component.worklogForm.get('project')).toBeTruthy();
    expect(component.worklogForm.get('task')).toBeTruthy();
    expect(component.worklogForm.get('description')).toBeTruthy();
    expect(component.worklogForm.get('loggedHours')).toBeTruthy();
    expect(component.worklogForm.get('date')).toBeTruthy();
  });

  it('should display project dropdown', () => {
    const projectDropdown = fixture.debugElement.query(
      By.css('p-dropdown[formControlName="project"]')
    );
    expect(projectDropdown).toBeTruthy();
  });

  it('should display task dropdown', () => {
    const taskDropdown = fixture.debugElement.query(
      By.css('p-dropdown[formControlName="task"]')
    );
    expect(taskDropdown).toBeTruthy();
  });

  it('should disable the submit button when the form is invalid', () => {
    const submitButton = fixture.nativeElement.querySelector(
      'button[type="submit"]'
    );
    fixture.detectChanges();

    // Initially, form is invalid (no values set)
    expect(submitButton.disabled).toBeTrue();

    component.worklogForm.patchValue({
      project: { id: '1', projectName: 'Test Project' },
      task: { id: '1', taskName: 'Test Task' },
      description: 'Test description',
      loggedHours: new Date(2024, 0, 1, 2, 30),
      date: new Date(),
    });

    fixture.detectChanges();

    // Verify button is now enabled
    expect(submitButton.disabled).toBeTrue();
  });
});
