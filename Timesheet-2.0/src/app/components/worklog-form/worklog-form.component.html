<form
  [formGroup]="worklogForm"
  (ngSubmit)="onSubmit()"
  class="flex flex-col space-y-4 w-full max-w-lg"
>
  @if (dateWarning) {
    <small
      class="flex items-center bg-amber-100 border border-amber-300 text-amber-800 rounded-md px-2 py-1"
      ><i class="pi pi-info-circle mr-2"></i>{{ dateWarning }}</small
    >
  }

  @if (rejectedWorklogRemarks) {
    <small
      class="flex items-center bg-red-100 border border-red-100 text-red-800 rounded-md px-2 py-1"
      ><i class="pi pi-info-circle mr-2"></i>{{ rejectedWorklogRemarks }}</small
    >
  }

  <div class="flex flex-col gap-2">
    <label for="date" class="text-sm text-neutral-500"
      >Date<span class="text-lg text-red-500">*</span></label
    >
    <p-calendar
      #date
      formControlName="date"
      id="date"
      [showIcon]="!isEditMode"
      [variant]="isEditMode ? 'filled' : 'outlined'"
      [appendTo]="'body'"
      [autofocus]="false"
      dateFormat="dd/mm/yy"
      [maxDate]="today"
      [readonlyInput]="true"
      [iconDisplay]="'input'"
      [style]="{ width: '100%' }"
    ></p-calendar>
    @if (checkErrors('date')) {
      <small class="text-red-500">{{ checkErrors('date') }}</small>
    }
  </div>

  <div class="flex flex-col gap-2">
    <label for="project" class="text-sm text-neutral-500"
      >Project<span class="text-lg text-red-500">*</span></label
    >
    <p-dropdown
      formControlName="project"
      [options]="getProjectDropdownOptions(date.value)"
      optionLabel="label"
      id="project"
      [autofocus]="!worklogForm.get('project')?.value"
      placeholder="Select project"
      [style]="{ width: '100%' }"
      (onChange)="onProjectChange($event)"
      [loading]="projectsQuery().isLoading() || projectsQuery().isFetching()"
      [emptyMessage]="'No Projects Found.'"
    />
  </div>

  <div class="grid grid-cols-2 gap-2">
    <div class="flex flex-col gap-2">
      <label for="task" class="text-sm text-neutral-500">
        Task<span class="text-lg text-red-500">*</span>
      </label>
      <p-dropdown
        formControlName="task"
        [options]="taskDropdownOptions()"
        optionLabel="label"
        placeholder="Select task"
        [style]="{ width: '100%' }"
        [loading]="tasksQuery().isLoading() || tasksQuery().isFetching()"
        [emptyMessage]="'No Tasks Found.'"
        [autofocus]="!isEditMode"
      />
    </div>
    <div class="flex flex-col gap-2">
      <label for="task" class="text-sm text-neutral-500">
        Time Spent<span class="text-lg text-red-500">*</span>
      </label>
      <div class="flex flex-col gap-1">
        <p-calendar
          inputId="calendar-timeonly"
          formControlName="loggedHours"
          [timeOnly]="true"
          placeholder="HH : MM"
          (onInput)="onTimeInput($event)"
          [style]="{ width: '100%' }"
          [class.border-red-500]="worklogForm.getError('loggedHours')"
        />
        @if (checkErrors('loggedHours')) {
          <small class="text-xs text-red-500">{{
            checkErrors('loggedHours')
          }}</small>
        }
      </div>
    </div>
  </div>

  <div class="flex flex-col gap-1">
    <label for="location" class="text-sm text-neutral-500">
      Location
      <span class="text-red-500 text-lg leading-none">*</span>
    </label>
    <p-dropdown
      formControlName="location"
      [options]="location"
      optionLabel="label"
      styleClass="w-full"
      [dropdownIcon]="'pi pi-angle-down'"
      [placeholder]="'Select location'"
    >
      <ng-template pTemplate="selectedItem" let-selected>
        <div class="flex items-center">
          <div>{{ selected.label }}</div>
        </div>
      </ng-template>
      <ng-template let-location pTemplate="item">
        <div class="flex items-center">
          <div>{{ location.label }}</div>
        </div>
      </ng-template>
    </p-dropdown>
  </div>

  <div class="flex flex-col gap-2">
    <label for="description" class="text-sm text-neutral-500">
      Description<span class="text-lg text-red-500">*</span>
    </label>
    <textarea
      pInputTextarea
      id="description"
      formControlName="description"
      [rows]="3"
      [cols]="30"
      placeholder="Enter description"
      [style]="{ width: '100%' }"
    ></textarea>
  </div>

  <div class="flex flex-row-reverse justify-between items-center">
    <p-button
      type="submit"
      [label]="
        formSubmitButtonLabelService.getFormSubmitButtonLabel(
          isSubmitting,
          isEditMode
        )
      "
      [attr.aria-label]="
        formSubmitButtonLabelService.getFormSubmitButtonLabel(
          isSubmitting,
          isEditMode
        )
      "
      [icon]="
        formSubmitButtonLabelService.getFormSubmitButtonIcon(isSubmitting)
      "
      [disabled]="submitButtonDisabled"
      styleClass="submit-button disabled:cursor-not-allowed disabled:bg-neutral-400 disabled:border-neutral-400"
    ></p-button>
    @if (isEditMode) {
      <!-- Opens a dialog for delete confirmation -->
      <p-confirmDialog
        class="custom-deletedialog lg:max-w-40"
      ></p-confirmDialog>
      <p-button
        severity="danger"
        label="Delete"
        [attr.aria-label]="'Delete Worklog'"
        styleClass="text-sm"
        (click)="confirm($event)"
      ></p-button>
    }
  </div>
</form>
