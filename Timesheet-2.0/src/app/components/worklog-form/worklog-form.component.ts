import { CommonModule, DatePipe } from '@angular/common';
import { Component, computed, signal, ViewEncapsulation } from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { injectMutation } from '@tanstack/angular-query-experimental';
import { addMinutes, format, isValid, parse, set, startOfDay } from 'date-fns';
import { ConfirmationService, MessageService } from 'primeng/api';
import { CalendarModule } from 'primeng/calendar';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DropdownModule } from 'primeng/dropdown';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { InputNumberModule } from 'primeng/inputnumber';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { AuthService } from '../../services/auth/auth-service';
import { Option } from '../../services/common.model';
import { FormSubmitButtonLabelService } from '../../services/formsubmitbuttonlabel/formsubmitbuttonlabel.service';
import { ProjectsService } from '../../services/project/project.service';
import { TaskService } from '../../services/task/task.service';
import {
  CreateWorkLog,
  DayOffType,
  Worklog,
} from '../../services/worklog/worklog.model';
import { WorkLogService } from '../../services/worklog/worklog.service';
import { MAXIMUM_WORK_MINUTES_PER_DAY } from '../../settings';
import { DATE_FORMAT_YYYY_MM_DD } from '../../settings';
import { WORKLOG_LOCATION } from '../../constants/constant';

/**
 * Interface defining the structure of worklog form data.
 */
interface WorklogFormData {
  project: Option;
  task: Option;
  description: string;
  loggedHours: Date | string;
  date: Date;
  location:Option;
}

/**
 * Component for creating or editing a work log entry.
 *
 * This component provides a reactive form to capture details such as project,
 * task, description, logged hours, and date. It integrates with services to
 * dynamically fetch projects and tasks based on user selection.
 *
 * Usage:
 * - Import the component and use its selector <tms-worklog-form> in your template.
 * - To open the form in a dialog, use DynamicDialogRef to pass any necessary data
 *   (e.g., existing work log for editing or default values).
 * - The form handles submission internally and provides feedback through messages
 *   upon success or failure.
 */
@Component({
  selector: 'tms-worklog-form',
  standalone: true,
  imports: [
    ReactiveFormsModule,
    DropdownModule,
    CalendarModule,
    InputTextareaModule,
    InputNumberModule,
    CommonModule,
    ConfirmDialogModule,
  ],
  providers: [DatePipe, ConfirmationService, MessageService],
  templateUrl: './worklog-form.component.html',
  styleUrls: ['./worklog-form.component.css'],
  encapsulation: ViewEncapsulation.None,
})
export class WorklogFormComponent {
  isEditMode = false;
  worklogForm: FormGroup;
  today: Date = new Date();
  isSubmitting = false;
  dateWarning: string | null = null;
  location: Option[];

  selectedProjectId = signal<string | null>(null);

  projectsQuery = computed(() => this.projectService.projectListQuery());

  tasksQuery = computed(() => {
    return this.taskService.tasksByProjectQuery(this.selectedProjectId() || '');
  });

  constructor(
    public worklogService: WorkLogService,
    public projectService: ProjectsService,
    public taskService: TaskService,
    // ref for dynamic dialog
    public dialogRef: DynamicDialogRef,
    // config object from dynamic modal rendering this (WorklogFormComponent) component.
    public config: DynamicDialogConfig,
    public datePipe: DatePipe,
    private fb: FormBuilder,
    private confirmationService: ConfirmationService,
    private authService: AuthService,
    public formSubmitButtonLabelService: FormSubmitButtonLabelService
  ) {
    // Determine if the form is in edit mode based on provided data
    this.isEditMode = !!this.config?.data?.worklog;
    this.location = WORKLOG_LOCATION;

    this.worklogForm = this.fb.group({
      project: [null, Validators.required],
      task: [null, Validators.required],
      description: ['', Validators.required],
      loggedHours: [this.getDefaultLoggedHoursValue(), [Validators.required]],
      date: [
        {
          value: this.config?.data?.date,
          disabled: this.isEditMode,
        },
        [Validators.required, this.dateValidator()],
      ],
      location:['', Validators.required]
    });

    // Check warnings on initialization
    this.updateDateWarning(this.worklogForm.get('date')?.value);

    // Subscribe to changes in the date field
    this.worklogForm.get('date')?.valueChanges.subscribe((date) => {
      this.updateDateWarning(date);
    });
  }

  // Populate the form with the data on initialization.
  ngAfterContentInit() {
    this.loadFormData();

    const selectedProject = this.worklogForm.get('project')?.value;
    if (selectedProject?.value) {
      this.selectedProjectId.set(selectedProject.value);
    } else {
      this.worklogForm.get('task')?.setValue(null);
      this.worklogForm.updateValueAndValidity();
    }
  }

  /**
   * Confirms the deletion of a worklog entry and handles the deletion process.
   *
   * This method displays a confirmation dialog to the user, asking if they are sure
   * about deleting a specific worklog entry. If the user confirms, it calls the
   * `deleteWorklogQuery` mutation to delete the entry. Upon success or failure,
   * it closes the dialog with an appropriate message.
   */
  confirm(event: Event) {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: `Are you sure you want to delete the worklog entry for ${this.config?.data?.worklog?.project?.projectName}?`,
      header: 'Delete Worklog Entry',
      icon: 'pi pi-exclamation-circle text-base',
      acceptLabel: 'Delete',
      rejectLabel: 'Cancel',
      acceptButtonStyleClass: 'p-button-danger',
      rejectButtonStyleClass: 'p-button-primary',
      acceptIcon: 'none',
      rejectIcon: 'none',
      defaultFocus: 'none',
      accept: async () => {
        try {
          const response =
            await this.worklogService.deleteWorklogQuery.mutateAsync(
              this.config?.data?.worklog?.id
            );
          this.dialogRef.close({
            type: 'success',
            severity: 'success',
            message: response.message,
            summary: 'Worklog Deleted',
          });
          this.worklogService.worklogQuery.refetch();
        } catch (error: any) {
          const errorMessage =
            error.response?.data?.message ||
            error.message ||
            'Failed to delete. Please try again';
          this.dialogRef.close({
            type: 'error',
            severity: 'error',
            message: errorMessage,
            summary: 'Error',
          });
        }
      },
    });
  }

  /* Generates dropdown options for projects based on date criteria.
   * Filters projects to include only those with valid start and end dates relative to the given date.
   * If in edit mode the current worklog's project is missing in the dropdown options,
   * it is manually added to ensure it is always available.
   */
  getProjectDropdownOptions(date: Date): Option[] | undefined {
    const projectOptions = this.projectsQuery()
      .data()
      ?.filter((project) =>
        this.validateProjectDate(
          project.startDate,
          project.endDate,
          this.worklogForm.get('date')?.value
        )
      )
      .map((project) => ({
        label: project.projectName,
        value: project.id,
      }));

    const worklog: Worklog = this.config.data?.worklog;
    if (
      this.isEditMode &&
      worklog &&
      !projectOptions?.some((p) => p.value === worklog.projectId)
    ) {
      projectOptions?.push({
        value: worklog.projectId,
        label: worklog.project?.projectName || '',
      });
    }
    return projectOptions;
  }

  /**
   * Computed property to generate dropdown options for tasks.
   * If in edit mode and the current worklog's task is missing in the dropdown options,
   * it is manually added to ensure it is always available.
   */
  taskDropdownOptions = computed(() => {
    const taskOptions = this.tasksQuery()
      .data()
      ?.map((task) => ({
        value: task.id,
        label: task.taskName,
      }));

    const worklog: Worklog = this.config.data?.worklog || null;
    if (
      this.isEditMode &&
      worklog &&
      !taskOptions?.some((t) => t.value === worklog.taskId) &&
      worklog.projectId === this.worklogForm.get('project')?.value.value
    ) {
      taskOptions?.push({
        value: worklog.taskId,
        label: worklog.task?.taskName || '',
      });
    }

    return taskOptions;
  });

  /**
   * Event handler for when a project is selected or changed.
   * Resets the task selection and selected project id signal.
   */
  onProjectChange(event: any) {
    const selectedProject = event.value;
    this.worklogForm.get('task')?.setValue(null);

    if (selectedProject) {
      this.selectedProjectId.set(selectedProject.value);
    } else {
      this.selectedProjectId.set(null);
    }
  }

  // Mutation hook for adding a new work log entry
  addWorklogQuery = injectMutation(() => ({
    mutationKey: ['worklog', this.authService.userId()],
    mutationFn: (worklogData: CreateWorkLog) =>
      this.worklogService.createWorklog(worklogData),
    onSuccess: () => {
      this.worklogService.worklogQuery.refetch();
      this.dialogRef.close({
        type: 'success',
        severity: 'success',
        message: 'Worklog submitted successfully!',
        summary: 'Worklog Added',
      });
    },
    onError: (error) => {
      console.error('Error adding worklog:', error);
      this.dialogRef.close({
        type: 'error',
        severity: 'error',
        message: error.message,
        summary: 'Error',
      });
    },
  }));

  // Mutation hook for updating an existing work log entry
  updateWorklogQuery = injectMutation(() => ({
    enabled: !!this.config?.data?.worklog?.id,
    mutationKey: [
      'worklog',
      this.config?.data?.worklog?.id,
      this.authService.userId(),
    ],
    mutationFn: (worklogData: CreateWorkLog) =>
      this.worklogService.updateWorklog(worklogData),
    onSuccess: () => {
      this.worklogService.worklogQuery.refetch();
      this.dialogRef.close({
        type: 'success',
        severity: 'success',
        message: 'Worklog updated successfully!',
        summary: 'Worklog Edited',
      });
    },
    onError: (error) => {
      console.error('Error updating worklog:', error);
      this.dialogRef.close({
        type: 'error',
        severity: 'error',
        message: error.message,
        summary: 'Error',
      });
    },
  }));

  // Converts a given date to a UTC date with time set to 00:00:00.000.
  private toUTCDate(date: string | Date): Date {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
  
    const utcDate = new Date();
    utcDate.setUTCFullYear(dateObj.getFullYear());
    utcDate.setUTCMonth(dateObj.getMonth());
    utcDate.setUTCDate(dateObj.getDate());
    utcDate.setUTCHours(0, 0, 0, 0);  
    return utcDate;
  }
  
  /**
   * Loads existing form data into the reactive form if provided.
   */
  private loadFormData() {
    const lastEnteredWorklog = this.worklogService.lastEnteredWorklog();
    const worklog: Worklog = this.config.data?.worklog || null;

    if (worklog?.workDate) {
       const formValue: any = {
        date: this.toUTCDate(worklog.workDate),
        project: {
          value: worklog.projectId,
          label: worklog.project?.projectName,
        },
        task: {
          value: worklog.taskId,
          label: worklog.task?.taskName,
        },
        loggedHours: this.convertMinutesToDateObject(worklog.minutes),
        description: worklog.description,
      };
  
      // Pre-fill location if it exists in the worklog
      if (worklog.location) {
        // Find the matching location option from WORKLOG_LOCATION
        const selectedLocation = this.location.find(
          (loc) => loc.value === worklog.location
        );
        if (selectedLocation) {
          formValue.location = selectedLocation;
        }
      }
      this.worklogForm.patchValue(formValue);
    } else if (lastEnteredWorklog) {
      if (
        this.validateProjectDate(
          lastEnteredWorklog?.startDate ?? null,
          lastEnteredWorklog?.endDate ?? null,
          this.worklogForm.get('date')?.value
        )
      ) {
        this.worklogForm.patchValue({
          project: {
            value: lastEnteredWorklog?.value,
            label: lastEnteredWorklog?.label,
          },
        });
      }
    }
  }

  // Checks if the current date is within projects start and end date.
  validateProjectDate(
    startDate: Date | string | null,
    endDate: Date | string | null,
    currentDate: Date
  ) {
    const startDateValid = startDate
      ? new Date(startDate) <= currentDate
      : false;
    const endDateValid = endDate ? new Date(endDate) >= currentDate : false;

    return startDateValid && endDateValid;
  }

  convertMinutesToDateObject(minutes: number): Date | '' {
    if (!minutes && minutes !== 0) return '';
    return addMinutes(startOfDay(new Date()), minutes);
  }

  /**
   * Submits the form if valid and triggers mutation
   * to add or update a work log entry based on edit mode.
   */
  onSubmit() {
    try {
      if (this.worklogForm.valid) {
        this.isSubmitting = true;
        const formValue: WorklogFormData = {
          ...this.worklogForm.value,
          date: this.worklogForm.get('date')?.value, // Include the disabled date field value.
        };

        const loggedHours =
          this.datePipe.transform(new Date(formValue.loggedHours), 'HH:mm') ||
          '00:00';

        if (this.isMaximumEffortReached(loggedHours, formValue.date)) {
          this.dialogRef.close({
            type: 'error',
            severity: 'error',
            message: 'Total logged hours cannot exceed 16 hours',
            summary: 'Worklog Error',
          });
          return;
        }

        const worklogData: CreateWorkLog = {
          id: this.config?.data?.worklog?.id || null,
          date: format(formValue.date, DATE_FORMAT_YYYY_MM_DD),
          projectId: formValue.project.value,
          taskId: formValue.task.value,
          description: formValue.description.trim(),
          loggedHours:
            this.datePipe.transform(new Date(formValue.loggedHours), 'HH:mm') ||
            '00:00',
          location:formValue.location.value
        };

        this.isEditMode
          ? this.updateWorklogQuery.mutate(worklogData)
          : this.addWorklogQuery.mutate(worklogData);
        this.worklogForm.disable();
      }
    } catch (error) {
      console.error(error);
      this.dialogRef.close({
        type: 'error',
        severity: 'error',
        message: 'Something went wrong. Please try again.',
        summary: 'Worklog Error',
      });
    }
  }

  /**
   * Validator function for ensuring that the selected date is not in the future.
   * @returns A validation function that checks if the date is valid.
   */
  dateValidator(): ValidatorFn {
    return (control: AbstractControl): ValidationErrors | null => {
      const selectedDate = control.value;
      if (selectedDate && new Date(selectedDate) > this.today) {
        return { dateInFuture: true };
      }
      return null;
    };
  }

  onTimeInput(event: Event): void {
    const inputElement = event.target as HTMLInputElement;
    let inputValue = inputElement.value;

    // Remove all non-digit characters
    const cleanedValue = inputValue.replace(/\D/g, '');

    // Extract hours and minutes
    const hours = cleanedValue.slice(0, 2);
    const minutes = cleanedValue.slice(2, 4);

    // Format value as hh:mm
    let formattedValue = hours;
    if (minutes) {
      formattedValue += ':' + minutes;
    }
    if (formattedValue === '00:00') {
      this.worklogForm.get('loggedHours')?.setErrors({ zeroHours: true });
      return;
    }

    // Update input element value
    inputElement.value = formattedValue;

    // Validate and update form control
    if (formattedValue.length === 5) {
      const parsedTime = parse(formattedValue, 'HH:mm', new Date());

      if (isValid(parsedTime)) {
        const timeDate = set(new Date(), {
          hours: parsedTime.getHours(),
          minutes: parsedTime.getMinutes(),
          seconds: 0,
        });

        this.worklogForm.get('loggedHours')?.setValue(timeDate);
        this.worklogForm.get('loggedHours')?.setErrors(null);
      } else {
        this.worklogForm.get('loggedHours')?.setErrors({ invalidTime: true });
      }
    } else if (formattedValue.length === 0) {
      this.worklogForm.get('loggedHours')?.setErrors({ required: true });
    } else {
      this.worklogForm.get('loggedHours')?.setErrors({ invalidTime: true });
    }
  }

  /**
   * Updates the date warning message based on the selected date.
   * @param selectedDate The date selected in the form.
   */
  private updateDateWarning(selectedDate: Date): void {
    const dayOffType = this.worklogService.getDayOffType(selectedDate);
    const dayOffWarnings: { [key in DayOffType]: string } = {
      FullDayLeave: 'You are adding a worklog on a full-day leave.',
      HalfDayLeave: 'You are adding a worklog on a half-day leave.',
      WeekOff: 'You are adding a worklog on a weekend.',
      Holiday: 'You are adding a worklog on a company holiday.',
    };

    this.dateWarning = dayOffType ? dayOffWarnings[dayOffType] : '';
  }

  /**
   * Check for errors in the form and return an error message for a specific control.
   */
  checkErrors(fieldName: string): string | null {
    const control = this.worklogForm.get(fieldName);
    if (control?.invalid && control?.touched) {
      const errors = control.errors;

      if (errors) {
        if (errors['dateValidator']) {
          return `Please enter a valid date.`;
        } else if (errors['invalidTime']) {
          return `Enter valid log time.`;
        } else if (errors['zeroHours']) {
          return 'Work hours must be greater than zero.';
        }
      }
    }
    return null;
  }

  get submitButtonDisabled() {
    return (
      this.worklogForm.invalid ||
      this.isSubmitting ||
      !this.worklogForm.get('description')?.value.trim()
    );
  }

  convertTimeToMinutes(timeString: string): number {
    const [hours, minutes] = timeString.split(':').map(Number);
    return hours * 60 + minutes;
  }

  isMaximumEffortReached(timeString: string, date: Date): boolean {
    const totalLoggedMinutes = this.worklogService.totalWorkMinutes(date);
    const enteredMinutes = this.convertTimeToMinutes(timeString);

    if (this.isEditMode) {
      // In edit mode, subtract the original time entry from total logged minutes
      const originalEntryMinutes = this.config.data.worklog.minutes;
      return (
        totalLoggedMinutes - originalEntryMinutes + enteredMinutes >
        MAXIMUM_WORK_MINUTES_PER_DAY
      );
    }

    return totalLoggedMinutes + enteredMinutes > MAXIMUM_WORK_MINUTES_PER_DAY;
  }

  get rejectedWorklogRemarks(): string | undefined {
    const status = this.config.data?.worklog?.workLogStatus;
    return status?.status === 'rejected'
      ? (status.remarks ?? undefined)
      : undefined;
  }

  /**
   * Returns a default Date object with the time set to 08:00.
   * This is used as the initial value for logged hours in the form.
   */
  getDefaultLoggedHoursValue(): Date {
    const date = new Date();
    date.setHours(8, 0, 0, 0);
    return date;
  }
}
