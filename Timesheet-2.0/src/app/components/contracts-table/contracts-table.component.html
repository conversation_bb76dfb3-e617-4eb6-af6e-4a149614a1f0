<div class="w-full bg-white overflow-hidden shadow rounded-xl">
  <p-table
    #dt
    [value]="contracts()"
    [sortOrder]="1"
    [loading]="isLoading()"
    [showLoader]="false"
    [scrollable]="true"
    [scrollHeight]="'calc(100vh - 165px)'"
  >
    <ng-template pTemplate="header">
      <tr class="bg-transparent">
        @for (col of contractColumnHeader; track $index) {
          <th
            [pSortableColumn]="col.field"
            class="text-sm font-bold bg-primary-50 text-nowrap"
            [ngClass]="col.class"
          >
            {{ col.header }}
            @if (col.sortable) {
              <p-sortIcon [field]="col.field"></p-sortIcon>
            }
          </th>
        }
      </tr>
    </ng-template>

    <ng-template pTemplate="body" let-contract>
      <tr class="text-sm">
        <td>
          <p
            class="text-primary-500 font-semibold cursor-pointer hover:underline max-w-60 truncate"
            (click)="navigateToDetails(contract.id)"
            (keydown.enter)="navigateToDetails(contract.id)"
            tabindex="0"
            [pTooltip]="isTruncated(contractIdEl) ? contract.contractId : null"
            tooltipPosition="top"
            [tooltipStyleClass]="'text-xs text-neutral-700'"
            #contractIdEl
          >
            {{ contract.contractId || 'N/A' }}
          </p>
          @if (contract.renewedFrom) {
            <p
              class="text-sm text-neutral-500 text-nowrap truncate max-w-60 inline-block"
              [pTooltip]="
                isTruncated(renewedFromEl) ? contract.renewedFrom : null
              "
              tooltipPosition="top"
              [tooltipStyleClass]="'text-xs text-neutral-700'"
              #renewedFromEl
            >
              Renewed from ({{ contract.renewedFrom }})
            </p>
          }
        </td>
        <td>
          @if (contract.contractManagers.length > 0) {
            <p
              class="text-primary-500 truncate max-w-40"
              pTooltip="{{
                getContractManagersToolTip(contract.contractManagers) ||
                  'No email'
              }}"
              tooltipPosition="top"
              [tooltipStyleClass]="
                'text-xs text-neutral-700 whitespace-pre-wrap'
              "
              [autoHide]="false"
            >
              {{ getContractManagersName(contract.contractManagers) || 'N/A' }}
            </p>
          } @else {
            <span>N/A</span>
          }
        </td>
        <td class="text-center">{{ contract.startDate | date }}</td>
        <td class="text-center">{{ contract.endDate | date }}</td>
        <td class="text-center">
          {{ convertMinutesToHours(contract.totalLoggedMinutes) }}
        </td>
        <td class="text-center">{{ contract.resourceCount }}</td>
        <td>
          {{ getBillableStatus(contract) }}
        </td>
        <td>
          <p-chip
            [label]="contract.contractStatus.toUpperCase()"
            [styleClass]="
              getStatusChipStyle(contract.contractStatus.toUpperCase())
            "
          >
          </p-chip>
        </td>
        <td class="text-center">
          <div class="relative inline-block">
            <button
              pButton
              type="button"
              icon="pi pi-ellipsis-h"
              class="p-button-rounded p-button-text p-button-plain"
              [pTooltip]="'Actions'"
              tooltipPosition="left"
              style="width: 2.5rem; height: 2.5rem"
              (click)="onMenuClick($event, contract, menu)"
              aria-label="menu"
            ></button>
            <p-menu
              #menu
              [model]="menuItems"
              [popup]="true"
              [styleClass]="'w-60 shadow-md border-0 text-sm'"
              appendTo="body"
            >
            </p-menu>
          </div>
        </td>
      </tr>
    </ng-template>

    <ng-template pTemplate="emptymessage">
      <tr>
        <td colspan="8" class="text-center p-6">
          <p class="text-lg text-gray-500">No contracts found.</p>
        </td>
      </tr>
    </ng-template>

    <!-- Loading Template -->
    <ng-template pTemplate="loadingbody">
      @for (_ of skeletonRows; track $index) {
        <tr>
          @for (col of contractColumnHeader; track col) {
            <td class="text-sm">
              <p-skeleton width="100%" height="1.5rem"></p-skeleton>
            </td>
          }
        </tr>
      }
    </ng-template>
  </p-table>
</div>
<p-dialog
  header="Manual Action Required"
  [(visible)]="showTaskWarningDialog"
  [modal]="true"
  [closable]="false"
  class="manual-warning-dialog"
>
  <div class="p-text-center">
    <p class="message-text">
      The associated contract tasks are inactive. You will need to activate them
      manually on need basis.
    </p>
    <div class="flex justify-end mt-4">
      <button
        pButton
        label="OK"
        class="p-button-sm p-button-info font-medium"
        (click)="showTaskWarningDialog = false"
      ></button>
    </div>
  </div>
</p-dialog>

<p-confirmDialog [appendTo]="'body'"></p-confirmDialog>
<p-toast [preventOpenDuplicates]="true"></p-toast>
