import { CommonModule, DatePipe, TitleCasePipe } from '@angular/common';
import {
  Component,
  computed,
  EnvironmentInjector,
  input,
  OnInit,
  runInInjectionContext,
} from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { injectMutation } from '@tanstack/angular-query-experimental';
import { ConfirmationService, MenuItem, MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { ChipModule } from 'primeng/chip';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { DialogService } from 'primeng/dynamicdialog';
import { InputTextModule } from 'primeng/inputtext';
import { Menu, MenuModule } from 'primeng/menu';
import { SkeletonModule } from 'primeng/skeleton';
import { TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { TooltipModule } from 'primeng/tooltip';
import {
  ContractActionType,
  ContractBudgetDetail,
  ContractManager,
  ContractsById,
  ContractStatus,
} from '../../services/contract/contract.model';
import { ContractService } from '../../services/contract/contract.service';
import { Manager, ProjectStatus } from '../../services/project/project.model';
import { ProjectsService } from '../../services/project/project.service';
import { getStatusChipStyle } from '../../utils/chip.utils';
import { ContractFormComponent } from '../contract-form/contract-form.component';
import { ContractActivationDialogComponent } from '../contract-activation-dialog/contract-activation-dialog.component';
import { StringUtilsService } from '../../services/stringutils/stringutils.service';
import { convertMinutesToHours } from '../../utils/date.utils';

@Component({
  selector: 'tms-contracts-table',
  standalone: true,
  imports: [
    CommonModule,
    DialogModule,
    TableModule,
    ButtonModule,
    MenuModule,
    TooltipModule,
    InputTextModule,
    SkeletonModule,
    DropdownModule,
    ChipModule,
    ConfirmDialogModule,
    ToastModule,
  ],
  providers: [ConfirmationService, MessageService, TitleCasePipe, DatePipe],
  templateUrl: './contracts-table.component.html',
  styleUrl: './contracts-table.component.css',
})
export class ContractsTableComponent implements OnInit {
  projectId = input<string>('');
  skeletonRows = Array(3);
  menuItems: MenuItem[] = [];
  showTaskWarningDialog = false;

  getStatusChipStyle = getStatusChipStyle;
  convertMinutesToHours = convertMinutesToHours;

  contractsQuery = computed(() =>
    this.contractService.contractsByProjectIdQuery(this.projectId())
  );

  isLoading = computed(() => this.contractsQuery().isLoading());
  hasError = computed(() => this.contractsQuery().isError());
  contracts = computed(() =>
    (this.contractsQuery().data() ?? []).map((contract: ContractsById) => ({
      ...contract,
      // Add resourceCount to each contract for easier access in the UI
      resourceCount: contract.contractResources?.length ?? 0,
    }))
  );
  project = computed(() =>
    this.projectService.projectQuery(this.projectId()).data()
  );
  contractColumnHeader = [
    { field: 'contractId', header: 'Contract ID', sortable: true, class: '' },
    {
      field: 'contractManagers',
      header: 'Contract Manager',
      sortable: true,
      class: '',
    },
    {
      field: 'startDate',
      header: 'Start Date',
      sortable: true,
      class: 'text-center',
    },
    {
      field: 'endDate',
      header: 'End Date',
      sortable: true,
      class: 'text-center',
    },
    {
      field: 'totalLoggedMinutes',
      header: 'Total Hours',
      sortable: true,
      class: 'text-center',
    },
    { field: 'resourceCount', header: 'Resources', sortable: true },
    {
      field: 'isBillable',
      header: 'Billable',
      sortable: false,
      class: 'text-center',
    },
    {
      field: 'contractStatus',
      header: 'Status',
      sortable: true,
      class: 'text-center',
    },
    {
      field: 'actions',
      header: 'Actions',
      sortable: false,
      class: 'text-center',
    },
  ];

  constructor(
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
    private contractService: ContractService,
    private projectService: ProjectsService,
    private injector: EnvironmentInjector,
    private router: Router,
    public stringUtilsServce: StringUtilsService,
    private activatedRoute: ActivatedRoute,
    private dialogService: DialogService,
    private titleCasePipe: TitleCasePipe,
    private datePipe: DatePipe
  ) {}

  ngOnInit() {
    this.contractsQuery().refetch();
  }

  getBillableStatus(contract: ContractsById): string {
    if (
      contract.contractBudgetDetail &&
      Array.isArray(contract.contractBudgetDetail)
    ) {
      return contract.contractBudgetDetail.some(
        (item: ContractBudgetDetail) => item.isBillable
      )
        ? 'Yes'
        : 'No';
    }
    return 'No';
  }

  navigateToDetails(contractId: string) {
    this.router.navigate(['contracts', contractId], {
      relativeTo: this.activatedRoute,
    });
  }

  /**
   * Handles menu click events for a specific contract.
   * Dynamically generates and displays menu options based on the contract status.
   *
   * @param {Event} event - The click event triggering the menu.
   * @param {ContractsById} contract - The contract for which menu options are generated.
   * @param {Menu} menu - The menu instance to be populated and displayed.
   */
  onMenuClick(event: Event, contract: ContractsById, menu: Menu) {
    // Dynamically set menu items based on the contract
    menu.model = this.getMenuItems(contract);
    menu.show(event);
  }

  /**
   * Generates a list of menu items based on the status of a given contract.
   *
   * @param {ContractsById} contract - The contract used to determine menu options.
   * @returns {MenuItem[]} - An array of menu items including actions like Complete, Renew, Edit, and Delete.
   */
  getMenuItems(contract: ContractsById): MenuItem[] {
    const menuItems: MenuItem[] = [];

    // Check contract status to determine menu items
    switch (contract.contractStatus) {
      case ContractStatus.active:
        menuItems.push(
          {
            label: 'Complete Contract',
            icon: 'pi pi-check-circle',
            command: () =>
              this.confirmAction(
                () => this.completeContract(contract),
                'complete',
                contract
              ),
          },
          {
            label: 'Deactivate Contract',
            icon: 'pi pi-ban',
            command: () =>
              this.confirmAction(
                () => this.deactivateContract(contract),
                'deactivate',
                contract
              ),
          }
        );
        break;

      // @ts-expect-error Intentional fallthrough
      case ContractStatus.inActive:
        menuItems.push({
          label: 'Activate Contract',
          icon: 'pi pi-caret-right',
          command: () => this.openActivateContractDialog(contract),
        });
      case ContractStatus.completed:
        menuItems.push({
          label: 'Renew Contract',
          icon: 'pi pi-refresh',
          command: () =>
            this.confirmAction(
              () => this.renewContract(contract),
              'renew',
              contract
            ),
        });
        break;
    }

    menuItems.push({
      label: 'Edit Contract',
      icon: 'pi pi-pencil',
      command: () => this.editContract(contract),
    });

    return menuItems;
  }

  /**
   * Displays a confirmation dialog for contract actions complete, renew, or activate.
   * Executes the respective action upon confirmation.
   *
   * @param {string} actionType - The type of action (e.g., 'complete', 'renew', 'delete').
   * @param {ContractsById} contract - The contract for which the action is performed.
   */
  confirmAction(
    actionFn: () => void,
    actionType: ContractActionType,
    contract: ContractsById
  ) {
    this.confirmationService.confirm({
      message: `Are you sure you want to ${actionType} contract <b>${contract.contractId}</b>?`,
      header: `${this.stringUtilsServce.capitalizeFirstLetter(actionType)} Contract`,
      acceptButtonStyleClass: `${actionType === 'deactivate' ? 'p-button-danger' : 'p-button-success'}`,
      rejectButtonStyleClass: 'p-button-primary',
      acceptIcon: 'none',
      rejectIcon: 'none',
      icon: 'pi pi-exclamation-circle text-base',
      accept: actionFn,
      acceptLabel: this.titleCasePipe.transform(actionType),
      rejectLabel: 'Cancel',
      defaultFocus: 'none',
    });
  }

  /**
   * Renews the given contract by calling the corresponding API mutation.
   * Displays success or error messages based on the API response.
   *
   * @param {ContractsById} contract - The contract to be renewed.
   */
  renewContract(contract: ContractsById) {
    const renewQueryOptions = this.contractService.renewContractQueryOptions(
      contract.id
    );

    runInInjectionContext(this.injector, () => {
      const renewContractMutation = injectMutation(() => renewQueryOptions);
      renewContractMutation.mutate(contract.id, {
        onSuccess: () => {
          this.contractsQuery().refetch();
          this.messageService.add({
            severity: 'success',
            summary: 'Renew contract',
            detail: `Contract (${contract.contractId}) renewed successfully.`,
          });
        },
        onError: () => {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: `Failed to renew contract (${contract.contractId}). Please try again.`,
          });
        },
      });
    });
  }

  /**
   * Checks if a contract's start date is in the future.
   *
   * @param {ContractsById} contract - The contract to check.
   * @returns {boolean} - True if the start date is in the future, false otherwise.
   */
  isContractStartDateInFuture(contract: ContractsById): boolean {
    if (!contract.startDate) return false;

    const startDate = new Date(contract.startDate);
    const currentDate = new Date();

    // Normalize both dates to the start of the day for accurate comparison
    startDate.setHours(0, 0, 0, 0);
    currentDate.setHours(0, 0, 0, 0);

    return startDate > currentDate;
  }

  /**
   * Marks the given contract as complete by calling the corresponding API mutation.
   * Displays success or error messages based on the API response.
   *
   * @param {ContractsById} contract - The contract to be marked as complete.
   */
  completeContract(contract: ContractsById) {
    // Check if contract start date is in the future
    if (this.isContractStartDateInFuture(contract)) {
      const formattedDate =
        this.datePipe.transform(contract.startDate, 'MMM dd, yyyy') || 'N/A';
      this.messageService.add({
        severity: 'error',
        summary: 'Cannot Complete Contract',
        detail: `Cannot complete contract (${contract.contractId}) because its start date (${formattedDate}) is in the future. Please wait until the contract start date to complete it.`,
      });
      return;
    }

    const completeQueryOptions =
      this.contractService.completeContractQueryOptions(contract.id);

    runInInjectionContext(this.injector, () => {
      const completeContractMutation = injectMutation(
        () => completeQueryOptions
      );
      completeContractMutation.mutate(contract.id, {
        onSuccess: () => {
          this.contractsQuery().refetch();
          this.messageService.add({
            severity: 'success',
            summary: 'Complete contract',
            detail: `Successfully marked contract (${contract.contractId}) as complete.`,
          });
        },
        onError: () =>
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: `Failed to mark contract (${contract.contractId}) as complete.`,
          }),
      });
    });
  }

  /**
   * Marks the given inActive as deactivated by calling the corresponding API mutation.
   * Displays success or error messages based on the API response.
   *
   * @param {ContractsById} contract - The contract to be marked as inActive.
   */
  deactivateContract(contract: ContractsById) {
    const deactivateContractQueryOptions =
      this.contractService.deactivateContractQueryOptions(contract.id);

    runInInjectionContext(this.injector, () => {
      const deactivateContractMutation = injectMutation(
        () => deactivateContractQueryOptions
      );
      deactivateContractMutation.mutate(contract.id, {
        onSuccess: (data) => {
          this.contractsQuery().refetch();
          this.messageService.add({
            severity: 'success',
            summary: 'Deactivate contract',
            detail: data.message,
          });
        },
        onError: (error: Error) =>
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: error.message,
          }),
      });
    });
  }

  editContract(contract: ContractsById) {
    const dialogRef = this.dialogService.open(ContractFormComponent, {
      data: {
        contract: contract,
        project: this.project(),
      },
      header: 'Update Contract',
      dismissableMask: false,
      styleClass: 'overflow-hidden w-[90vw] md:w-[50vw]',
    });

    dialogRef.onClose.subscribe((result) => {
      if (result) {
        if (result.type === 'success') {
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: result.message,
          });
        } else if (result.type === 'error') {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: result.message,
          });
        }
      }
    });
  }

  /**
   * Opens a dialog to activate a contract.
   *
   * This function opens a dialog using the `ContractActivationDialogComponent` to confirm
   * and proceed with the activation of the contract. It passes the contract data to the dialog
   * and handles the dialog's close event to display success or error messages based on the result.
   *
   * @param contract The contract to be activated, of type `ContractsById`. This data is passed to the dialog.
   */
  openActivateContractDialog(contract: ContractsById) {
    // Check if contract start date is in the future
    if (this.isContractStartDateInFuture(contract)) {
      const formattedDate =
        this.datePipe.transform(contract.startDate, 'MMM dd, yyyy') || 'N/A';

      this.messageService.add({
        severity: 'error',
        summary: 'Cannot Activate Contract',
        detail: `Cannot activate contract (${contract.contractId}) because its start date (${formattedDate}) is in the future. Please wait until the contract start date to activate it.`,
      });
      return;
    }

    const dialogRef = this.dialogService.open(
      ContractActivationDialogComponent,
      {
        header: 'Activate Contract',
        dismissableMask: false,
        focusOnShow: false,
        data: {
          contract: contract,
          projectId: this.projectId(),
        },
      }
    );
    dialogRef.onClose.subscribe((result) => {
      if (result) {
        if (result.type === 'success') {
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: result.message,
          });
          this.showTaskWarningDialog = true;
        } else if (result.type === 'error') {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: result.message,
          });
        }
      }
    });
  }

  getContractManagersToolTip(contractManagers: ContractManager[]) {
    return contractManagers
      .map((manager) => `${manager.name} (${manager.email})`)
      .join('\n');
  }

  getContractManagersName(contractManagers: ContractManager[]) {
    return contractManagers.map((manager) => manager.name).join(', ');
  }

  // Checks if the content of an element is truncated due to overflow by comparing its visible width (offsetWidth) with its total content width (scrollWidth).
  isTruncated(element: HTMLElement): boolean {
    if (!element) return false;
    return element.offsetWidth < element.scrollWidth;
  }
}
