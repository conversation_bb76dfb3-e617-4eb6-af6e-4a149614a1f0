import { HttpClient, HttpHandler } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ActivatedRoute } from '@angular/router';
import { QueryClient } from '@tanstack/angular-query-experimental';
import { ConfirmationService, MessageService } from 'primeng/api';
import { of } from 'rxjs';
import { ContractsById } from '../../services/contract/contract.model';
import { ContractsTableComponent } from './contracts-table.component';
import { DialogService } from 'primeng/dynamicdialog';

describe('ContractsTableComponent', () => {
  let component: ContractsTableComponent;
  let fixture: ComponentFixture<ContractsTableComponent>;
  let mockConfirmationService: jasmine.SpyObj<ConfirmationService>;
  let mockMessageService: jasmine.SpyObj<MessageService>;

  beforeEach(async () => {
    mockConfirmationService = jasmine.createSpyObj('ConfirmationService', [
      'confirm',
    ]);
    mockMessageService = jasmine.createSpyObj('MessageService', ['add']);

    await TestBed.configureTestingModule({
      imports: [ContractsTableComponent],
      providers: [
        { provide: ConfirmationService, useValue: mockConfirmationService },
        { provide: MessageService, useValue: mockMessageService },
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: { params: { projectId: '123' } }, // Mock params
            paramMap: of({ get: () => '123' }), // Mock observable
          },
        },
        HttpClient,
        HttpHandler,
        QueryClient,
        provideHttpClientTesting,
        DialogService,
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ContractsTableComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should return correct menu items for active contract', () => {
    const contract: ContractsById = {
      id: '1',
      contractId: 'C001',
      contractStatus: 'active',
      startDate: '',
      endDate: '',
      renewedFrom: null,
      total: 0,
      project: {
        id: '',
        projectName: '',
        contactName: null,
        contactEmail: null,
      },
      contractResources: ['0'],
    };

    const menuItems = component.getMenuItems(contract);
    expect(menuItems.length).toBe(3);
    expect(menuItems[0].label).toBe('Complete Contract');
    expect(menuItems[1].label).toBe('Deactivate Contract');
    expect(menuItems[2].label).toBe('Edit Contract');
  });

  it('should return correct menu items for in-active contract', () => {
    const contract: ContractsById = {
      id: '1',
      contractId: 'C001',
      contractStatus: 'inActive',
      startDate: '',
      endDate: '',
      renewedFrom: null,
      total: 0,
      project: {
        id: '',
        projectName: '',
        contactName: null,
        contactEmail: null,
      },
      contractResources: ['0'],
    };

    const menuItems = component.getMenuItems(contract);
    expect(menuItems.length).toBe(3);
    expect(menuItems[0].label).toBe('Renew Contract');
    expect(menuItems[1].label).toBe('Activate Contract');
    expect(menuItems[2].label).toBe('Edit Contract');
  });

  it('should return correct menu items for completed contract', () => {
    const contract: ContractsById = {
      id: '2',
      contractId: 'C002',
      contractStatus: 'completed',
      startDate: '',
      endDate: '',
      renewedFrom: null,
      total: 0,
      project: {
        id: '',
        projectName: '',
        contactName: null,
        contactEmail: null,
      },
      contractResources: ['0'],
    };

    const menuItems = component.getMenuItems(contract);
    expect(menuItems.length).toBe(2);
    expect(menuItems[0].label).toBe('Renew Contract');
    expect(menuItems[1].label).toBe('Edit Contract');
  });

  it('should handle menu click and update menu items', () => {
    const contract: ContractsById = {
      id: '4',
      contractId: 'C004',
      contractStatus: 'active',
      startDate: '',
      endDate: '',
      renewedFrom: null,
      total: 0,
      project: {
        id: '',
        projectName: '',
        contactName: null,
        contactEmail: null,
      },
      contractResources: ['0'],
    };

    const menuMock = jasmine.createSpyObj('Menu', ['show']);
    component.onMenuClick(new MouseEvent('click'), contract, menuMock);

    expect(menuMock.model.length).toBe(3);
    expect(menuMock.show).toHaveBeenCalled();
  });
});
