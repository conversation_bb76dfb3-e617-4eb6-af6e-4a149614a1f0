<!-- 
This HTML template defines the structure of the task creation form within the TaskFormComponent.
It utilizes Angular's reactive forms and PrimeNG components to provide a user-friendly interface 
for inputting task details.-->
<form
  class="flex flex-col space-y-4 w-full"
  [formGroup]="taskFormGroup"
  (ngSubmit)="submitTaskForm()"
>
  <div class="flex flex-col gap-2">
    <label for="taskName" class="text-sm text-neutral-500"
      >Task Name<span class="text-lg text-red-500">*</span></label
    >
    <input
      pInputText
      formControlName="taskName"
      id="taskName"
      placeholder="Enter task name"
      [style]="{ width: '100%' }"
    />
  </div>

  <div class="flex flex-col gap-2">
    <label for="contractResources" class="text-sm text-neutral-500">
      Assignee<span class="font-bold text-red-500">*</span>
    </label>
    <p-multiSelect
      placeholder="Select employee"
      formControlName="contractResources"
      [options]="assigneeOptions()"
      [resetFilterOnHide]="true"
      optionLabel="name"
      display="chip"
      [maxSelectedLabels]="null"
      [style]="{ width: '100%' }"
    ></p-multiSelect>
  </div>

  <div class="grid md:grid-cols-2 gap-2">
    <div class="flex flex-col gap-2">
      <label for="frequency" class="text-sm text-neutral-500">
        Frequency<span class="font-bold text-red-500">*</span>
      </label>
      <p-dropdown
        #frequency
        formControlName="frequency"
        [options]="frequencyDropdownOptions"
        optionLabel="name"
        placeholder="Select frequency"
        [style]="{ width: '100%' }"
      />
    </div>
    <div class="flex flex-col gap-2">
      <label for="estimationDuration" class="text-sm text-neutral-500"
        >Estimated Duration<span class="text-sm text-red-500">*</span></label
      >
      <p-inputNumber
        id="estimationDuration"
        inputId="integeronly"
        formControlName="estimationDuration"
        placeholder="Enter estimated duration"
        [style]="{ width: '100%' }"
        [useGrouping]="false"
        [max]="9999"
        maxlength="4"
      />
    </div>
  </div>

  <div class="flex flex-col gap-2">
    <label for="description" class="text-sm text-neutral-500"
      >Description<span class="text-lg text-red-500">*</span></label
    >
    <textarea
      pInputTextarea
      id="description"
      formControlName="description"
      rows="4"
      placeholder="Enter task details"
      [style]="{ width: '100%' }"
    ></textarea>
  </div>

  <div class="flex flex-row-reverse">
    <p-button
      type="submit"
      [label]="
        formSubmitButtonLabelService.getFormSubmitButtonLabel(
          isSubmitting,
          isEditMode
        )
      "
      [icon]="
        formSubmitButtonLabelService.getFormSubmitButtonIcon(isSubmitting)
      "
      [disabled]="
        taskFormGroup.invalid ||
        isSubmitting ||
        !taskFormGroup.get('description')?.value.trim() ||
        !taskFormGroup.get('taskName')?.value.trim()
      "
      styleClass="submit-button disabled:cursor-not-allowed disabled:bg-neutral-400 disabled:border-neutral-400"
      aria-label="Submit"
    ></p-button>
  </div>
</form>
