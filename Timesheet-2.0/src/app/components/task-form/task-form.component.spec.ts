import { ComponentFixture, TestBed } from '@angular/core/testing';
import { TaskFormComponent } from './task-form.component';
import { QueryClient } from '@tanstack/angular-query-experimental';
import { HttpClient, HttpHandler } from '@angular/common/http';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { ComponentRef } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { ContractService } from '../../services/contract/contract.service';
import { TaskService } from '../../services/task/task.service';
import { FormSubmitButtonLabelService } from '../../services/formsubmitbuttonlabel/formsubmitbuttonlabel.service';

describe('TaskFormComponent', () => {
  let component: TaskFormComponent;
  let fixture: ComponentFixture<TaskFormComponent>;
  let componentRef: ComponentRef<TaskFormComponent>;

  const mockMutation = {
    mutate: jasmine.createSpy('mutate'),
  };

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TaskFormComponent],
      providers: [
        HttpClient,
        QueryClient,
        HttpHandler,
        FormBuilder,
        {
          provide: DynamicDialogConfig,
          useValue: {
            data: {
              contractId: 'mockContractId',
              projectId: 'mockProjectId',
              customContractId: '12345',
            },
          },
        },
        DynamicDialogRef,
        ContractService,
        TaskService,
        FormSubmitButtonLabelService,
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(TaskFormComponent);
    component = fixture.componentInstance;
    componentRef = fixture.componentRef;

    // Replace `addTaskQuery` with a mock
    component.addTaskQuery = mockMutation as any;

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize the form with default values', () => {
    const form = component.taskFormGroup;
    expect(form).toBeDefined();
    expect(form.get('taskName')?.value).toBe('');
    expect(form.get('contractResources')?.value).toBe('');
    expect(form.get('estimationDuration')?.value).toBe('');
    expect(form.get('frequency')?.value).toBe('');
    expect(form.get('description')?.value).toBe('');
    expect(form.valid).toBeFalse(); // Form should be invalid initially
  });

  it('should call addTaskQuery.mutate with correct data on valid form submission', () => {
    component.taskFormGroup.setValue({
      taskName: 'Test Task',
      contractResources: [{ id: 'resource1', name: 'Resource 1' }],
      estimationDuration: 8,
      frequency: { name: 'Days', value: 'days' },
      description: 'This is a test task',
    });

    component.submitTaskForm();

    expect(mockMutation.mutate).toHaveBeenCalledWith({
      taskName: 'Test Task',
      projectId: 'mockProjectId',
      customContractId: '12345',
      contractResources: ['resource1'],
      estimationDuration: 8,
      frequency: 'days',
      description: 'This is a test task',
    });
    expect(component.taskFormGroup.disabled).toBeTrue();
  });

  it('should disable the submit button when the form is invalid', () => {
    const submitButton: HTMLButtonElement = fixture.nativeElement.querySelector(
      'p-button[aria-label="Submit"] button'
    );

    // Initially, the form is invalid
    expect(component.taskFormGroup.invalid).toBeTrue();
    expect(submitButton.disabled).toBeTrue();

    // Update the form to make it valid
    component.taskFormGroup.setValue({
      taskName: 'Test Task',
      contractResources: [{ id: 'resource1', name: 'Resource 1' }],
      estimationDuration: 8,
      frequency: { name: 'Days', value: 'days' },
      description: 'This is a valid description',
    });

    fixture.detectChanges(); // Trigger change detection

    // Now, the form should be valid, and the button should be enabled
    expect(component.taskFormGroup.valid).toBeTrue();
    expect(submitButton.disabled).toBeFalse();
  });
});
