import { Component, computed } from '@angular/core';
import { DropdownModule } from 'primeng/dropdown';
import { InputTextModule } from 'primeng/inputtext';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { MultiSelectModule } from 'primeng/multiselect';
import { ContractService } from '../../services/contract/contract.service';
import {
  injectMutation,
  injectQuery,
} from '@tanstack/angular-query-experimental';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { CommonModule } from '@angular/common';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { TaskService } from '../../services/task/task.service';
import {
  CreateTask,
  TasksByContractId,
  UpdateTask,
} from '../../services/task/task.model';
import { InputNumberModule } from 'primeng/inputnumber';
import { FormSubmitButtonLabelService } from '../../services/formsubmitbuttonlabel/formsubmitbuttonlabel.service';

/**
 * Frequency interface defines the structure for frequency options.
 */
interface Frequency {
  name: 'Days' | 'Hours';
  value: 'days' | 'hours';
}

/**
 * ContractResource interface defines the structure for contract resources.
 */
interface ContractResource {
  id: string;
  name: string;
}

/**
 * TaskFormData interface defines the structure for task form data.
 */
interface TaskFormData {
  taskName: string;
  customContractId: string;
  contractResources: ContractResource[];
  estimationDuration: number;
  frequency: Frequency;
  description: string;
}

/**
 * TaskFormComponent is an Angular component responsible for managing the task creation form.
 * It utilizes various Angular modules and PrimeNG components to provide a user-friendly interface
 * for adding tasks associated with specific contracts.
 */
@Component({
  selector: 'tms-task-form',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    ButtonModule,
    InputTextModule,
    InputNumberModule,
    DropdownModule,
    MultiSelectModule,
    InputTextareaModule,
  ],
  templateUrl: './task-form.component.html',
  styleUrl: './task-form.component.css',
})
export class TaskFormComponent {
  taskFormGroup: FormGroup;
  isEditMode: boolean = false;
  isSubmitting: boolean = false;
  frequencyDropdownOptions: Frequency[] = [
    { name: 'Days', value: 'days' },
    { name: 'Hours', value: 'hours' },
  ];

  constructor(
    private contractService: ContractService,
    private dialogConfig: DynamicDialogConfig,
    private formBuilder: FormBuilder,
    public formSubmitButtonLabelService: FormSubmitButtonLabelService,
    private taskService: TaskService,
    private dialogRef: DynamicDialogRef
  ) {
    this.taskFormGroup = this.initializeForm();
    this.isEditMode = this.dialogConfig.data.isEditMode;
  }

  /**
   * Populate the form with the data on initialization.
   */
  ngAfterContentInit() {
    this.loadTaskFormData();
  }

  /**
   * Initializes the reactive form with validation.
   * @returns FormGroup - The initialized form group.
   */
  private initializeForm(): FormGroup {
    return this.formBuilder.group({
      taskName: ['', Validators.required],
      contractResources: ['', Validators.required],
      estimationDuration: ['', Validators.required],
      frequency: ['', Validators.required],
      description: ['', Validators.required],
    });
  }

  /**
   * Loads existing form data into the reactive form if provided.
   */
  private loadTaskFormData() {
    const taskData: TasksByContractId = this.dialogConfig.data?.taskData;

    if (taskData && this.isEditMode) {
      this.taskFormGroup.patchValue({
        taskName: taskData.taskName,
        contractResources: taskData.contractResource.map((resource) => ({
          id: resource.id,
          name: resource.name,
        })),
        estimationDuration: taskData.estimationTime,
        frequency: this.frequencyDropdownOptions.find(
          (option) => option.value === taskData.frequency
        ),
        description: taskData.description,
      });
    }
  }

  /**
   * Query to fetch contract resources based on contract ID.
   */
  contractResourcesQuery = injectQuery(() => {
    return {
      queryKey: ['resourceByContractId', this.dialogConfig.data.contractId],
      queryFn: () => {
        return this.contractService.getResourcesByContractId(
          this.dialogConfig.data.contractId
        );
      },
    };
  });

  /**
   * Mutation to add a new task based on form input.
   */
  addTaskQuery = injectMutation(() => ({
    mutationKey: ['task', this.dialogConfig.data.contractId],
    mutationFn: (taskData: CreateTask) => this.taskService.createTask(taskData),
    onSuccess: () => {
      this.taskService
        .tasksQuery(
          this.dialogConfig.data.projectId,
          this.dialogConfig.data.contractId
        )
        .refetch();
      this.dialogRef.close({
        type: 'success',
        severity: 'success',
        message: 'Task added successfully!',
        summary: 'Task added.',
      });
    },
    onError: (error) => {
      console.error('Error adding task:', error);
      this.dialogRef.close({
        type: 'error',
        severity: 'error',
        message: error.message,
        summary: 'Error',
      });
    },
  }));

  /**
   * Mutation to update an existing task based on form input.
   */
  updateTaskQuery = injectMutation(() => ({
    mutationKey: ['updateTask', this.dialogConfig.data?.taskData?.id],
    mutationFn: (updateTaskData: UpdateTask) =>
      this.taskService.updateTask(updateTaskData),
    onSuccess: () => {
      this.taskService
        .tasksQuery(
          this.dialogConfig.data.projectId,
          this.dialogConfig.data.contractId
        )
        .refetch();
      this.dialogRef.close({
        type: 'success',
        severity: 'success',
        message: 'Task updated successfully!',
        summary: 'Task updated.',
      });
    },
    onError: (error) => {
      console.error('Error updating task:', error);
      this.dialogRef.close({
        type: 'error',
        severity: 'error',
        message: error.message,
        summary: 'Error',
      });
    },
  }));

  /**
   * Computes available assignee options based on fetched contract resources.
   */
  assigneeOptions(): ContractResource[] {
    // Get selected resource IDs from the form
    const selectedResources = this.taskFormGroup.value.contractResources || [];
    const selectedIds = new Set(selectedResources.map((r: ContractResource) => r.id));

    const allResources =
      this.contractResourcesQuery
        .data()
        ?.flatMap((contractResource) =>
          contractResource?.contractResource?.map((resource) => ({
            id: resource?.id,
            name: resource?.contractResource?.name ?? 'Unknown',
          }))
        ) || [];

    // Separate selected and unselected items
    const selectedOptions = allResources.filter((r) => selectedIds.has(r.id));
    const unselectedOptions = allResources.filter((r) => !selectedIds.has(r.id));

    return [...selectedOptions, ...unselectedOptions];
  }


  /**
   * Submits the task form and triggers mutation to add a new task.
   */
  submitTaskForm() {
    if (this.taskFormGroup.valid) {
      this.isSubmitting = true;
      const formValue: TaskFormData = this.taskFormGroup.value;

      const taskData: CreateTask = {
        taskName: formValue.taskName.trim(),
        projectId: this.dialogConfig.data.projectId,
        customContractId: this.dialogConfig.data.customContractId,
        contractResources: formValue.contractResources.map(
          (contractResource) => contractResource.id
        ),
        estimatedTime: formValue.estimationDuration ?? null,
        frequency: formValue.frequency.value,
        description: formValue.description.trim(),
      };

      const updateTaskData: UpdateTask = {
        taskId: this.dialogConfig.data?.taskData?.id,
        ...taskData,
      };
      if (this.isEditMode) {
        this.updateTaskQuery.mutate(updateTaskData);
      } else {
        this.addTaskQuery.mutate(taskData);
      }
      this.taskFormGroup.disable();
    }
  }
}
