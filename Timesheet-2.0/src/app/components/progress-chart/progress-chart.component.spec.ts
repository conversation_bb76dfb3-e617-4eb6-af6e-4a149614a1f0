import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ProgressChartComponent } from './progress-chart.component';

describe('ProgressChartComponent', () => {
  let component: ProgressChartComponent;
  let fixture: ComponentFixture<ProgressChartComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ProgressChartComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(ProgressChartComponent);
    component = fixture.componentInstance;
    fixture.componentRef.setInput('actualValue', 10);
    fixture.componentRef.setInput('baseValue', 100);
    fixture.componentRef.setInput('suffix', '%');
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('actualValue should be defined', () => {
    fixture.componentRef.setInput('actualValue', 10);
    expect(component.actualValue()).toBeDefined();
    expect(component.actualValue()).toBe(10);
  });

  it('baseValue should be defined', () => {
    fixture.componentRef.setInput('baseValue', 100);
    expect(component.baseValue()).toBeDefined();
    expect(component.baseValue()).toBe(100);
  });

  it('should able to set the suffix', () => {
    fixture.componentRef.setInput('suffix', '%');
    expect(component.suffix()).toBe('%');
  });

  it('should calculate the percentage correctly', () => {
    expect(component.percentage()).toBe(10);
  });
});
