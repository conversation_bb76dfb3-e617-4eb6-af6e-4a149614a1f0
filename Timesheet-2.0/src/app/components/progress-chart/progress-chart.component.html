<div class="w-full my-0 mx-auto relative chart-container">
  <svg viewBox="0 0 36 36" class="w-full h-auto">
    <path
      class="fill-none stroke-gray-300 stroke-[3.8]"
      d="M18 2.0845
                 a 15.9155 15.9155 0 0 1 0 31.831
                 a 15.9155 15.9155 0 0 1 0 -31.831"
    />
    @if (percentage()) {
      <path
        class="fill-none stroke-primary-500 stroke-[3.8] stroke-linecap-round animate-progress"
        [attr.stroke-dasharray]="getStrokeDashArray()"
        d="M18 2.0845
                         a 15.9155 15.9155 0 0 1 0 31.831
                         a 15.9155 15.9155 0 0 1 0 -31.831"
      />
    }
  </svg>
  <span
    class="text-primary-500 font-bold absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 font-size"
    [ngClass]="{
      'text-gray-500': percentage() === 0,
      'text-primary-500': percentage() > 0,
    }"
  >
    {{ actualValue() + suffix().toLocaleLowerCase() }}
  </span>
</div>
