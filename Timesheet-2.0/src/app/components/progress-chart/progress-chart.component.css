.animate-progress {
  stroke-linecap: round;
  animation: progress 1s ease-in-out forwards;
}

.resize-animation {
  transition: font-size 0.1s ease-in-out;
}

.chart-container {
  container-type: inline-size;
  container-name: chart-container;
}

@container chart-container (inline-size > 100px) {
  .font-size {
    font-size: 3em;
  }
}

@container chart-container (inline-size > 300px) {
  .font-size {
    font-size: 5em;
  }
}

@container chart-container (inline-size > 500px) {
  .font-size {
    font-size: 7em;
  }
}

@container chart-container (inline-size > 800px) {
  .font-size {
    font-size: 9em;
  }
}

@container chart-container (inline-size > 1024px) {
  .font-size {
    font-size: 11em;
  }
}

@keyframes progress {
  0% {
    stroke-dasharray: 0, 100;
  }
}
