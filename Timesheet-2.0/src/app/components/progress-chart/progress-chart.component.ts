import { CommonModule } from '@angular/common';
import { Component, computed, input } from '@angular/core';

@Component({
  selector: 'tms-progress-chart',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './progress-chart.component.html',
  styleUrl: './progress-chart.component.css',
})
export class ProgressChartComponent {
  actualValue = input.required<number>();
  baseValue = input.required<number>();
  suffix = input<string>('');
  percentage = computed(
    () => +(this.actualValue() / this.baseValue()).toFixed(2) * 100
  );

  getStrokeDashArray = () => {
    return `${this.percentage()}, 100`;
  };
}
