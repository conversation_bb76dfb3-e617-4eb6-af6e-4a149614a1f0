import { CommonModule, TitleCasePipe } from '@angular/common';
import { Component, computed, input, signal } from '@angular/core';
import { ConfirmationService, MenuItem, MessageService } from 'primeng/api';
import { AvatarModule } from 'primeng/avatar';
import { ChipModule } from 'primeng/chip';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { Menu, MenuModule } from 'primeng/menu';
import { SkeletonModule } from 'primeng/skeleton';
import { TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { TooltipModule } from 'primeng/tooltip';
import { AuthService } from '../../services/auth/auth-service';
import {
  TaskActionType,
  TasksByContractId,
} from '../../services/task/task.model';
import { TaskService } from '../../services/task/task.service';
import { getStatusChipStyle } from '../../utils/chip.utils';
import { TaskFormComponent } from '../task-form/task-form.component';
import { DialogService } from 'primeng/dynamicdialog';
import { ContractService } from '../../services/contract/contract.service';
import { ContractStatus } from '../../services/contract/contract.model';

@Component({
  selector: 'tms-tasks-table',
  standalone: true,
  imports: [
    TableModule,
    CommonModule,
    ToastModule,
    SkeletonModule,
    TooltipModule,
    ChipModule,
    MenuModule,
    ConfirmDialogModule,
    AvatarModule,
    MenuModule,
  ],
  providers: [ConfirmationService, MessageService, TitleCasePipe],
  templateUrl: './tasks-table.component.html',
  styleUrl: './tasks-table.component.css',
})
export class TasksTableComponent {
  projectId = input<string>('');
  contractId = input<string>('');
  contractStatus = input<string>();
  skeletonRows = Array(3);
  menuItems: MenuItem[] = [];

  getStatusChipStyle = getStatusChipStyle;

  taskQuery = computed(() =>
    this.tasksService.tasksQuery(this.projectId(), this.contractId())
  );

  isValidIds = computed(() => !!this.projectId() && !!this.contractId());

  isLoading = computed(
    () => this.taskQuery().isLoading() || this.taskQuery().isFetching()
  );

  hasError = computed(() => this.taskQuery().isError());

  tasks = computed(() => {
    const contractResources = this.contractsQuery().data()?.contractResource;
    const resourceIds = contractResources?.map(
      (resource) => resource.contractResource.id
    );
    return (
      this.taskQuery()
        ?.data()
        ?.map((task) => {
          const filteredResource = task.contractResource.filter((resource) => {
            return resourceIds?.includes(resource.resourceId);
          });
          return {
            ...task,
            contractResource: filteredResource,
            contractResourceCount: filteredResource.length,
          };
        }) || []
    );
  });

  role = computed(() => this.authService.userRole());

  taskColumnHeader = [
    { field: 'taskName', header: 'Task Name', sortable: true, class: '' },
    {
      field: 'description',
      header: 'Description',
      sortable: false,
      class: '',
    },
    {
      field: 'estimatedTime',
      header: 'Estimated Time',
      sortable: false,
      class: '',
    },
    {
      field: 'contractResourceCount',
      header: 'Assignees',
      sortable: true,
      class: 'text-center',
    },
    {
      field: 'status',
      header: 'Status',
      sortable: false,
      class: 'align-status',
    },
    {
      field: 'actions',
      header: 'Actions',
      sortable: false,
      class: 'text-center',
    },
  ];

  taskAssigneeColumnHeader = [
    { field: 'name', header: 'Name', sortable: true, class: '' },
    { field: 'email', header: 'Email', sortable: true, class: '' },
    {
      field: 'phoneNumber',
      header: 'Phone Number',
      sortable: false,
      class: '',
    },
    {
      field: 'expectedHours',
      header: 'Expected Hours',
      sortable: true,
      class: 'text-center',
    },
  ];

  // Used by primeng table component to keep expanded rows
  expandedRows = signal({});

  selectedTasks = signal<TasksByContractId[]>([]);

  contractsQuery = computed(() =>
    this.contractService.contractsQuery(this.contractId())
  );

  constructor(
    private authService: AuthService,
    private tasksService: TaskService,
    private contractService: ContractService,
    private confirmationService: ConfirmationService,
    private messageService: MessageService,
    private dialogService: DialogService,
    private titleCasePipe: TitleCasePipe
  ) {}

  /**
   * Handles menu click events for a specific task.
   * Dynamically generates and displays menu options based on the task status.
   */
  onMenuClick(event: Event, task: TasksByContractId, menu: Menu) {
    // Dynamically set menu items based on the contract
    menu.model = this.getMenuItems(task);
    menu.show(event);
  }

  /**
   * Generates a list of menu items based on the task status.
   */
  getMenuItems(task: TasksByContractId): MenuItem[] {
    const menuItems: MenuItem[] = [];

    if (
      this.contractsQuery().data()?.contractStatus === ContractStatus.active
    ) {
      switch (task.taskStatus) {
        case 'active':
          menuItems.push(
            {
              label: 'Complete Task',
              icon: 'pi pi-check-circle',
              command: () =>
                this.confirmAction(
                  () => this.completeTask(task),
                  'complete',
                  task
                ),
            },
            {
              label: 'Deactivate Task',
              icon: 'pi pi-ban',
              command: () =>
                this.confirmAction(
                  () => this.deactivateTask(task),
                  'deactivate',
                  task
                ),
            }
          );
          break;

        case 'inActive':
          menuItems.push({
            label: 'Activate Task',
            icon: 'pi pi-caret-right',
            command: () =>
              this.confirmAction(
                () => this.activateTask(task),
                'activate',
                task
              ),
          });
          break;
      }
    }

    menuItems.push(
      {
        label: 'Edit Task',
        icon: 'pi pi-pencil',
        command: () => this.editTask(task),
      },
      {
        label: 'Delete Task',
        icon: 'pi pi-trash',
        command: () =>
          this.confirmAction(() => this.deleteTask(task), 'delete', task),
      }
    );

    return menuItems;
  }

  /**
   * Displays a confirmation dialog for task actions complete or delete.
   * Executes the respective action upon confirmation.
   */
  confirmAction(
    actionFn: () => void,
    actionType: TaskActionType,
    task: TasksByContractId
  ) {
    if (
      this.contractsQuery().data()?.contractStatus !== ContractStatus.active
    ) {
      this.messageService.add({
        severity: 'error',
        summary: 'Task Action',
        detail: `Cannot ${actionType} task in a ${this.contractsQuery().data()?.contractStatus?.toLowerCase()} contract.`,
      });
    } else {
      this.confirmationService.confirm({
        message: `Are you sure you want to ${actionType} task <b>${task.taskName}</b>?`,
        header: `${actionType.charAt(0).toUpperCase() + actionType.slice(1)} Task`,
        acceptButtonStyleClass: `${actionType === 'delete' || actionType === 'deactivate' ? 'p-button-danger' : 'p-button-success'}`,
        rejectButtonStyleClass: 'p-button-primary',
        acceptIcon: 'none',
        rejectIcon: 'none',
        icon: 'pi pi-exclamation-circle text-base',
        defaultFocus: 'none',
        accept: actionFn,
        acceptLabel: this.titleCasePipe.transform(actionType),
        rejectLabel: 'Cancel',
      });
    }
  }

  /**
   * Deletes a specified task, triggers a mutation to delete the task identified by resource ID
   * displays a toast messages for success and error responses.
   */
  deleteTask(task: TasksByContractId): void {
    const deleteTaskMutation = this.tasksService.deleteTasksQuery();

    deleteTaskMutation.mutate([task.id], {
      onSuccess: () => {
        this.tasksService
          .tasksQuery(this.projectId(), this.contractId())
          .refetch();
        this.messageService.add({
          severity: 'success',
          summary: 'Delete Task',
          detail: `Successfully deleted task "${task.taskName}".`,
        });
      },
      onError: () =>
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: `Failed to delete task "${task.taskName}".`,
        }),
    });
  }

  /**
   * Edits a task if the contract is active.
   *
   * This function checks the contract status. If the contract is not active,
   * it displays an error message. Otherwise, it opens the edit task form.
   */
  editTask(task: TasksByContractId): void {
    if (this.contractStatus() !== ContractStatus.active) {
      this.messageService.add({
        severity: 'error',
        summary: 'Cannot edit Task',
        detail: 'Cannot edit task: The contract is currently inactive.',
      });
    } else {
      this.openEditTaskForm(task);
    }
  }

  /**
   * Opens a dialog for editing a task associated with a specific contract and project.
   * The dialog allows users to modify task details and provides feedback upon completion.
   */
  openEditTaskForm(task: TasksByContractId): void {
    const dialogRef = this.dialogService.open(TaskFormComponent, {
      data: {
        contractId: this.contractId(),
        projectId: this.projectId(),
        isEditMode: true,
        customContractId: this.contractsQuery().data()?.customContractId,
        taskData: task,
      },
      header: 'Edit Task',
      dismissableMask: false,
      styleClass: 'overflow-hidden w-[90vw] md:w-[50vw]',
    });

    dialogRef.onClose.subscribe((result) => {
      if (result) {
        if (result.type === 'success') {
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: result.message,
          });
        } else if (result.type === 'error') {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: result.message,
          });
        }
      }
    });
  }

  /**
   * Marks a specified task as complete, triggers a mutation to complete the task identified by its ID and
   * displays a toast messages for success and error responses.
   */
  completeTask(task: TasksByContractId): void {
    const updateTaskStatusMutation = this.tasksService.updateTaskStatusQuery();

    updateTaskStatusMutation.mutate(
      { taskIds: [task.id], status: 'completed' },
      {
        onSuccess: () => {
          this.tasksService
            .tasksQuery(this.projectId(), this.contractId())
            .refetch();
          this.messageService.add({
            severity: 'success',
            summary: 'Complete Task',
            detail: `Successfully marked task "${task.taskName}" as complete.`,
          });
        },
        onError: () =>
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: `Failed to mark task "${task.taskName}" as complete.`,
          }),
      }
    );
  }

  /**
   * Marks a specified task as complete, triggers a mutation to complete the task identified by its ID and
   * displays a toast messages for success and error responses.
   */
  activateTask(task: TasksByContractId): void {
    const updateTaskStatusMutation = this.tasksService.updateTaskStatusQuery();

    updateTaskStatusMutation.mutate(
      { taskIds: [task.id], status: 'active' },
      {
        onSuccess: () => {
          this.tasksService
            .tasksQuery(this.projectId(), this.contractId())
            .refetch();
          this.messageService.add({
            severity: 'success',
            summary: 'Activate Task',
            detail: `Successfully activated task "${task.taskName}".`,
          });
        },
        onError: () => {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: `Failed to activate task "${task.taskName}".`,
          });
        },
      }
    );
  }

  /**
   * Marks a specified task as complete, triggers a mutation to complete the task identified by its ID and
   * displays a toast messages for success and error responses.
   */
  deactivateTask(task: TasksByContractId): void {
    const updateTaskStatusMutation = this.tasksService.updateTaskStatusQuery();

    updateTaskStatusMutation.mutate(
      { taskIds: [task.id], status: 'inActive' },
      {
        onSuccess: () => {
          this.tasksService
            .tasksQuery(this.projectId(), this.contractId())
            .refetch();
          this.messageService.add({
            severity: 'success',
            summary: 'Deactivate Task',
            detail: `Successfully deactivated task "${task.taskName}".`,
          });
        },
        onError: () => {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: `Failed to deactivate task "${task.taskName}".`,
          });
        },
      }
    );
  }
}
