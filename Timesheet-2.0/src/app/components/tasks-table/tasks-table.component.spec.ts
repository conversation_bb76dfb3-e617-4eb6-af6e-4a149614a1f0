import { Http<PERSON>lient, HttpHandler } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ActivatedRoute } from '@angular/router';
import { QueryClient } from '@tanstack/angular-query-experimental';
import { ConfirmationService, MessageService } from 'primeng/api';
import { of } from 'rxjs';
import { TasksByContractId } from '../../services/task/task.model'; // Adjust the import path as needed
import { TasksTableComponent } from './tasks-table.component';
import { DialogService } from 'primeng/dynamicdialog';

describe('TasksTableComponent', () => {
  let component: TasksTableComponent;
  let fixture: ComponentFixture<TasksTableComponent>;
  let mockConfirmationService: jasmine.SpyObj<ConfirmationService>;
  let mockMessageService: jasmine.SpyObj<MessageService>;

  beforeEach(async () => {
    mockConfirmationService = jasmine.createSpyObj('ConfirmationService', [
      'confirm',
    ]);
    mockMessageService = jasmine.createSpyObj('MessageService', ['add']);

    await TestBed.configureTestingModule({
      imports: [TasksTableComponent],
      providers: [
        { provide: ConfirmationService, useValue: mockConfirmationService },
        { provide: MessageService, useValue: mockMessageService },
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: { params: { projectId: '123' } }, // Mock params
            paramMap: of({ get: () => '123' }), // Mock observable
          },
        },
        HttpClient,
        HttpHandler,
        QueryClient,
        DialogService,
        provideHttpClientTesting,
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(TasksTableComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should return correct menu items for active task', () => {
    const task: TasksByContractId = {
      id: '1',
      taskName: 'Active Task',
      taskStatus: 'active',
      contract: {
        customContractId: 'C001',
        id: '',
      },
      contractResource: [],
      projectId: '',
      createdAt: '',
      updatedAt: '',
      frequency: 'hours',
      deleted: false,
      _count: {
        contractResources: 0,
      },
    };

    const menuItems = component.getMenuItems(task);
    expect(menuItems.length).toBe(4); // Adjust based on actual expected items
    expect(menuItems[0].label).toBe('Complete Task');
    expect(menuItems[1].label).toBe('Deactivate Task');
    expect(menuItems[2].label).toBe('Edit Task');
    expect(menuItems[3].label).toBe('Delete Task');
  });

  it('should return correct menu items for inactive task', () => {
    const task: TasksByContractId = {
      id: '2',
      taskName: 'Inactive Task',
      taskStatus: 'inActive',
      contract: {
        customContractId: 'C002',
        id: '',
      },
      contractResource: [],
      createdAt: '',
      updatedAt: '',
      frequency: 'hours',
      deleted: false,
      projectId: '',
      _count: {
        contractResources: 0,
      },
    };

    const menuItems = component.getMenuItems(task);
    expect(menuItems.length).toBe(3); // Adjust based on actual expected items
    expect(menuItems[0].label).toBe('Activate Task');
    expect(menuItems[1].label).toBe('Edit Task');
    expect(menuItems[2].label).toBe('Delete Task');
  });

  it('should return correct menu items for completed task', () => {
    const task: TasksByContractId = {
      id: '3',
      taskName: 'Completed Task',
      taskStatus: 'completed',
      contract: { id: '', customContractId: 'C003' },
      contractResource: [],
      projectId: '',
      createdAt: '',
      updatedAt: '',
      frequency: 'hours',
      deleted: false,
      _count: {
        contractResources: 0,
      },
    };

    const menuItems = component.getMenuItems(task);
    expect(menuItems.length).toBe(2); // Adjust based on actual expected items
    expect(menuItems[0].label).toBe('Edit Task');
    expect(menuItems[1].label).toBe('Delete Task');
  });

  it('should handle menu click and update menu items', () => {
    const task: TasksByContractId = {
      id: '4',
      taskName: 'Another Active Task',
      taskStatus: 'active',
      contract: {
        customContractId: 'C004',
        id: '',
      },
      contractResource: [],
      createdAt: '',
      updatedAt: '',
      frequency: 'hours',
      deleted: false,
      projectId: '',
      _count: {
        contractResources: 0,
      },
    };

    const menuMock = jasmine.createSpyObj('Menu', ['show']);

    // Simulate the menu click event
    component.onMenuClick(new MouseEvent('click'), task, menuMock);

    expect(menuMock.model.length).toBe(2); // Adjust based on actual expected items
    expect(menuMock.show).toHaveBeenCalled();
  });
});
