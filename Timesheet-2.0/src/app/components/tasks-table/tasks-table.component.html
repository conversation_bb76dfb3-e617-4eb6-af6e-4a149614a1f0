<div class="w-full bg-white overflow-hidden shadow rounded-xl">
  <p-table
    #dt
    [value]="tasks()"
    sortField="taskId"
    [sortOrder]="1"
    [loading]="isLoading()"
    [showLoader]="false"
    dataKey="id"
    [expandedRowKeys]="expandedRows()"
    [(selection)]="selectedTasks"
  >
    <ng-template pTemplate="header">
      <tr class="bg-transparent rounded-t-lg">
        <th style="width: 1rem" class="text-sm font-bold bg-primary-50"></th>
        <th style="width: 1rem" class="bg-primary-50">
          <p-tableHeaderCheckbox />
        </th>
        @for (col of taskColumnHeader; track $index) {
          <th
            [pSortableColumn]="col.field"
            class="text-sm font-bold bg-primary-50 text-nowrap"
            [ngClass]="col.class"
          >
            {{ col.header }}
            @if (col.sortable) {
              <p-sortIcon [field]="col.field"></p-sortIcon>
            }
          </th>
        }
      </tr>
    </ng-template>

    <ng-template pTemplate="body" let-task let-expanded="expanded">
      <tr>
        <td>
          <p-button
            type="button"
            pRipple
            [pRowToggler]="task"
            [text]="true"
            [rounded]="true"
            [plain]="true"
            [icon]="expanded ? 'pi pi-chevron-down' : 'pi pi-chevron-right'"
          />
        </td>
        <td>
          <p-tableCheckbox [value]="task" />
        </td>
        <td class="text-primary-500">
          <span>
            {{ task.taskName }}
          </span>
        </td>
        <td>{{ task.description }}</td>
        <td class="text-primary-500">
          @if (task.estimationTime) {
            <span>
              {{ task.estimationTime }}{{ ' ' }}{{ task.frequency }}
            </span>
          } @else {
            <span>N/A</span>
          }
        </td>
        <td class="text-center">{{ task.contractResource.length }}</td>
        <td>
          <p-chip
            [label]="task.taskStatus.toUpperCase()"
            [styleClass]="getStatusChipStyle(task.taskStatus.toUpperCase())"
          >
          </p-chip>
        </td>
        <td class="text-center">
          <div class="relative inline-block">
            <button
              pButton
              type="button"
              icon="pi pi-ellipsis-h"
              class="p-button-rounded p-button-text p-button-plain"
              [pTooltip]="'Actions'"
              tooltipPosition="left"
              style="width: 2.5rem; height: 2.5rem"
              (click)="onMenuClick($event, task, menu)"
              aria-label="menu"
            ></button>
            <p-menu
              #menu
              [model]="menuItems"
              [popup]="true"
              [styleClass]="'w-60 shadow-md border-0 text-sm'"
              appendTo="body"
            >
            </p-menu>
          </div>
        </td>
      </tr>
    </ng-template>

    <ng-template pTemplate="rowexpansion" let-task>
      <tr class="bg-gray-100">
        <td colspan="8">
          <div class="ml-8 flex flex-col gap-1">
            <div class="w-full flex items-center justify-between">
              <h3 class="font-semibold text-lg">Assignees</h3>
            </div>
            <div class="rounded-xl overflow-hidden">
              <p-table [value]="task.contractResource" dataKey="id">
                <ng-template pTemplate="header" class="bg-transparent">
                  <tr>
                    @for (col of taskAssigneeColumnHeader; track $index) {
                      <th
                        [pSortableColumn]="col.field"
                        class="text-sm font-bold"
                        [ngClass]="col.class"
                      >
                        {{ col.header }}
                        @if (col.sortable) {
                          <p-sortIcon [field]="col.field"></p-sortIcon>
                        }
                      </th>
                    }
                  </tr>
                </ng-template>
                <ng-template pTemplate="body" let-assignee>
                  <tr>
                    <td class="flex items-center gap-2">
                      @if (assignee.profilePicUrl) {
                        <p-avatar
                          [image]="assignee.profilePicUrl"
                          styleClass=""
                          shape="circle"
                        />
                      } @else {
                        <p-avatar
                          [label]="assignee.name.charAt(0)"
                          styleClass=""
                          shape="circle"
                        />
                      }
                      {{ assignee.name }}
                    </td>
                    <td>{{ assignee.email }}</td>
                    <td>{{ assignee.phoneNumber }}</td>
                    <td class="text-center">{{ assignee.expectedHours }}</td>
                  </tr>
                </ng-template>

                <ng-template pTemplate="loadingbody">
                  @for (_ of skeletonRows; track $index) {
                    <tr>
                      @for (col of taskColumnHeader; track $index) {
                        <td class="text-sm">
                          <p-skeleton width="100%" height="1.5rem"></p-skeleton>
                        </td>
                      }
                    </tr>
                  }
                </ng-template>

                <ng-template pTemplate="emptymessage">
                  <tr>
                    <td colspan="8" class="text-center p-6">
                      <p class="text-lg text-gray-500">
                        No assignees for this task.
                      </p>
                    </td>
                  </tr>
                </ng-template>
              </p-table>
            </div>
          </div>
        </td>
      </tr>
    </ng-template>

    <ng-template pTemplate="loadingbody">
      @for (_ of skeletonRows; track $index) {
        <tr>
          @for (col of taskColumnHeader; track $index) {
            <td class="text-sm">
              <p-skeleton width="100%" height="1.5rem"></p-skeleton>
            </td>
          }
        </tr>
      }
    </ng-template>

    <ng-template pTemplate="emptymessage">
      <tr>
        <td colspan="8" class="text-center p-6">
          <p class="text-lg text-gray-500">No tasks found.</p>
        </td>
      </tr>
    </ng-template>
  </p-table>
</div>

<p-confirmDialog [appendTo]="'body'"></p-confirmDialog>
<p-toast [preventOpenDuplicates]="true"></p-toast>
