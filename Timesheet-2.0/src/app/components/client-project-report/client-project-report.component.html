<div class="w-full p-4 rounded-lg bg-white shadow">
  <!-- Top row: Project info + Right-side controls -->
  <div class="flex justify-between items-start w-full">
    <!-- Left: Project Info -->
    <div class="flex flex-col">
      <span class="font-bold text-xl">{{ project().projectName }}</span>
      <div class="flex items-center gap-4 mt-1 text-gray-500 text-sm">
        <span>
          {{ project().numberOfContracts }}
          {{ project().numberOfContracts === 1 ? 'Contract' : 'Contracts' }}
        </span>
        <span>|</span>
        <span>Since {{ project().startDate | date: 'dd-MM-yyyy' }}</span>
      </div>
    </div>

    <!-- Right: Checkbox and Legend -->
    <div class="flex items-center gap-8 flex-wrap ml-4">
      @if (showCheckbox()) {
        <div class="flex items-center gap-2">
          <p-checkbox
            [binary]="true"
            [(ngModel)]="checked"
            inputId="kekaIdCheckbox"
          ></p-checkbox>
          <label for="kekaIdCheckbox" class="text-gray-700 ml-2">Keka Id</label>
        </div>
      }
      <div class="flex items-center flex-wrap gap-4">
        <div class="flex items-center gap-2">
          <div class="h-3 w-3 rounded-full bg-purple-200"></div>
          <span class="text-gray-500">Half day</span>
        </div>
        <div class="flex items-center gap-2">
          <div class="h-3 w-3 rounded-full bg-green-200"></div>
          <span class="text-gray-500">Present</span>
        </div>
        <div class="flex items-center gap-2">
          <div class="h-3 w-3 rounded-full bg-yellow-100"></div>
          <span class="text-gray-500">Weekend</span>
        </div>
        <div class="flex items-center gap-2">
          <div class="h-3 w-3 rounded-full bg-blue-200"></div>
          <span class="text-gray-500">Holiday</span>
        </div>
        <div class="flex items-center gap-2">
          <div class="h-3 w-3 rounded-full bg-red-200"></div>
          <span class="text-gray-500">Leave</span>
        </div>
        <div class="flex items-center gap-2">
          <div class="h-3 w-3 rounded-full bg-slate-100"></div>
          <span class="text-gray-500">Future Dates</span>
        </div>
      </div>
    </div>
  </div>

  <!-- Bottom row: Filters -->
  <div class="flex flex-col gap-4 mt-4">
    <div class="flex flex-wrap gap-3 items-center">
      <p-multiSelect
        [options]="contractOptions"
        [ngModel]="selectedContracts()"
        (ngModelChange)="onContractsChange($event)"
        optionLabel="label"
        placeholder="Select Contracts"
        class="w-38 rounded-lg"
        [showClear]="true"
        [resetFilterOnHide]="true"
        [disabled]="contractOptions.length === 0"
        multiple="true"
      ></p-multiSelect>
      <p-dropdown
        [options]="statusOptions"
        [ngModel]="selectedStatus()"
        (onChange)="onStatusChange($event.value)"
        optionLabel="label"
        optionValue="value"
        placeholder="Select Status"
        class="w-40 rounded-lg"
        [disabled]="!hasAnyContracts"
      ></p-dropdown>
    </div>
  </div>
</div>

<!-- Render contract worklogs for each selected contract -->
@for (contract of selectedContracts(); track contract.value.contractId) {
  <tms-contract-resources-worklogs
    [contract]="contract.value"
    [cappedHours]="cappedHours()"
    [kekaId]="checked"
    [currentDate]="currentDate()"
  ></tms-contract-resources-worklogs>
}
