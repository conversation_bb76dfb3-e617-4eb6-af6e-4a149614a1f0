import { Component, signal, input, ChangeDetectionStrategy, computed } from '@angular/core';
import {
  ClientReportProject,
  ClientReportContract,
} from '../../services/client/client.model';
import { DatePipe } from '@angular/common';
import { MultiSelectModule } from 'primeng/multiselect';
import { DropdownModule } from 'primeng/dropdown';
import { ButtonModule } from 'primeng/button';
import { ContractResourcesWorklogsComponent } from '../contract-resources-worklogs/contract-resources-worklogs.component';
import { FormsModule } from '@angular/forms';
import { CheckboxModule } from 'primeng/checkbox';

interface ContractOption {
  label: string;
  value: ClientReportContract;
}

@Component({
  selector: 'tms-client-project-report',
  imports: [
    DatePipe,
    MultiSelectModule,
    DropdownModule,
    ButtonModule,
    ContractResourcesWorklogsComponent,
    FormsModule,
    CheckboxModule,
  ],
  templateUrl: './client-project-report.component.html',
  styleUrls: ['./client-project-report.component.css'],
  standalone: true,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ClientProjectReportComponent {
  project = input.required<ClientReportProject>();
  showCheckbox = input<boolean>(true);
  cappedHours = input<boolean>(false);
  currentDate = input<Date>(new Date()); // input to receive date from parent

  checked: boolean = false;

  // State for selected contracts and status
  selectedContracts = signal<ContractOption[]>([]); // array of {label, value}
  selectedStatus = signal<string>('all');

  // Status options for dropdown
  statusOptions = [
    { label: 'All', value: 'all' },
    { label: 'Active', value: 'active' },
    { label: 'Completed', value: 'completed' },
    { label: 'Inactive', value: 'inactive' },
  ];

  // Check if there are any contracts at all (for disabling status dropdown)
  get hasAnyContracts() {
    return this.project().contracts.length > 0;
  }

  // MultiSelect options for contracts
  get contractOptions() {
    if (this.selectedStatus() === 'all') {
      return this.project().contracts.map((contract) => ({
        label: contract.contractId,
        value: contract,
      }));
    }
    return this.project().contracts
      .filter(contract => (contract.contractStatus || '').toLowerCase() === this.selectedStatus().toLowerCase())
      .map((contract) => ({
        label: contract.contractId,
        value: contract,
      }));
  }

  onContractsChange(contracts: ContractOption[]) {
    this.selectedContracts.set(contracts);
  }

  onStatusChange(status: string) {
    this.selectedStatus.set(status);
    this.selectedContracts.set([]); // Clear selected contracts when status changes
  }
}
