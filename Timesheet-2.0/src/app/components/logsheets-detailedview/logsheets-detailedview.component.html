<!-- Worklog Dialog Component: 
  -This component displays a modal dialog containing work log entries for a selected date.
  -It allows users to navigate between dates, view work log details, and perform actions such as approving, revising, or rejecting work logs.
  -The dialog includes loading indicators and error messages for better user experience.
-->
<p
  class="rounded-full w-7 h-7 grid place-items-center text-[0.6rem] font-semibold ml-1.5 my-2 z-50"
  [ngClass]="getStatusClass(worklogDayData()?.statuses)"
  [class.cursor-pointer]="worklogDayData()?.totalHours !== '-'"
  (click)="
    worklogDayData()?.totalHours !== '-' &&
      showDialog(worklogDayData()?.date, worklogDayData()?.resourceId ?? '')
  "
  (keydown.enter)="
    worklogDayData()?.totalHours !== '-' &&
      showDialog(worklogDayData()?.date, worklogDayData()?.resourceId ?? '')
  "
  ariaLabel="resource-worklog"
  tabindex="0"
>
  {{ formatHoursToTimeString(+(worklogDayData()?.totalHours ?? 0)) }}
</p>
<p-dialog
  header="Worklog"
  [modal]="true"
  (onHide)="onDialogHide()"
  [(visible)]="visible"
  draggable="false"
  [style]="{ width: '50rem', height: '80vh' }"
  [contentStyle]="{ overflowY: 'hidden' }"
  ><div class="space-y-4">
    <div class="flex items-center justify-between">
      <p-button
        icon="pi pi-angle-left text-lg"
        (onClick)="navigateDay('prev')"
      ></p-button>
      <div class="flex items-center space-x-2">
        <i class="pi pi-calendar"></i>
        <h1 class="text-lg font-semibold">
          {{ currentDate() | date: 'fullDate' }}
        </h1>
      </div>
      <p-button
        icon="pi pi-angle-right"
        (onClick)="navigateDay('next')"
      ></p-button>
    </div>
    <div class="overflow-y-auto max-h-[60vh] space-y-4">
      @for (worklog of worklogQuery().data(); track $index) {
        <div class="bg-white rounded-lg border border-neutral-100 max-h-[60vh]">
          <div class="p-4">
            <div class="flex justify-between items-start gap-4">
              <div class="flex-1">
                <div class="flex items-center gap-2 mb-1">
                  <h3 class="font-semibold text-left">
                    {{
                      stringUtilsService.capitalizeFirstLetter(
                        worklog.employeeResource?.name
                      )
                    }}
                  </h3>
                  <p-tag
                    [value]="
                      stringUtilsService.capitalizeFirstLetter(
                        worklog.workLogStatus?.status
                      )
                    "
                    rounded="true"
                    [styleClass]="
                      getWorklogStatusTagStyle(worklog.workLogStatus?.status)
                    "
                  ></p-tag>
                  @if (worklog.location) {
                    <p-tag
                      [value]="getWorklogLocationLabel(worklog.location)"
                      rounded="true"
                      styleClass="bg-blue-100 text-blue-700"
                    ></p-tag>
                  }
                  @if (worklog.workLogStatus?.status === 'rejected') {
                    <i
                      class="pi pi-exclamation-circle text-red-800"
                      [pTooltip]="worklog.workLogStatus?.remarks"
                      [tooltipStyleClass]="
                        'text-xs text-gray-700  whitespace-pre-wrap'
                      "
                      tooltipPosition="top"
                    ></i>
                  }
                </div>
                <div class="text-sm mb-1 text-start">
                  <span class="font-medium">{{
                    worklog.project?.projectName
                  }}</span>
                  •
                  <span>{{ worklog.task?.taskName }}</span>
                </div>
                <p
                  class="text-sm text-neutral-500 break-words line-clamp-1 text-start"
                >
                  <span
                    [pTooltip]="worklog.description"
                    [tooltipStyleClass]="
                      'text-xs text-gray-700  whitespace-pre-wrap'
                    "
                    tooltipPosition="top"
                    >{{ worklog.description }}</span
                  >
                </p>
              </div>
              <div class="flex flex-col items-end gap-2">
                <p-tag
                  [value]="worklog.minutes | minutesToHours"
                  icon="pi pi-clock"
                  styleClass="text-sm"
                ></p-tag>

                <div class="flex gap-2">
                  <p-button
                    label="Approve"
                    size="small"
                    icon="pi pi-check"
                    styleClass="hover:text-white border-none bg-green-100 text-green-600 hover:bg-green-500 "
                    [disabled]="worklog.workLogStatus?.status === 'approved'"
                    (onClick)="approveWorklog(worklog.id)"
                  ></p-button>
                  <p-button
                    label="Reject"
                    size="small"
                    icon="pi pi-times"
                    styleClass="border-none bg-red-100 text-red-600 hover:bg-red-500 hover:text-white"
                    [disabled]="worklog.workLogStatus?.status === 'rejected'"
                    (onClick)="showRejectDialog(worklog.id)"
                  ></p-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      }
    </div>
    @if (worklogQuery().isLoading()) {
      <div class="overflow-y-auto max-h-[60vh] space-y-4">
        @for (_ of skeletonRows; track $index) {
          <div
            class="bg-white rounded-lg shadow-md border border-neutral-100 max-h-[60vh]"
          >
            <div class="p-4">
              <div class="flex justify-between items-start gap-4">
                <div class="flex-1">
                  <div class="flex items-center gap-2 mb-1">
                    <p-skeleton shape="text" width="150px"></p-skeleton>
                    <p-skeleton
                      shape="rectangle"
                      width="80px"
                      height="20px"
                    ></p-skeleton>
                  </div>
                  <div class="text-sm mb-1">
                    <p-skeleton shape="text" width="100px"></p-skeleton>
                  </div>
                  <p-skeleton
                    shape="text"
                    width="100%"
                    height="20px"
                  ></p-skeleton>
                </div>
                <div class="flex flex-col items-end gap-2">
                  <p-skeleton
                    shape="rectangle"
                    width="50px"
                    height="20px"
                  ></p-skeleton>
                  <div class="flex gap-2">
                    <p-skeleton
                      shape="rectangle"
                      width="50px"
                      height="30px"
                    ></p-skeleton>
                    <p-skeleton
                      shape="rectangle"
                      width="50px"
                      height="30px"
                    ></p-skeleton>
                    <p-skeleton
                      shape="rectangle"
                      width="50px"
                      height="30px"
                    ></p-skeleton>
                  </div>
                </div>
              </div>
            </div>
          </div>
        }
      </div>
    }
    @if (worklogQuery().isError()) {
      <small
        class="flex items-center bg-red-100 border border-red-300 text-red-800 rounded-md px-2 py-1 mb-1 text-sm"
        ><i class="pi pi-exclamation-triangle mr-2"></i>Failed to load the
        worklog</small
      >
    }
    @if (worklogQuery().data()?.length === 0) {
      <small
        class="flex items-center bg-red-100 border border-red-300 text-red-800 rounded-md px-2 py-1 mb-1 text-sm"
        ><i class="pi pi-exclamation-triangle mr-2"></i>No worklogs available
        for this date</small
      >
    }
  </div>
</p-dialog>

<!--Reject Dialog:
  -This dialog is displayed while rejecting the worklog and also allowing to add a remark on why the worklog is being rejected
-->
<p-dialog
  #rejectDialog
  header="Reject Worklog"
  [modal]="true"
  [(visible)]="isRejectDialogOpen"
  ><form [formGroup]="rejectWorklogForm" (ngSubmit)="rejectWorklog()">
    <div class="space-y-3">
      <div><p>Please provide a reason for rejecting this worklog</p></div>

      <p-dropdown
        [options]="rejectionReasons"
        optionLabel="label"
        placeholder="Select reason for rejection"
        formControlName="rejectionReason"
        styleClass="w-full flex ring-0 mt-2"
        [style]="{ 'text-align': 'left' }"
      />

      <textarea
        pInputTextarea
        rows="3"
        class="flex-grow p-2 border rounded flex-1 w-full outline-none ring-0 focus:ring-1 focus:border-primary-500"
        placeholder="Add your remark..."
        formControlName="remark"
      ></textarea>
    </div>
    <div class="flex justify-end mt-3">
      <p-button
        label="Reject"
        size="small"
        type="submit"
        styleClass="border-none bg-red-500 text-white hover:bg-red-600"
        [disabled]="
          rejectWorklogForm.invalid ||
          (rejectWorklogForm.get('rejectionReason')?.value?.value === 'Other' &&
            !rejectWorklogForm.get('remark')?.value.trim())
        "
        (onClick)="rejectWorklog()"
      ></p-button>
    </div>
  </form>
</p-dialog>

<p-toast></p-toast>
