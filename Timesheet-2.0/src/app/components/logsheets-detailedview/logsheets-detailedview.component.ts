import { CommonModule, DatePipe } from '@angular/common';
import {
  Component,
  computed,
  input,
  signal,
  ViewEncapsulation,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { injectMutation } from '@tanstack/angular-query-experimental';
import { format } from 'date-fns';
import { MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { DialogModule } from 'primeng/dialog';
import { DropdownModule } from 'primeng/dropdown';
import { SkeletonModule } from 'primeng/skeleton';
import { TagModule } from 'primeng/tag';
import { ToastModule } from 'primeng/toast';
import { TooltipModule } from 'primeng/tooltip';
import { MinutesToHoursPipe } from '../../pipes/time-formatter/minutes-to-hours.pipe';
import { AuthService } from '../../services/auth/auth-service';
import { StringUtilsService } from '../../services/stringutils/stringutils.service';
import {
  ApproveWorklogPayload,
  RejectWorklogPayload,
} from '../../services/worklog/worklog.model';
import { WorkLogService } from '../../services/worklog/worklog.service';
import { DATE_FORMAT_YYYY_MM_DD } from '../../settings';
import { formatHoursToTimeString } from '../../utils/date.utils';
import { WorklogDayData, WorklogStatus } from '../../pages/logsheets/logsheets.component';
import { WORKLOG_LOCATION } from '../../constants/constant';

interface IRejectionReasons {
  label: string;
  value: string;
}

/**
 * LogsheetsDetailedViewComponent
 *
 * This component provides a detailed view of work logs for a specific resource.
 * It allows users to approve or reject work logs, navigate through dates,
 * and manage work log statuses. The component utilizes Angular's reactive forms
 * for handling user input and integrates with various PrimeNG components for UI.
 */
@Component({
  selector: 'tms-logsheets-detailedview',
  standalone: true,
  imports: [
    ButtonModule,
    DialogModule,
    DatePipe,
    TagModule,
    FormsModule,
    ReactiveFormsModule,
    CommonModule,
    TooltipModule,
    DropdownModule,
    CardModule,
    ToastModule,
    MinutesToHoursPipe,
    SkeletonModule,
  ],
  providers: [MessageService],
  templateUrl: './logsheets-detailedview.component.html',
  styleUrl: './logsheets-detailedview.component.css',
  encapsulation: ViewEncapsulation.None,
})
export class LogsheetsDetailedViewComponent {
  visible: boolean = false;
  worklogDayData = input<WorklogDayData>();
  resourceId = signal<string>('');
  currentDate = signal<Date>(new Date());
  skeletonRows = Array(3);
  isRejectDialogOpen: boolean = false;
  selectedWorklogId: string | null = '';
  selectedProjectIds = input<string[]>([]);
  dateRange = input<{ startDate: Date; endDate: Date; daysInMonth: number }>();
  rejectWorklogForm: FormGroup;
  formatHoursToTimeString = formatHoursToTimeString;

  rejectionReasons: IRejectionReasons[] = [
    {
      label: 'Incorrect Hours Logged',
      value: 'Incorrect Hours Logged',
    },
    {
      label: 'Incorrect Date and Time Entered',
      value: 'Incorrect Date and Time Entered',
    },
    {
      label: 'Incorrect Project/Task Name Entered',
      value: 'Incorrect Project/Task Name Entered',
    },
    {
      label: 'Other',
      value: 'Other',
    },
  ];

  constructor(
    private worklogService: WorkLogService,
    private messageService: MessageService,
    private authService: AuthService,
    private fb: FormBuilder,
    public stringUtilsService: StringUtilsService
  ) {
    this.rejectWorklogForm = this.fb.group({
      rejectionReason: ['', Validators.required],
      remark: [{ value: '', disabled: true }],
    });

    this.rejectWorklogForm
      .get('rejectionReason')
      ?.valueChanges.subscribe((reason) => {
        const remarkControl = this.rejectWorklogForm.get('remark');
        if (reason?.value === 'Other') {
          remarkControl?.setValidators([Validators.required]);
          remarkControl?.enable();
        } else {
          remarkControl?.clearValidators();
          remarkControl?.disable();
        }
        remarkControl?.updateValueAndValidity();
      });
  }

  // Computed property to fetch work log query based on current date and resource ID
  worklogQuery = computed(() =>
    this.worklogService.getWorklogQuery(
      this.dateToISO(this.currentDate()),
      this.resourceId()
    )
  );

  /**
   * Shows the dialog for viewing or editing work log details.
   * @param worklogDate - The date of the work log.
   * @param resourceId - The ID of the resource associated with the work log.
   */
  showDialog(worklogDate: Date | undefined, resourceId: string) {
    this.visible = true;

    if (worklogDate) {
      const utcDate = new Date(worklogDate);
      this.currentDate.set(new Date(utcDate.getUTCFullYear(), utcDate.getUTCMonth(), utcDate.getUTCDate()));
    }
    this.resourceId.set(resourceId);
  }

  /**
   * Navigate to the previous or next day based on the specified direction.
   */
  navigateDay(direction: 'prev' | 'next'): void {
    this.currentDate.set(
      new Date(
        this.currentDate().setDate(
          this.currentDate().getDate() + (direction === 'prev' ? -1 : 1)
        )
      )
    );
  }

  /**
   * Converts a Date object to a string formatted as 'YYYY-MM-DD'.
   */
  dateToISO(date: Date): string {
    return format(date, DATE_FORMAT_YYYY_MM_DD);
  }

  /**
   * Get CSS class styles based on the work log status.
   */
  getWorklogStatusTagStyle(worklogStatus: WorklogStatus | undefined): string {
    const baseClassStyle = 'text-xs hover:text-white';
    switch (worklogStatus) {
      case 'submitted':
        return `${baseClassStyle}`;
      case 'approved':
        return `${baseClassStyle} bg-green-100 text-green-600 hover:bg-green-500`;
      case 'revised':
        return `${baseClassStyle} bg-purple-100 text-purple-600 hover:bg-purple-500`;
      case 'rejected':
        return `${baseClassStyle} bg-red-100 text-red-600 hover:bg-red-500`;
      default:
        return '';
    }
  }

  /**
  * Retrieves the label corresponding to a given worklog location value.
  */
  getWorklogLocationLabel(value: string): string {
    return WORKLOG_LOCATION.find(option => option.value === value)?.label ?? value;
  }
  
  /**
  * Maps each worklog status to its corresponding CSS class.
  */
  private statusColorMap: Record<WorklogStatus | 'no worklogs', string> = {
    approved: 'bg-green-200',
    leave: 'bg-pink-200',
    halfDayLeave: 'bg-amber-200',
    holiday: 'bg-orange-200',
    submitted: 'bg-primary-200',
    revised: 'bg-purple-200',
    rejected: 'bg-red-200',
    weekoff: '',
    'no worklogs': ''
  };

  /**
   * Returns the highest priority status from the given list.
   */
  private getPriorityStatus(statuses: WorklogStatus[] = []): WorklogStatus | 'no worklogs' {
    const priorityOrder: WorklogStatus[] = [
      'rejected', 'revised', 'submitted', 'leave', 'holiday', 'approved', 'halfDayLeave', 'weekoff'
    ];
    return priorityOrder.find(status => statuses.includes(status)) || 'no worklogs';
  }

  /**
   * Returns an object containing the CSS class for the highest priority worklog status.
   */
  getStatusClass(statuses: WorklogStatus[] = []): Record<string, boolean> {
    return { [this.statusColorMap[this.getPriorityStatus(statuses)]]: true };
  }

  /**
   * Query to approve the worklog
   */
  approveWorklogQuery = injectMutation(() => {
    return {
      mutationKey: ['approveWorklog', this.authService.userId()],
      mutationFn: (worklogId: ApproveWorklogPayload) =>
        this.worklogService.approveWorklog(worklogId),
      onSuccess: () => {
        this.worklogQuery().refetch();
      },
      onError: (error: any) => {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: error.message,
        });
      },
    };
  });

  /**
   * Query to reject the worklog
   */
  rejectWorklogQuery = injectMutation(() => {
    return {
      mutationKey: ['rejectWorklog', this.authService.userId()],
      mutationFn: (worklogPayload: RejectWorklogPayload) =>
        this.worklogService.rejectWorklog(worklogPayload),
      onSuccess: () => {
        this.worklogQuery().refetch();
        this.isRejectDialogOpen = false;
      },
      onError: (error: any) => {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: error.message,
        });
      },
    };
  });

  /**
   * Approves a specific work log by ID.
   * @param id - The ID of the work log to approve.
   */
  approveWorklog(worklogId: string) {
    const worklogPayload: ApproveWorklogPayload = {
      workLogIdList: [worklogId],
    };
    this.approveWorklogQuery.mutate(worklogPayload);
  }

  /**
   * Shows the dialog for rejecting a specific work log.
   * @param worklogId - The ID of the work log to reject.
   */
  showRejectDialog(worklogId: string) {
    this.isRejectDialogOpen = true;
    this.selectedWorklogId = worklogId;
    this.rejectWorklogForm.reset();
  }

  /**
   * Rejects a selected work log with a remark.
   */
  rejectWorklog() {
    if (!this.selectedWorklogId) {
      return;
    }
    const reason: IRejectionReasons =
      this.rejectWorklogForm.value.rejectionReason;
    const remark: string = this.rejectWorklogForm.value.remark;
    const rejectWorklogPayload: RejectWorklogPayload = {
      workLogIdList: [this.selectedWorklogId],
      remark: reason?.value !== 'Other' ? reason?.value : remark.trim(),
    };
    this.rejectWorklogQuery.mutate(rejectWorklogPayload);
    this.selectedWorklogId = null;
    this.rejectWorklogForm.reset();
  }

  /**
   * Handles actions when the dialog is hidden, such as refreshing data.
   */
  onDialogHide() {
    this.worklogService.getWorkLogsByManagerIdQuery(
      this.authService.userId(),
      this.dateRange()?.startDate.toISOString() ?? '',
      this.dateRange()?.endDate.toISOString() ?? '',
      this.selectedProjectIds()
    );
  }
}
