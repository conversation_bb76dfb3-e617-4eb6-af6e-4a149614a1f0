import { ComponentFixture, TestBed } from '@angular/core/testing';

import { LogsheetsDetailedViewComponent } from './logsheets-detailedview.component';
import { HttpClient, HttpHandler } from '@angular/common/http';
import { QueryClient } from '@tanstack/angular-query-experimental';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';

/**
 * Unit tests for the LogsheetsDetailedViewComponent.
 * These tests verify the component's behavior and rendering based on different states.
 */
describe('LogsheetsDetailedviewComponent', () => {
  let component: LogsheetsDetailedViewComponent;
  let fixture: ComponentFixture<LogsheetsDetailedViewComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [LogsheetsDetailedViewComponent, BrowserAnimationsModule],
      providers: [HttpClient, QueryClient, HttpHandler],
    }).compileComponents();

    fixture = TestBed.createComponent(LogsheetsDetailedViewComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  /**
   * Test case to verify whether the component is created
   */
  it('should create', () => {
    expect(component).toBeTruthy();
  });

  /**
   * Test case to navigate to the previous day
   */
  it('should navigate to the previous day', () => {
    const initialDate = new Date(component.currentDate());

    component.navigateDay('prev');
    fixture.detectChanges();

    const expectedDate = new Date(initialDate);
    expectedDate.setDate(initialDate.getDate() - 1);

    expect(component.currentDate().toDateString()).toEqual(
      expectedDate.toDateString()
    );
  });

  /**
   * Test case to navigate to the next day
   */
  it('should navigate to the next day', () => {
    const initialDate = new Date(component.currentDate());

    component.navigateDay('next');
    fixture.detectChanges();

    const expectedDate = new Date(initialDate);
    expectedDate.setDate(initialDate.getDate() + 1);

    expect(component.currentDate().toDateString()).toEqual(
      expectedDate.toDateString()
    );
  });

  /**
   * Test case to convert date to UTC format
   */
  it('should convert date to UTC format', () => {
    const testDate = new Date('2024-01-15T10:00:00'); // Sample date
    const convertedDate = component.dateToISO(testDate);

    expect(convertedDate).toBe('2024-01-15'); // UTC format
  });
});
