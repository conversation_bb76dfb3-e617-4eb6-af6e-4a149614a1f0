import {
  ChangeDetectorRef,
  Component,
  computed,
  ElementRef,
  input,
  QueryList,
  signal,
  ViewChild,
  ViewChildren,
} from '@angular/core';
import { TagComponent } from '../tag/tag.component';
import { AvatarModule } from 'primeng/avatar';
import { ButtonModule } from 'primeng/button';
import { AccordionModule } from 'primeng/accordion';
import { ContractService } from '../../services/contract/contract.service';
import {
  injectMutation,
  injectQuery,
} from '@tanstack/angular-query-experimental';
import { CommonModule } from '@angular/common';
import { StringUtilsService } from '../../services/stringutils/stringutils.service';
import { InputTextareaModule } from 'primeng/inputtextarea';
import {
  FormBuilder,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { SkeletonModule } from 'primeng/skeleton';
import { ConfirmPopupModule } from 'primeng/confirmpopup';
import { ToastModule } from 'primeng/toast';
import { ConfirmationService, Message, MessageService } from 'primeng/api';
import { COMMENTS_MIN_VISIBLE } from '../../constants/constant';
import { TimelineHistoryViewComponent } from '../timeline-history-view/timeline-history-view.component';
import { ContractHistoryResponse } from '../../services/contract/contract.model';
import { DialogService } from 'primeng/dynamicdialog';
import { AddResourceFormComponent } from '../add-resource-form/add-resource-form.component';
import { InputTextModule } from 'primeng/inputtext';

/**
 * ContractInfoCardComponent is responsible for displaying contract details
 * and managing comments associated with a contract.
 */
@Component({
  selector: 'tms-contract-info-card',
  standalone: true,
  imports: [
    SkeletonModule,
    TagComponent,
    AvatarModule,
    ReactiveFormsModule,
    ButtonModule,
    AccordionModule,
    FormsModule,
    InputTextareaModule,
    InputTextModule,
    CommonModule,
    ToastModule,
    ConfirmPopupModule,
    TimelineHistoryViewComponent,
  ],
  providers: [MessageService, ConfirmationService],
  templateUrl: './contract-info-card.component.html',
  styleUrl: './contract-info-card.component.css',
})
export class ContractInfoCardComponent {
  @ViewChildren('editTextArea') textAreas!: QueryList<ElementRef>;
  private pendingFocusCommentId: string | null = null;

  contractId = input<string>('');
  commentsForm: FormGroup;
  editCommentsForm = new Map<string, FormGroup>();
  visibleCommentsCount = signal<number>(COMMENTS_MIN_VISIBLE);
  resourceSkeletonRows = Array(6);
  commentsSkeletonRows = Array(3);
  visibleContractComments = computed(() =>
    this.contractsQuery()
      .data()
      ?.contractComments.slice(0, this.visibleCommentsCount())
  );

  errorMessage: Message[] = [
    { severity: 'error', detail: 'Failed to fetch contract history' },
  ];
  isContractHistoryError = computed(() =>
    this.contractHistoryQuery().isError()
  );
  isContractHistoryLoading = computed(() =>
    this.contractHistoryQuery().isLoading()
  );
  activeEditingCommentIds = signal<string[]>([]);

  constructor(
    private contractService: ContractService,
    public stringUtilsService: StringUtilsService,
    private fb: FormBuilder,
    private messageService: MessageService,
    private dialogService: DialogService,
    private confirmationService: ConfirmationService,
    private cdr: ChangeDetectorRef
  ) {
    this.commentsForm = this.fb.group({
      comments: ['', Validators.required],
    });
  }

  ngAfterViewChecked() {
    if (this.pendingFocusCommentId) {
      const textareas = this.textAreas.toArray();
      const targetTextarea = textareas.find(
        (textarea) =>
          textarea.nativeElement.id ===
          `editTextArea-${this.pendingFocusCommentId}`
      );

      if (targetTextarea) {
        targetTextarea.nativeElement.focus();
        this.pendingFocusCommentId = null;
        this.cdr.detectChanges();
      }
    }
  }

  /**
   * Query to fetch contract history data based on the project ID.
   *
   * This query uses Angular Query to asynchronously fetch contract history
   * records and store them in the contractHistoryEvents signal.
   */
  contractHistoryQuery = computed(() =>
    this.contractService.contractsHistoryQuery(this.contractId())
  );

  /**
   * Query to fetch contract details based on the contract ID.
   */
  contractsQuery = computed(() =>
    this.contractService.contractsQuery(this.contractId())
  );

  /**
   * Mutation to add a comment to the contract.
   */
  updateCommentsQuery = injectMutation(() => {
    return {
      mutationKey: ['comments', this.contractId()],
      mutationFn: (comments: string[]) =>
        this.contractService.addCommentsToContract(this.contractId(), comments),
      onSuccess: () => this.contractsQuery().refetch(),
      onError: (error: any) => {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: error.message,
          life: 3000,
        });
      },
    };
  });

  /**
   * Handles the submission of the comment form.
   * If a comment is provided, it triggers the mutation to add the comment.
   */
  onSubmit() {
    const comment = this.commentsForm.value.comments;
    if (comment) {
      this.updateCommentsQuery.mutate([comment.trim()]);
      this.commentsForm.reset();
    }
  }

  /**
   * Increases the count of visible comments by four.
   */
  loadMoreComments() {
    this.visibleCommentsCount.update((value) => value + 4);
  }

  /**
   * Computed Property: projectManagerDetails
   *
   * This computed property retrieves and formats contract resource details for project managers.
   */
  projectManagerDetails = computed(() => {
    const resources =
      this.contractsQuery().data()?.project?.contractResource || [];
    return resources.map((resource) => ({
      name: resource.contractResource.name,
      phoneNumber: resource.contractResource.phoneNumber,
      email: resource.contractResource.email,
    }));
  });

  openAddResourceForm() {
    const dialogRef = this.dialogService.open(AddResourceFormComponent, {
      header: 'Add Resource',
      dismissableMask: false,
      styleClass: 'overflow-hidden w-[60vw] md:w-[50vw]',
      data: {
        contract: this.contractsQuery().data(),
      },
    });
    dialogRef.onClose.subscribe((result) => {
      if (result) {
        if (result.type === 'success') {
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: result.message,
          });
        } else if (result.type === 'error') {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: result.message,
          });
        }
      }
    });
  }

  /**
   * If the user confirms, it triggers the mutation to delete the comment.
   */
  confirmDeleteComment(event: Event, commentId: string) {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: 'Are you sure ?',
      acceptButtonStyleClass: 'p-button-danger p-button-sm',
      rejectButtonStyleClass: 'p-button-primary',
      accept: () => {
        const deleteCommentMutation =
          this.contractService.deleteCommentsQuery();

        deleteCommentMutation.mutate(commentId, {
          onSuccess: () => {
            this.contractsQuery().refetch();
          },
          onError: (error: any) =>
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail: error.message,
            }),
        });
      },
    });
  }

  /**
   * @method editCommentsQuery
   * @description Mutation to edit an existing comment.
   * @returns {object} - An object containing the mutation configuration.
   */
  editCommentsQuery = injectMutation(() => ({
    mutationKey: ['editComment', this.contractId()],
    mutationFn: (comments: { commentId: string; comment: string }) =>
      this.contractService.editComment(comments.commentId, comments.comment),
    onSuccess: () => this.contractsQuery().refetch(),
  }));

  /**
   * Checks if a comment is currently being edited.
   */
  isCommentEditing(commentId: string): boolean {
    return this.activeEditingCommentIds().includes(commentId);
  }

  /**
   * @method editComment
   * @param {string} commentId - The ID of the comment to be edited.
   * @param {string} comment - The current content of the comment.
   * @description Enables the editing mode for a specific comment.
   * It initializes a new FormGroup for the comment and populates it with the current comment content.
   */
  editComment(commentId: string, comment: string) {
    this.activeEditingCommentIds().push(commentId);
    const newForm = this.fb.group({
      comment: ['', Validators.required],
    });
    this.editCommentsForm.set(commentId, newForm);
    this.getCommentsFormForCurrentCommentId(commentId).patchValue({
      comment: comment,
    });

    this.pendingFocusCommentId = commentId;
  }

  /**
   * Retrieves the FormGroup for a specific comment ID.
   */
  getCommentsFormForCurrentCommentId(commentId: string): FormGroup {
    return this.editCommentsForm.get(commentId) as FormGroup;
  }

  /**
   * It removes the comment ID from the editingComments signal.
   */
  cancelEditingComment(commentId: string) {
    this.activeEditingCommentIds.update((comments) =>
      comments.filter((id) => id !== commentId)
    );
  }

  /**
   * @method submitEditedComment
   * @param {string} commentId - The ID of the comment to submit.
   * @description Submits the edited comment content to the server.
   * It retrieves the FormGroup for the comment, extracts the comment content, and triggers the editCommentsQuery mutation.
   */
  submitEditedComment(commentId: string) {
    const form = this.getCommentsFormForCurrentCommentId(commentId);
    if (!form) return;
    const comment: string = form.value.comment;
    const comments = {
      commentId: commentId,
      comment: comment.trim(),
    };
    if (comment) {
      this.editCommentsQuery.mutate(comments, {
        onError: (error: any) => {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: error.message,
          });
          this.cancelEditingComment(commentId);
        },
      });
      this.cancelEditingComment(commentId);
    }
  }
}
