import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ContractInfoCardComponent } from './contract-info-card.component';
import { QueryClient } from '@tanstack/angular-query-experimental';
import { ComponentRef, computed, signal, WritableSignal } from '@angular/core';
import { HttpClient, HttpHandler } from '@angular/common/http';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { ContractService } from '../../services/contract/contract.service';
import { DialogService } from 'primeng/dynamicdialog';

/**
 * Unit tests for the ContractInfoCardComponent.
 * These tests verify the component's behavior and rendering based on different states.
 */
describe('ContractInfoCardComponent', () => {
  let component: ContractInfoCardComponent;
  let fixture: ComponentFixture<ContractInfoCardComponent>;
  let componentRef: ComponentRef<ContractInfoCardComponent>;
  let mockMyContractService: jasmine.SpyObj<ContractService>;
  let refetchSpy: jasmine.Spy;

  beforeEach(async () => {
    refetchSpy = jasmine.createSpy('refetch');
    mockMyContractService = jasmine.createSpyObj(
      'MycontractsService',
      ['getUserRole'],
      {
        contractsQuery: () => ({
          refetch: refetchSpy,
          isLoading: signal(false),
          isError: signal(true),
          data: computed(() => ({
            customContractId: 'C001',
            contractStatus: 'active',
            startDate: '2024-01-01',
            endDate: '2024-12-31',
            contractComments: [],
            contractManagers: [],
            contractResource: [],
          })),
        }),
      }
    );

    await TestBed.configureTestingModule({
      imports: [ContractInfoCardComponent, BrowserAnimationsModule],
      providers: [
        { provide: ContractService, useValue: mockMyContractService },
        QueryClient,
        HttpClient,
        HttpHandler,
        DialogService,
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ContractInfoCardComponent);
    component = fixture.componentInstance;
    componentRef = fixture.componentRef;

    // Mock updateCommentsQuery
    component.updateCommentsQuery = {
      mutate: jasmine.createSpy('mutate'),
    } as any;

    fixture.detectChanges();
  });

  /**
   * Test case to verify whether the component is created
   */
  it('should create', () => {
    expect(component).toBeTruthy();
  });

  /**
   * Test case to display skeleton loaders when contractsQuery is loading
   */
  it('should display skeleton loaders when contractsQuery is loading', () => {
    (component.contractsQuery().isLoading as WritableSignal<boolean>).set(true);
    fixture.detectChanges();

    const skeletonElements =
      fixture.nativeElement.querySelectorAll('p-skeleton');
    expect(skeletonElements.length).toBeGreaterThan(0);
  });

  /**
   * Test case to display an error message when contractsQuery has an error
   */
  it('should display an error message when contractsQuery has an error', () => {
    fixture.detectChanges();

    const errorMessage = fixture.nativeElement.querySelector(
      '.bg-red-100.border.border-red-400.text-red-700'
    );
    expect(errorMessage).toBeTruthy();
    expect(errorMessage.textContent).toContain(
      'Unable to load contract information.'
    );
  });

  /**
   * Test case to call updateCommentsQuery when a comment is added
   */
  it('should call updateCommentsQuery when a comment is added', () => {
    component.commentsForm.setValue({ comments: 'New comment' });

    component.onSubmit();
    expect(component.updateCommentsQuery.mutate).toHaveBeenCalledWith([
      'New comment',
    ]);
  });

  /**
   * Test case to increase visible comments count when "Show More" is clicked
   */
  it('should increase visible comments count when "Show More" is clicked', () => {
    component.visibleCommentsCount.set(2);
    component.loadMoreComments();
    expect(component.visibleCommentsCount()).toBe(6);
  });

  /**
   * Test case to display resource skeleton rows
   */
  it('should display resource skeleton rows', () => {
    component.resourceSkeletonRows = Array(6);
    (component.contractsQuery().isLoading as WritableSignal<boolean>).set(true);
    fixture.detectChanges();

    const skeletonRows =
      fixture.nativeElement.querySelectorAll('.grid .p-skeleton');
    expect(skeletonRows.length).toBe(6);
  });
});
