@if (contractsQuery().isLoading()) {
  <div class="flex justify-between mb-8">
    <div class="flex items-end gap-4">
      <p-skeleton width="200px" height="48px"></p-skeleton>
      <p-skeleton width="80px" height="24px"></p-skeleton>
    </div>
    <p-skeleton width="100px" height="36px"></p-skeleton>
  </div>
  <div
    class="contract-container shadow p-6 rounded-xl bg-white overflow-hidden overflow-y-auto h-[calc(100vh-7rem)]"
  >
    <div
      class="flex md:items-start justify-between md:flex-row flex-col gap-2 mb-4"
    >
      <div class="flex flex-col gap-4">
        <div class="flex gap-10 items-center flex-wrap">
          <p-skeleton width="200px" height="24px"></p-skeleton>
          <p-skeleton width="200px" height="24px"></p-skeleton>
          <p-skeleton width="150px" height="24px"></p-skeleton>
          <p-skeleton width="200px" height="24px"></p-skeleton>
        </div>
      </div>
    </div>
    <p-skeleton width="100%" height="1px" styleClass="mb-4"></p-skeleton>
    <div class="mb-6">
      <p-skeleton width="200px" height="32px" styleClass="mb-2"></p-skeleton>
      <p-skeleton width="100%" height="100px"></p-skeleton>
    </div>
    <div class="mb-6">
      <p-skeleton width="150px" height="32px" styleClass="mb-2"></p-skeleton>
      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
        @for (_ of resourceSkeletonRows; track $index) {
          <p-skeleton width="100%" height="80px"></p-skeleton>
        }
      </div>
    </div>
    <div>
      <p-skeleton width="150px" height="32px" styleClass="mb-2"></p-skeleton>
      <p-skeleton width="100%" height="100px" styleClass="mb-2"></p-skeleton>
      @for (_ of commentsSkeletonRows; track $index) {
        <p-skeleton width="100%" height="60px" styleClass="mb-2"></p-skeleton>
      }
    </div>
  </div>
}
@if (contractsQuery().isError()) {
  <div class="flex items-center justify-center h-[calc(100vh-4rem)]">
    <div
      class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-4"
      role="alert"
    >
      <strong class="font-bold">Error!</strong>
      <span class="block sm:inline"> Unable to load contract information.</span>
    </div>
  </div>
} @else {
  <div class="flex justify-between md:flex-row lg:flex-row flex-col">
    <div class="flex items-end gap-4 mb-8">
      <h1 class="text-4xl font-bold text-neutral-700">
        {{ contractsQuery().data()?.customContractId }}
      </h1>
      <tms-tag
        [label]="
          stringUtilsService.capitalizeFirstLetter(
            contractsQuery().data()?.contractStatus
          )
        "
        [styles]="
          stringUtilsService.getStatusTagStyles(
            contractsQuery().data()?.contractStatus
          )
        "
      ></tms-tag>
    </div>
  </div>
  <div class="shadow p-6 rounded-xl bg-white">
    <div class="flex md:items-start justify-between md:flex-row flex-col gap-2">
      <div class="w-full flex justify-between gap-4">
        <div class="flex gap-10 items-center flex-wrap">
          <div class="flex gap-2 items-center text-gray-700">
            <i class="pi pi-calendar"></i>
            <span class="">{{
              contractsQuery().data()?.startDate | date: 'MMM d, y'
            }}</span>
            <span>&ndash;</span>
            <span class="">{{
              contractsQuery().data()?.endDate | date: 'MMM d, y'
            }}</span>
          </div>

          <div class="flex gap-2 items-center justify-center">
            <i class="text-gray-700 pi pi-user"></i>
            <span class="text-gray-700 truncate"
              >Project Manager:
              @for (projectManager of projectManagerDetails(); track $index) {
                <span>{{ projectManager?.name }}</span>
              }
            </span>
          </div>

          <div class="flex gap-2 items-center justify-center">
            <i class="text-gray-700 pi pi-phone"></i>
            @for (projectManager of projectManagerDetails(); track $index) {
              <a
                href="tel: {{ projectManager?.phoneNumber }}"
                class="text-primary-500 hover:underline"
                >{{ projectManager?.phoneNumber }}</a
              >
            }
          </div>
          <div class="flex gap-2 items-center justify-center">
            <i class="text-gray-700 pi pi-envelope"></i>
            @for (projectManager of projectManagerDetails(); track $index) {
              <a
                [href]="'mailto:' + projectManager?.email"
                class="text-primary-500 hover:underline"
                >{{ projectManager?.email }}</a
              >
            }
          </div>
        </div>
        <!-- Display timeline history view for the contract -->
        <tms-timeline-history-view
          timelineHeader="Contract History"
          [timelineHistoryEvents]="contractHistoryQuery().data() ?? []"
          [errorMessage]="errorMessage"
          [isLoading]="isContractHistoryLoading()"
          [hasError]="isContractHistoryError()"
        ></tms-timeline-history-view>
      </div>
    </div>
    <hr class="my-4" />
    <div class="mb-6">
      <p-accordion>
        <p-accordionTab header="Contract Managers">
          <ul class="list-disc list-inside">
            @if (contractsQuery().data()?.contractManagers?.length === 0) {
              <p>
                No contract managers assigned for contract
                {{ contractsQuery().data()?.customContractId }}
              </p>
            } @else {
              @for (
                contractManager of contractsQuery().data()?.contractManagers;
                track $index
              ) {
                <li>
                  {{
                    stringUtilsService.capitalizeFirstLetter(
                      contractManager?.name
                    )
                  }}
                </li>
              }
            }
          </ul>
        </p-accordionTab>
      </p-accordion>
    </div>
    <div class="mb-6 grid gap-4">
      <div class="flex justify-between items-center">
        <h3 class="text-xl font-semibold mb-2">Resources</h3>
        <p-button
          icon="pi pi-plus"
          label="Add Resource"
          (onClick)="openAddResourceForm()"
        ></p-button>
      </div>

      @if (contractsQuery().data()?.contractResource?.length) {
        <div
          class="grid pe-2 grid-cols-1 sm:grid-cols-3 md:grid-cols-5 gap-4 max-h-60 overflow-x-auto"
        >
          @for (
            contractResource of contractsQuery().data()?.contractResource;
            track $index
          ) {
            <div
              class="bg-gray-100 px-3 py-2 rounded-lg flex items-center space-x-2"
            >
              @if (contractResource.contractResource.profilePicUrl) {
                <p-avatar
                  styleClass="w-8 h-8"
                  [image]="contractResource.contractResource.profilePicUrl"
                  shape="circle"
                />
              } @else {
                <p-avatar
                  icon="pi pi-user"
                  shape="circle"
                  styleClass="w-8 h-8"
                ></p-avatar>
              }
              <div class="overflow-hidden">
                <p class="text-sm font-semibold">
                  {{ contractResource?.contractResource?.name }}
                </p>
                <p class="text-sm text-gray-600 truncate">
                  {{ contractResource?.contractResource?.designation }}
                </p>
              </div>
            </div>
          }
        </div>
      } @else {
        <div
          class="bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4"
          role="alert"
        >
          <p class="font-bold">No Resources</p>
          <p>There are currently no resources assigned to this contract.</p>
        </div>
      }
    </div>
    <div>
      <h3 class="text-xl font-semibold mb-2">Comments</h3>
      <form
        class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 mb-2"
        [formGroup]="commentsForm"
        (ngSubmit)="onSubmit()"
      >
        <textarea
          pInputTextarea
          class="flex-grow p-2 border rounded flex-1 resize-none"
          placeholder="Add a comment..."
          formControlName="comments"
        ></textarea>
        <p-button
          severity="primary"
          type="submit"
          label="Add Comment"
          styleClass="h-full"
          [disabled]="
            commentsForm.invalid || !commentsForm.get('comments')?.value.trim()
          "
        ></p-button>
      </form>
      <ul class="mb-2">
        @for (contractComment of visibleContractComments(); track $index) {
          <li class="bg-gray-100 p-2 rounded mb-1">
            <div class="flex justify-between items-start mb-1">
              <span class="font-semibold comment-author">
                {{
                  stringUtilsService.capitalizeFirstLetter(
                    contractComment?.createdBy?.name
                  )
                }}
              </span>

              <span class="text-sm text-gray-500 comment-timestamp">
                {{ contractComment?.updatedAt | date: 'MMM d, y, h:mm a' }}
              </span>
            </div>
            <div class="flex justify-between items-end">
              <div class="flex-1">
                @if (!isCommentEditing(contractComment?.id ?? '')) {
                  <p class="text-gray-700 break-words">
                    {{ contractComment?.comment }}
                  </p>
                } @else {
                  <form
                    class="flex w-full items-end"
                    [formGroup]="
                      getCommentsFormForCurrentCommentId(
                        contractComment?.id ?? ''
                      )
                    "
                    (ngSubmit)="submitEditedComment(contractComment?.id ?? '')"
                  >
                    <textarea
                      #editTextArea
                      pInputTextarea
                      type="text"
                      class="text-gray-700 break-words w-full bg-transparent p-0 border-none ring-0 outline-none"
                      formControlName="comment"
                      [autoResize]="true"
                      rows="1"
                      [id]="'editTextArea-' + contractComment?.id"
                    ></textarea>
                    <p-button
                      type="submit"
                      size="small"
                      icon="pi pi-check"
                      outlined="true"
                      text="true"
                      styleClass="size-8"
                      [disabled]="
                        getCommentsFormForCurrentCommentId(
                          contractComment?.id ?? ''
                        ).invalid ||
                        !getCommentsFormForCurrentCommentId(
                          contractComment?.id ?? ''
                        )
                          .get('comment')
                          ?.value.trim()
                      "
                    ></p-button>
                    <p-button
                      size="small"
                      icon="pi pi-times"
                      outlined="true"
                      text="true"
                      styleClass="size-8"
                      (onClick)="
                        cancelEditingComment(contractComment?.id ?? '')
                      "
                    ></p-button>
                  </form>
                }
              </div>
              <div class="flex-shrink-0 space-x-1">
                @if (!isCommentEditing(contractComment?.id ?? '')) {
                  <p-button
                    size="small"
                    icon="pi pi-pencil"
                    outlined="true"
                    text="true"
                    styleClass="size-8"
                    (onClick)="
                      editComment(
                        contractComment?.id ?? '',
                        contractComment?.comment ?? ''
                      )
                    "
                  ></p-button>
                }
                <p-button
                  size="small"
                  icon="pi pi-trash"
                  outlined="true"
                  styleClass="size-8"
                  text="true"
                  severity="danger"
                  (onClick)="
                    confirmDeleteComment($event, contractComment?.id ?? '')
                  "
                ></p-button>
              </div>
            </div>
          </li>
        }
      </ul>
      @if (
        visibleCommentsCount() <
        (contractsQuery().data()?.contractComments?.length || 0)
      ) {
        <div class="flex justify-center">
          <p-button
            label="Show More"
            (click)="loadMoreComments()"
            icon="pi pi-angle-down"
            text="true"
          ></p-button>
        </div>
      }
    </div>
  </div>
}
<p-toast></p-toast>
<p-confirmPopup></p-confirmPopup>
