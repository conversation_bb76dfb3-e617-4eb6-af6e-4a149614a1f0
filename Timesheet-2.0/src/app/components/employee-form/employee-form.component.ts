import { Component } from '@angular/core';
import { AuthService } from '../../services/auth/auth-service';
import { TeamsService } from '../../services/teams/teams.service';
import { DepartmentService } from '../../services/department/department.service';
import { InputNumberModule } from 'primeng/inputnumber';
import { InputTextareaModule } from 'primeng/inputtextarea';
import { DropdownModule } from 'primeng/dropdown';
import {
  FormBuilder,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { InputTextModule } from 'primeng/inputtext';
import { ButtonModule } from 'primeng/button';
import { InputGroupModule } from 'primeng/inputgroup';
import { ErrorMessage } from '../../services/common.model';
import { FormSubmitButtonLabelService } from '../../services/formsubmitbuttonlabel/formsubmitbuttonlabel.service';
import {
  injectMutation,
  injectQueryClient,
} from '@tanstack/angular-query-experimental';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import {
  Resource,
  AddEmployee,
  UpdateEmployee,
} from '../../services/teams/teams.model';
import { Department } from '../../services/department/department.model';
import { DESIGNATIONS } from '../../utils/designation.utils';
import { CommonModule } from '@angular/common';
import { acceptedIsoCodes } from '../../constants/ISOCodes';
import { RadioButtonModule } from 'primeng/radiobutton';
import { CheckboxModule } from 'primeng/checkbox';
import { TooltipModule } from 'primeng/tooltip';

interface EmployeeFormData {
  name: string;
  email: string;
  countryCode: string;
  phoneNumber: string;
  kekaId: string;
  location: string;
  department: string;
  jobTitle: string;
  reportingTo: string;
  expectedHours: number;
  frequency: string;
  currency: string;
  role: string;
  resourceContractor: boolean;
  canAccessAssetManagement: boolean;
}

interface CurrencyOption {
  label: string;
  value: string;
  icon: string;
}

interface FrequencyOption {
  label: string;
  value: string;
}

interface ExpectedHoursOption {
  label: string;
  value: number;
}

interface RoleOption {
  label: string;
  value: string;
}

export interface Options {
  label: string;
  value: string;
}

@Component({
  selector: 'tms-employee-form',
  standalone: true,
  imports: [
    InputNumberModule,
    InputTextareaModule,
    DropdownModule,
    ReactiveFormsModule,
    InputTextModule,
    ButtonModule,
    InputGroupModule,
    CommonModule,
    RadioButtonModule,
    CheckboxModule,
    TooltipModule,
  ],
  templateUrl: './employee-form.component.html',
})
export class EmployeeFormComponent {
  employeeFormGroup: FormGroup;
  isSubmitting = false;
  isEditMode = false;

  // Inject query client for cache invalidation
  private queryClient = injectQueryClient();

  // Dropdown options
  locationOptions = [
    { label: 'Mangalore', value: 'mangalore' },
    { label: 'Bangalore', value: 'bangalore' },
    { label: 'Toronto', value: 'toronto' },
    { label: 'US', value: 'us' },
  ];

  roleOptions: RoleOption[] = [
    { label: 'Admin', value: 'admin' },
    { label: 'Manager', value: 'manager' },
    { label: 'Employee', value: 'employee' },
  ];

  countryCodes: any[] = [];
  departmentOptions: Options[] = [];
  jobTitleOptions: Options[] = [];
  reportingToOptions: Options[] = [];

  expectedHoursOptions: ExpectedHoursOption[] = Array.from(
    { length: 40 },
    (_, i) => ({
      label: `${i + 1} hrs`,
      value: i + 1,
    })
  );

  frequencyOptions: FrequencyOption[] = [
    { label: 'Daily', value: 'daily' },
    { label: 'Weekly', value: 'weekly' },
  ];

  currencyOptions: CurrencyOption[] = [
    { label: 'INR', value: 'INR', icon: '₹' },
    { label: 'USD', value: 'USD', icon: '$' },
    { label: 'CAN', value: 'CAN', icon: '$' },
    { label: 'EUR', value: 'EUR', icon: '€' },
    { label: 'GBP', value: 'GBP', icon: '£' },
    { label: 'JPY', value: 'JPY', icon: '¥' },
  ];

  constructor(
    private formBuilder: FormBuilder,
    private authService: AuthService,
    private teamsService: TeamsService,
    private departmentService: DepartmentService,
    public formSubmitButtonLabelService: FormSubmitButtonLabelService,
    public dialogRef: DynamicDialogRef,
    private dialogConfig: DynamicDialogConfig
  ) {
    this.isEditMode = dialogConfig.data?.isEditMode;

    // Initialize the form directly in constructor
    this.employeeFormGroup = this.formBuilder.group({
      name: [
        '',
        [
          Validators.required,
          Validators.minLength(2),
          Validators.maxLength(50),
          Validators.pattern(/^(?!\s*$)[A-Za-z ]+$/),
        ],
      ],
      email: ['', [Validators.required, Validators.email]],
      countryCode: [null, Validators.required],
      phoneNumber: ['', [Validators.required, Validators.pattern(/^\d{10}$/)]],
      kekaId: [
        '',
        [
          Validators.required,
          Validators.minLength(3),
          Validators.maxLength(20),
          Validators.pattern(/^[a-zA-Z0-9_-]+$/),
        ],
      ],
      location: [null, Validators.required],
      department: [null, Validators.required],
      jobTitle: [null, Validators.required],
      reportingTo: [null, Validators.required],
      expectedHours: [null, Validators.required],
      frequency: [null, Validators.required],
      currency: [null, Validators.required],
      role: ['employee', Validators.required],
      resourceContractor: [false],
      canAccessAssetManagement: [false],
    });

    // Load dropdown data and then employee data if in edit mode
    this.loadDropdownData()
      .then(() => {
        if (this.isEditMode && this.dialogConfig.data?.employeeData) {
          this.loadEmployeeData();
        }
      })
      .catch((error) => {
        console.error('Error loading dropdown data:', error);
      });

    // Listen for country code changes to update phone number validation
    this.employeeFormGroup.get('countryCode')?.valueChanges.subscribe(() => {
      this.onCountryCodeChange();
    });
  }

  /**
   * Load dropdown data from services
   */
  private async loadDropdownData() {
    // Load country codes
    this.countryCodes = acceptedIsoCodes;

    // Load job titles from designation utils
    this.jobTitleOptions = DESIGNATIONS.map((designation) => ({
      label: designation,
      value: designation,
    }));

    // Load departments directly from service
    try {
      const departments = await this.departmentService.listAllDepartments();
      this.departmentOptions = departments
        .filter(
          (dept: Department) =>
            dept.departmentName && dept.departmentName.trim() !== ''
        )
        .map((dept: Department) => ({
          label: dept.departmentName,
          value: dept.departmentName,
        }));
    } catch (error) {
      console.error('Error loading departments:', error);
      this.departmentOptions = [];
    }

    // Load reporting to options (managers and admins)
    await this.loadReportingToOptions();
  }

  /**
   * Load reporting to options from API
   */
  private async loadReportingToOptions() {
    try {
      const employees = await this.teamsService.getEmployeesForReporting();

      // Filter out the current employee if in edit mode
      let filteredEmployees = employees;
      if (this.isEditMode && this.dialogConfig.data?.employeeData?.id) {
        const currentEmployeeId = this.dialogConfig.data.employeeData.id;
        filteredEmployees = employees.filter(
          (employee) => employee.id !== currentEmployeeId
        );
      }

      this.reportingToOptions = filteredEmployees.map((employee) => ({
        label: employee.name,
        value: employee.id,
      }));
    } catch (error) {
      console.error('Error loading reporting options:', error);
      this.reportingToOptions = [{ label: 'Select Manager', value: '' }];
    }
  }

  /**
   * Load phone number details from full phone number
   */
  private loadPhoneNumberDetails(fullPhoneNumber: string) {
    let matchedCode: string | null = null;
    let phoneNumber: string | null = null;
    for (const code of this.countryCodes) {
      if (fullPhoneNumber?.startsWith(code.value)) {
        matchedCode = code;
        phoneNumber = fullPhoneNumber.substring(code.value.length);
        break;
      } else {
        phoneNumber = fullPhoneNumber;
      }
    }
    return { matchedCode, phoneNumber };
  }

  /**
   * Load existing employee data into the form if in edit mode
   */
  private async loadEmployeeData() {
    const employeeData: Resource = this.dialogConfig.data?.employeeData;

    if (employeeData && this.isEditMode) {
      try {
        // Fetch fresh employee data from API
        const freshEmployeeData = await this.teamsService.getEmployeeById(
          employeeData.id
        );

        const { matchedCode, phoneNumber } = this.loadPhoneNumberDetails(
          freshEmployeeData.phoneNumber ?? ''
        );

        // Wait for dropdown data to be loaded
        await this.loadDropdownData();

        const locationOption = this.locationOptions.find(
          (option) =>
            option.value.toLowerCase() ===
            freshEmployeeData.location?.toLowerCase()
        );

        const departmentOption = this.departmentOptions.find(
          (option) =>
            option.value.toLowerCase() ===
            freshEmployeeData.department?.departmentName?.toLowerCase()
        );

        const jobTitleOption = this.jobTitleOptions.find(
          (option) =>
            option.value.toLowerCase() ===
            freshEmployeeData.designation?.toLowerCase()
        );

        const reportingToOption = this.reportingToOptions.find(
          (option) => option.value === freshEmployeeData.managerId
        );

        const expectedHoursOption = this.expectedHoursOptions.find(
          (option) => option.value === Number(freshEmployeeData.expectedHours)
        );

        const frequencyOption = this.frequencyOptions.find(
          (option) =>
            option.value.toLowerCase() ===
            freshEmployeeData.expectedHoursFrequency?.toLowerCase()
        );

        const currencyOption = this.currencyOptions.find(
          (option) =>
            option.value.toLowerCase() ===
            freshEmployeeData.currency?.toLowerCase()
        );

        const roleOption = this.roleOptions.find(
          (option) =>
            option.value.toLowerCase() === freshEmployeeData.role?.toLowerCase()
        );

        const formData = {
          name: freshEmployeeData.name,
          email: freshEmployeeData.email,
          countryCode: phoneNumber ? matchedCode : null,
          phoneNumber: (phoneNumber ?? '').trim(),
          kekaId: freshEmployeeData.kekaId || '',
          location: locationOption || {
            label: freshEmployeeData.location,
            value: freshEmployeeData.location,
          },
          department: departmentOption || {
            label: freshEmployeeData.department?.departmentName,
            value: freshEmployeeData.department?.departmentName,
          },
          jobTitle: jobTitleOption || {
            label: freshEmployeeData.designation,
            value: freshEmployeeData.designation,
          },
          reportingTo: reportingToOption || null,
          expectedHours: expectedHoursOption || {
            label: `${freshEmployeeData.expectedHours} hrs`,
            value: freshEmployeeData.expectedHours,
          },
          frequency: frequencyOption || {
            label: freshEmployeeData.expectedHoursFrequency,
            value: freshEmployeeData.expectedHoursFrequency,
          },
          currency: currencyOption || {
            label: freshEmployeeData.currency,
            value: freshEmployeeData.currency,
            icon: this.getCurrencyIcon(freshEmployeeData.currency),
          },
          role: freshEmployeeData.role || 'employee',
          resourceContractor: freshEmployeeData.resourceContractor || false,
          canAccessAssetManagement:
            freshEmployeeData.canAccessAssetManagement || false,
        };

        this.employeeFormGroup.patchValue(formData);
        this.employeeFormGroup.updateValueAndValidity({
          onlySelf: false,
          emitEvent: true,
        });

        // Apply country-specific validation after loading data
        this.onCountryCodeChange();
      } catch (error) {
        console.error('Error loading employee data:', error);
        // Fallback to using the passed employee data if API call fails
        const { matchedCode, phoneNumber } = this.loadPhoneNumberDetails(
          employeeData.phoneNumber ?? ''
        );

        // Wait for dropdown data to be loaded
        await this.loadDropdownData();

        const locationOption = this.locationOptions.find(
          (option) =>
            option.value.toLowerCase() === employeeData.location?.toLowerCase()
        );

        const departmentOption = this.departmentOptions.find(
          (option) =>
            option.value.toLowerCase() ===
            employeeData.department?.departmentName?.toLowerCase()
        );

        const jobTitleOption = this.jobTitleOptions.find(
          (option) =>
            option.value.toLowerCase() ===
            employeeData.designation?.toLowerCase()
        );

        const reportingToOption = this.reportingToOptions.find(
          (option) =>
            option.value === employeeData.reportingTo || employeeData.managerId
        );

        const expectedHoursOption = this.expectedHoursOptions.find(
          (option) => option.value === Number(employeeData.expectedHours)
        );

        const frequencyOption = this.frequencyOptions.find(
          (option) =>
            option.value.toLowerCase() ===
            (
              employeeData.frequency || employeeData.expectedHoursFrequency
            )?.toLowerCase()
        );

        const currencyOption = this.currencyOptions.find(
          (option) =>
            option.value.toLowerCase() === employeeData.currency?.toLowerCase()
        );

        const roleOption = this.roleOptions.find(
          (option) =>
            option.value.toLowerCase() === employeeData.role?.toLowerCase()
        );

        this.employeeFormGroup.patchValue({
          name: employeeData.name,
          email: employeeData.email,
          countryCode: phoneNumber ? matchedCode : null,
          phoneNumber: (phoneNumber ?? '').trim(),
          kekaId: employeeData.kekaId || '',
          location: locationOption || {
            label: employeeData.location,
            value: employeeData.location,
          },
          department: departmentOption || {
            label: employeeData.department?.departmentName,
            value: employeeData.department?.departmentName,
          },
          jobTitle: jobTitleOption || {
            label: employeeData.designation,
            value: employeeData.designation,
          },
          reportingTo: reportingToOption || null,
          expectedHours: expectedHoursOption || {
            label: `${employeeData.expectedHours} hrs`,
            value: employeeData.expectedHours,
          },
          frequency: frequencyOption || {
            label:
              employeeData.frequency || employeeData.expectedHoursFrequency,
            value:
              employeeData.frequency || employeeData.expectedHoursFrequency,
          },
          currency: currencyOption || {
            label: employeeData.currency,
            value: employeeData.currency,
            icon: this.getCurrencyIcon(employeeData.currency),
          },
          role: employeeData.role || 'employee',
          resourceContractor: employeeData.resourceContractor || false,
          canAccessAssetManagement:
            employeeData.canAccessAssetManagement || false,
        });
        this.employeeFormGroup.updateValueAndValidity({
          onlySelf: false,
          emitEvent: true,
        });

        // Apply country-specific validation after loading data
        this.onCountryCodeChange();
      }
    }
  }

  /**
   * Mutation hook for creating a new employee
   */
  createEmployeeQuery = injectMutation(() => ({
    mutationKey: ['employee', this.authService.userId()],
    mutationFn: (employeeData: AddEmployee) =>
      this.teamsService.addEmployee(employeeData),
    onSuccess: (data) => {
      // Invalidate and refetch teams queries
      this.queryClient.invalidateQueries({
        queryKey: ['getTeamsWithFilters'],
      });
      this.dialogRef.close({
        type: 'success',
        severity: 'success',
        message: 'Employee created successfully!',
        summary: 'Employee Created.',
      });
    },
    onError: (error) => {
      console.error('Error creating employee:', error);

      const errorMessages = Array.isArray(error.message)
        ? error.message
        : [error.message];

      errorMessages.forEach((msg: string) => {
        this.dialogRef.close({
          type: 'error',
          severity: 'error',
          message: msg,
          summary: 'Unable to add an employee',
        });
      });
    },
  }));

  /**
   * Mutation to update an existing employee
   */
  updateEmployeeQuery = injectMutation(() => ({
    mutationKey: ['updateEmployee', this.authService.userId()],
    mutationFn: (employeeData: UpdateEmployee) =>
      this.teamsService.updateEmployee(employeeData),
    onSuccess: (data) => {
      // Invalidate and refetch teams queries
      this.queryClient.invalidateQueries({
        queryKey: ['getTeamsWithFilters'],
      });
      this.dialogRef.close({
        type: 'success',
        severity: 'success',
        message: 'Employee updated successfully!',
        summary: 'Employee Updated.',
      });
    },
    onError: (error) => {
      console.error('Error updating employee:', error);
      const errorMessages = Array.isArray(error.message)
        ? error.message
        : [error.message];
      errorMessages.forEach((msg: string) => {
        this.dialogRef.close({
          type: 'error',
          severity: 'error',
          message: msg,
          summary: 'Failed to update the employee',
        });
      });
    },
  }));

  onSubmit() {
    if (this.employeeFormGroup.invalid) {
      this.employeeFormGroup.markAllAsTouched();
      return;
    }

    this.isSubmitting = true;

    const formData: EmployeeFormData = this.employeeFormGroup.value;
    const employeeDataById: Resource = this.dialogConfig.data?.employeeData;

    // Helper function to extract value from dropdown objects
    const getValue = (field: any): any => {
      if (field && typeof field === 'object' && field.value !== undefined) {
        return field.value;
      }
      return field;
    };

    // Fix phone number concatenation
    const countryCode = getValue(formData.countryCode) || '';
    const phoneNumber = formData.phoneNumber?.trim() || '';
    const fullPhoneNumber = `${countryCode}${phoneNumber}`;

    if (this.isEditMode) {
      // For edit mode, send complete payload with employeeId
      const updateEmployeeData: UpdateEmployee = {
        employeeId: employeeDataById.id,
        name: formData.name.trim(),
        email: formData.email.trim(),
        phoneNumber: fullPhoneNumber,
        kekaId: formData.kekaId.trim(),
        location: getValue(formData.location),
        department: getValue(formData.department),
        designation: getValue(formData.jobTitle),
        managerId: getValue(formData.reportingTo),
        currency: getValue(formData.currency),
        expectedHours: getValue(formData.expectedHours),
        expectedHoursFrequency: getValue(formData.frequency),
        role: formData.role,
        resourceContractor: formData.resourceContractor,
        canAccessAssetManagement: formData.canAccessAssetManagement,
      };

      this.updateEmployeeQuery.mutate(updateEmployeeData);
    } else {
      // For add mode, send all required fields
      const addEmployeeData: AddEmployee = {
        name: formData.name.trim(),
        email: formData.email.trim(),
        phoneNumber: fullPhoneNumber,
        kekaId: formData.kekaId.trim(),
        location: getValue(formData.location),
        department: getValue(formData.department),
        designation: getValue(formData.jobTitle),
        managerId: getValue(formData.reportingTo),
        currency: getValue(formData.currency),
        expectedHours: getValue(formData.expectedHours),
        expectedHoursFrequency: getValue(formData.frequency),
        role: formData.role,
        resourceContractor: formData.resourceContractor,
        canAccessAssetManagement: formData.canAccessAssetManagement,
      };

      this.createEmployeeQuery.mutate(addEmployeeData);
    }
    this.employeeFormGroup.disable();
  }

  checkErrors(fieldName: keyof EmployeeFormData): string | null {
    const control = this.employeeFormGroup.get(fieldName);

    // Handle phone number errors dynamically based on country code
    if (fieldName === 'phoneNumber' && control?.errors?.['pattern']) {
      return this.getPhoneNumberErrorMessage();
    }

    const errorMessage: ErrorMessage<EmployeeFormData> = {
      name: {
        required: 'Employee name is required.',
        minlength: 'Employee name is too short.',
        maxlength: 'Employee name is too long.',
        pattern:
          'Employee name can only contain alphabets and spaces, and cannot be empty or just spaces.',
      },
      email: {
        required: 'Email is required.',
        email: 'Enter a valid email address.',
      },
      countryCode: {
        required: 'Country code is required.',
      },
      phoneNumber: {
        required: 'Phone number is required.',
        pattern: 'Enter a valid phone number for the selected country.',
      },
      kekaId: {
        required: 'Keka ID is required.',
        minlength: 'Keka ID is too short.',
        maxlength: 'Keka ID is too long.',
        pattern:
          'Keka ID can only contain letters, numbers, hyphens, and underscores.',
      },
      location: {
        required: 'Location is required.',
      },
      department: {
        required: 'Department is required.',
      },
      jobTitle: {
        required: 'Job title is required.',
      },
      reportingTo: {
        required: 'Reporting manager is required.',
      },
      expectedHours: {
        required: 'Expected hours is required.',
      },
      frequency: {
        required: 'Frequency is required.',
      },
      currency: {
        required: 'Currency is required.',
      },
      role: {
        required: 'Role is required.',
      },
      resourceContractor: {},
      canAccessAssetManagement: {},
    };

    if (
      control?.invalid &&
      (control.touched || control.dirty) &&
      control.errors
    ) {
      for (const key of Object.keys(control.errors)) {
        const message =
          errorMessage[fieldName]?.[
            key as keyof (typeof errorMessage)[typeof fieldName]
          ];
        if (message) return message;
      }
    }

    return null;
  }

  get isSubmitButtonDisabled() {
    return this.employeeFormGroup.invalid || this.isSubmitting;
  }

  // Helper method to get currency icon
  private getCurrencyIcon(currency: string | null | undefined): string {
    if (!currency) return '₹';
    const currencyOption = this.currencyOptions.find(
      (option) => option.value.toLowerCase() === currency.toLowerCase()
    );
    return currencyOption?.icon || '₹';
  }

  /**
   * Get phone number validation pattern based on country code
   */
  getPhoneNumberPattern(countryCode: string): RegExp {
    switch (countryCode) {
      case '+91': // India
        return /^[6-9]\d{9}$/; // 10 digits starting with 6, 7, 8, or 9
      case '+1': // US/Canada
        return /^\d{10}$/; // 10 digits (area code + number)
      case '+44': // UK
        return /^[1-9]\d{9}$/; // 10 digits starting with 1-9
      default:
        return /^\d{10}$/; // Default 10 digits
    }
  }

  /**
   * Get phone number placeholder based on country code
   */
  getPhoneNumberPlaceholder(countryCode: string): string {
    switch (countryCode) {
      case '+91': // India
        return 'Enter 10-digit mobile number';
      case '+1': // US/Canada
        return 'Enter 10-digit phone number';
      case '+44': // UK
        return 'Enter 10-digit phone number';
      default:
        return 'Enter phone number';
    }
  }

  /**
   * Get phone number error message based on country code
   */
  getPhoneNumberErrorMessage(): string {
    const countryCode = this.employeeFormGroup.get('countryCode')?.value;
    const countryCodeValue = countryCode?.value || countryCode;

    switch (countryCodeValue) {
      case '+91': // India
        return 'Enter a valid 10-digit mobile number starting with 6, 7, 8, or 9';
      case '+1': // US/Canada
        return 'Enter a valid 10-digit phone number (area code + number)';
      case '+44': // UK
        return 'Enter a valid 10-digit phone number starting with 1-9';
      default:
        return 'Enter a valid phone number for the selected country';
    }
  }

  /**
   * Update phone number validation when country code changes
   */
  onCountryCodeChange() {
    const countryCode = this.employeeFormGroup.get('countryCode')?.value;
    const phoneNumberControl = this.employeeFormGroup.get('phoneNumber');

    if (countryCode && phoneNumberControl) {
      const pattern = this.getPhoneNumberPattern(
        countryCode.value || countryCode
      );
      phoneNumberControl.setValidators([
        Validators.required,
        Validators.pattern(pattern),
      ]);
      phoneNumberControl.updateValueAndValidity();
    }
  }
}
