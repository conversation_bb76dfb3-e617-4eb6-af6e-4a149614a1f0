<form
  [formGroup]="employeeFormGroup"
  (ngSubmit)="onSubmit()"
  class="flex flex-col w-full"
>
  <div class="flex gap-6">
    <!-- Left Section - Existing Form Fields -->
    <div class="flex-1">
      <div class="flex flex-col space-y-0.5 w-full">
        <div class="flex flex-col gap-1">
          <label for="name" class="text-sm text-neutral-500"
            >Employee Name<span class="font-bold text-red-500">*</span></label
          >
          <input
            pInputText
            id="name"
            formControlName="name"
            placeholder="Enter employee name"
            [style]="{ width: '100%' }"
          />
          <small class="text-red-500 min-h-[1rem] block">
            {{ checkErrors('name') || '\u00A0' }}
          </small>
        </div>

        <div class="grid md:grid-cols-2 gap-4">
          <div class="flex flex-col gap-1">
            <label for="email" class="text-sm text-neutral-500"
              >Email ID<span class="font-bold text-red-500">*</span></label
            >
            <input
              pInputText
              id="email"
              formControlName="email"
              placeholder="Enter email address"
              [style]="{ width: '100%' }"
            />
            <small class="text-red-500 min-h-[1rem] block">
              {{ checkErrors('email') || '\u00A0' }}
            </small>
          </div>

          <div class="flex flex-col w-full gap-1">
            <label for="phoneNumber" class="text-sm text-neutral-500">
              Contact Number<span class="font-bold text-red-500">*</span>
            </label>

            <div class="flex w-full" id="phoneNumber">
              <p-dropdown
                formControlName="countryCode"
                [options]="countryCodes"
                optionLabel="value"
                [style]="{ width: '100%' }"
                styleClass="border-r-0 rounded-r-none px-0"
                [dropdownIcon]="'pi pi-angle-down'"
                (onChange)="onCountryCodeChange()"
              >
                <ng-template pTemplate="selectedItem" let-selected>
                  <div class="flex items-center">
                    <div>{{ selected.value }}</div>
                  </div>
                </ng-template>
                <ng-template let-country pTemplate="item">
                  <div class="flex items-center">
                    <div>{{ country.label }}</div>
                  </div>
                </ng-template>
              </p-dropdown>

              <input
                pInputText
                formControlName="phoneNumber"
                type="tel"
                maxlength="15"
                [style]="{ width: '100%' }"
                [placeholder]="
                  getPhoneNumberPlaceholder(
                    employeeFormGroup.get('countryCode')?.value?.value || ''
                  )
                "
                class="rounded-l-none pl-1"
              />
            </div>
            <small class="text-red-500 min-h-[1rem] block">
              {{
                checkErrors('countryCode') ||
                  checkErrors('phoneNumber') ||
                  '\u00A0'
              }}
            </small>
          </div>
        </div>

        <div class="grid md:grid-cols-2 gap-4">
          <div class="flex flex-col gap-1">
            <label for="kekaId" class="text-sm text-neutral-500"
              >Keka ID<span class="font-bold text-red-500">*</span></label
            >
            <input
              pInputText
              id="kekaId"
              formControlName="kekaId"
              placeholder="Enter Keka ID"
              [style]="{ width: '100%' }"
            />
            <small class="text-red-500 min-h-[1rem] block">
              {{ checkErrors('kekaId') || '\u00A0' }}
            </small>
          </div>

          <div class="flex flex-col gap-1">
            <label for="location" class="text-sm text-neutral-500">
              Location<span class="font-bold text-red-500">*</span>
            </label>
            <p-dropdown
              formControlName="location"
              [options]="locationOptions"
              optionLabel="label"
              [style]="{ width: '100%' }"
              [dropdownIcon]="'pi pi-angle-down'"
              [placeholder]="'Select location'"
            >
              <ng-template pTemplate="selectedItem" let-selected>
                <div class="flex items-center">
                  <div>{{ selected.label }}</div>
                </div>
              </ng-template>
              <ng-template let-location pTemplate="item">
                <div class="flex items-center">
                  <div>{{ location.label }}</div>
                </div>
              </ng-template>
            </p-dropdown>
            <small class="text-red-500 min-h-[1rem] block">
              {{ checkErrors('location') || '\u00A0' }}
            </small>
          </div>
        </div>

        <div class="grid md:grid-cols-2 gap-4">
          <div class="flex flex-col gap-1">
            <label for="department" class="text-sm text-neutral-500">
              Department<span class="font-bold text-red-500">*</span>
            </label>
            <p-dropdown
              formControlName="department"
              [options]="departmentOptions"
              optionLabel="label"
              [style]="{ width: '100%' }"
              [dropdownIcon]="'pi pi-angle-down'"
              [placeholder]="'Select department'"
              [filter]="true"
              filterPlaceholder="Search department"
              [resetFilterOnHide]="true"
            >
              <ng-template pTemplate="selectedItem" let-selected>
                <div class="flex items-center">
                  <div>{{ selected.label }}</div>
                </div>
              </ng-template>
              <ng-template let-department pTemplate="item">
                <div class="flex items-center">
                  <div>{{ department.label }}</div>
                </div>
              </ng-template>
            </p-dropdown>
            <small class="text-red-500 min-h-[1rem] block">
              {{ checkErrors('department') || '\u00A0' }}
            </small>
          </div>

          <div class="flex flex-col gap-1">
            <label for="jobTitle" class="text-sm text-neutral-500">
              Job Title<span class="font-bold text-red-500">*</span>
            </label>
            <p-dropdown
              formControlName="jobTitle"
              [options]="jobTitleOptions"
              optionLabel="label"
              [style]="{ width: '100%' }"
              [dropdownIcon]="'pi pi-angle-down'"
              [placeholder]="'Select job title'"
              [filter]="true"
              filterPlaceholder="Search job title"
              [resetFilterOnHide]="true"
            >
              <ng-template pTemplate="selectedItem" let-selected>
                <div class="flex items-center">
                  <div>{{ selected.label }}</div>
                </div>
              </ng-template>
              <ng-template let-jobTitle pTemplate="item">
                <div class="flex items-center">
                  <div>{{ jobTitle.label }}</div>
                </div>
              </ng-template>
            </p-dropdown>
            <small class="text-red-500 min-h-[1rem] block">
              {{ checkErrors('jobTitle') || '\u00A0' }}
            </small>
          </div>
        </div>

        <div class="grid md:grid-cols-2 gap-4">
          <div class="flex flex-col gap-1">
            <label for="reportingTo" class="text-sm text-neutral-500">
              Reporting To<span class="font-bold text-red-500">*</span>
            </label>
            <p-dropdown
              formControlName="reportingTo"
              [options]="reportingToOptions"
              optionLabel="label"
              [style]="{ width: '100%' }"
              [dropdownIcon]="'pi pi-angle-down'"
              [placeholder]="'Select reporting manager'"
              [filter]="true"
              filterPlaceholder="Search manager"
              [resetFilterOnHide]="true"
            >
              <ng-template pTemplate="selectedItem" let-selected>
                <div class="flex items-center">
                  <div>{{ selected.label }}</div>
                </div>
              </ng-template>
              <ng-template let-reportingTo pTemplate="item">
                <div class="flex items-center">
                  <div>{{ reportingTo.label }}</div>
                </div>
              </ng-template>
            </p-dropdown>
            <small class="text-red-500 min-h-[1rem] block">
              {{ checkErrors('reportingTo') || '\u00A0' }}
            </small>
          </div>

          <div class="flex flex-col gap-1">
            <label for="expectedHours" class="text-sm text-neutral-500">
              Expected Hours<span class="font-bold text-red-500">*</span>
            </label>
            <p-dropdown
              formControlName="expectedHours"
              [options]="expectedHoursOptions"
              optionLabel="label"
              [style]="{ width: '100%' }"
              [dropdownIcon]="'pi pi-angle-down'"
              [placeholder]="'Select expected hours'"
              [filter]="true"
              filterPlaceholder="Search hours"
              [resetFilterOnHide]="true"
            >
              <ng-template pTemplate="selectedItem" let-selected>
                <div class="flex items-center">
                  <div>{{ selected.label }}</div>
                </div>
              </ng-template>
              <ng-template let-expectedHours pTemplate="item">
                <div class="flex items-center">
                  <div>{{ expectedHours.label }}</div>
                </div>
              </ng-template>
            </p-dropdown>
            <small class="text-red-500 min-h-[1rem] block">
              {{ checkErrors('expectedHours') || '\u00A0' }}
            </small>
          </div>
        </div>

        <div class="grid md:grid-cols-2 gap-4">
          <div class="flex flex-col gap-1">
            <label for="frequency" class="text-sm text-neutral-500">
              Frequency<span class="font-bold text-red-500">*</span>
            </label>
            <p-dropdown
              formControlName="frequency"
              [options]="frequencyOptions"
              optionLabel="label"
              [style]="{ width: '100%' }"
              [dropdownIcon]="'pi pi-angle-down'"
              [placeholder]="'Select frequency'"
            >
              <ng-template pTemplate="selectedItem" let-selected>
                <div class="flex items-center">
                  <div>{{ selected.label }}</div>
                </div>
              </ng-template>
              <ng-template let-frequency pTemplate="item">
                <div class="flex items-center">
                  <div>{{ frequency.label }}</div>
                </div>
              </ng-template>
            </p-dropdown>
            <small class="text-red-500 min-h-[1rem] block">
              {{ checkErrors('frequency') || '\u00A0' }}
            </small>
          </div>

          <div class="flex flex-col gap-1">
            <label for="currency" class="text-sm text-neutral-500">
              Currency<span class="font-bold text-red-500">*</span>
            </label>
            <p-dropdown
              formControlName="currency"
              [options]="currencyOptions"
              optionLabel="label"
              [style]="{ width: '100%' }"
              [dropdownIcon]="'pi pi-angle-down'"
              [placeholder]="'Select currency'"
            >
              <ng-template pTemplate="selectedItem" let-selected>
                <div class="flex items-center">
                  <span class="mr-2">{{ selected.icon }}</span>
                  <span>{{ selected.label }}</span>
                </div>
              </ng-template>
              <ng-template let-currency pTemplate="item">
                <div class="flex items-center">
                  <span class="mr-2">{{ currency.icon }}</span>
                  <span>{{ currency.label }}</span>
                </div>
              </ng-template>
            </p-dropdown>
            <small class="text-red-500 min-h-[1rem] block">
              {{ checkErrors('currency') || '\u00A0' }}
            </small>
          </div>
        </div>
      </div>
    </div>

    <!-- Right Section - Role and Checkboxes -->
    <div class="w-1/3">
      <div class="bg-gray-200 p-6 rounded-lg h-fit">
        <!-- Role Section -->
        <div class="mb-6">
          <div class="flex items-center gap-2 mb-4">
            <h3 class="text-lg font-semibold text-gray-800">Role</h3>
            <i
              class="pi pi-info-circle text-blue-500 cursor-pointer"
              [pTooltip]="
                'Select your role to ensure appropriate access and permissions.'
              "
              tooltipPosition="top"
            ></i>
          </div>
          <div class="space-y-3">
            @for (role of roleOptions; track role.value) {
              <div class="flex items-center">
                <p-radioButton
                  [value]="role.value"
                  formControlName="role"
                  [inputId]="'role-' + role.value"
                  name="role"
                ></p-radioButton>
                <label
                  [for]="'role-' + role.value"
                  class="ml-2 text-sm text-gray-700 cursor-pointer"
                >
                  {{ role.label }}
                </label>
              </div>
            }
          </div>
          <small class="text-red-500 min-h-[1rem] block">
            {{ checkErrors('role') || '\u00A0' }}
          </small>
        </div>

        <!-- Checkboxes Section -->
        <div class="space-y-4">
          <div class="flex items-center">
            <p-checkbox
              formControlName="resourceContractor"
              [binary]="true"
              inputId="resourceContractor"
            ></p-checkbox>
            <label
              for="resourceContractor"
              class="ml-2 text-sm text-gray-700 cursor-pointer"
            >
              If employee is a contractor
            </label>
          </div>

          <div class="flex items-center">
            <p-checkbox
              formControlName="canAccessAssetManagement"
              [binary]="true"
              inputId="canAccessAssetManagement"
            ></p-checkbox>
            <label
              for="canAccessAssetManagement"
              class="ml-2 text-sm text-gray-700 cursor-pointer"
            >
              Can Access Asset Management
            </label>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Save Button at bottom line -->
  <div class="flex justify-end">
    <p-button
      type="submit"
      [label]="
        formSubmitButtonLabelService.getFormSubmitButtonLabel(
          isSubmitting,
          isEditMode
        )
      "
      [icon]="
        formSubmitButtonLabelService.getFormSubmitButtonIcon(isSubmitting)
      "
      [disabled]="isSubmitButtonDisabled"
      styleClass="submit-button disabled:cursor-not-allowed disabled:bg-neutral-400 disabled:border-neutral-400"
    ></p-button>
  </div>
</form>
