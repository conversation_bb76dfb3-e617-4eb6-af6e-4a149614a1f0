/* Efficient styles for the ComboBox (MultiSelect) component */

/* General multiselect styling */
.custom-multiselect {
  @apply w-full border border-neutral-300 hover:border-primary-500 rounded-lg transition duration-200;
}

/* Focus state for non-disabled multiselect */
.p-multiselect:not(.p-disabled).p-focus {
  @apply border-primary-500; /* Tailwind utilities for border color and shadow */
  outline: none;
  box-shadow: 0 0 0 2px rgba(77, 175, 224, 0.2);
}

/* Filter input focus styling inside dropdown panel */
.p-multiselect-panel
  .p-multiselect-filter-container
  .p-multiselect-filter:focus,
.p-multiselect-panel
  .p-multiselect-filter-container
  .p-multiselect-filter:hover {
  @apply border-primary-500;
  outline: none;
  box-shadow: 0 0 0 2px rgba(77, 175, 224, 0.2);
}

/* Icon styling for partially selected state */
.custom-panel-style .pi.pi-minus {
  @apply text-white bg-primary-500 border-primary-500 rounded p-1;
}

/* Remove padding from multiselect items */
.p-multiselect-items {
  @apply p-0;
}

/* Checkbox styling for highlighted selection and hover state */
.custom-panel-style .p-checkbox-box.p-highlight {
  @apply bg-primary-500 border-primary-500;
}

.custom-panel-style .p-checkbox-box:hover {
  @apply border-primary-500;
}
