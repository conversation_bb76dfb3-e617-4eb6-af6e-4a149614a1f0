<!-- 
  Multi-select component for selecting options for search, selection limit, 
  and customizable styles. Uses PrimeNG's p-multiSelect with support for virtual scrolling 
  and a "select all" checkbox. The component binds to an array of selected options and 
  supports disabling and clearing selections.
-->

<div class="card flex justify-content-center">
  <p-multiSelect
    [options]="options()"
    [showToggleAll]="true"
    [(ngModel)]="selectedOptions"
    [virtualScroll]="true"
    [virtualScrollItemSize]="43"
    [selectionLimit]="selectionLimit()"
    optionLabel="name"
    placeholder="Select Options"
    class="custom-multiselect"
    [style]="{ width: '100%', borderRadius: '0.5rem' }"
    [disabled]="disabled()"
    [showClear]="true"
    [filter]="isSearchEnabled()"
    [panelStyleClass]="'custom-panel-style'"
    (onSelectAllChange)="onSelectAllChange($event)"
    (onChange)="onSelectionChange()"
    (onClear)="onClear()"
    #ms
  >
    <!-- Custom template for "select all" checkbox icons -->
    <ng-template
      pTemplate="headercheckboxicon"
      let-allSelected
      let-partialSelected="partialSelected"
    >
      @if (allSelected) {
        <i class="pi pi-check"></i>
      } @else if (partialSelected) {
        <i class="pi pi-minus"></i>
      }</ng-template
  ></p-multiSelect>
</div>
