import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { ComboBoxComponent } from './combo-box.component';
import { ComponentRef } from '@angular/core';

/**
 * Test suite for ComboBoxComponent
 *
 * This suite verifies the key functionalities of the ComboBoxComponent, including:
 * - Component creation and initialization
 * - Handling of selection events like "select all" and "deselect all"
 * - Correct display of placeholder when no options are available
 * - Correct handling of selection limit input
 */

describe('ComboBoxComponent', () => {
  let component: ComboBoxComponent;
  let fixture: ComponentFixture<ComboBoxComponent>;
  let componentRef: ComponentRef<ComboBoxComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ComboBoxComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(ComboBoxComponent);
    component = fixture.componentInstance;
    componentRef = fixture.componentRef;
    componentRef.setInput('options', [
      { name: 'New York', code: 'NY' },
      { name: 'Los Angeles', code: 'LA' },
      { name: 'Chicago', code: 'CH' },
    ]);
    fixture.detectChanges();
  });

  /**
   * Test case: Verifies that the component is created successfully.
   */
  it('should create', () => {
    expect(component).toBeTruthy();
  });

  /**
   * Test case: Verifies that all options are selected when the "select all"
   * checkbox is checked.
   */
  it('should select all options when onSelectAllChange is triggered with checked true', () => {
    componentRef.setInput('options', [
      { name: 'New York', code: 'NY' },
      { name: 'Los Angeles', code: 'LA' },
      { name: 'Chicago', code: 'CH' },
    ]);
    fixture.detectChanges();
    component.onSelectAllChange({ checked: true });
    expect(component.selectedOptions.length).toBe(3);
  });

  /**
   * Test case: Verifies that selectedOptions is empty when there are no options.
   */
  it('should set selectedOptions as empty when options are empty', () => {
    componentRef.setInput('options', []);
    expect(component.selectedOptions.length).toBe(0);
  });

  /**
   * Test case: Verifies that the selectionLimit input is properly passed
   * to the p-multiSelect component.
   */
  it('should correctly handle selectionLimit input', () => {
    componentRef.setInput('selectionLimit', 2);
    fixture.detectChanges();
    const multiSelect = fixture.debugElement.query(By.css('p-multiSelect'));
    expect(multiSelect.componentInstance.selectionLimit).toBe(2);
  });

  /**
   * Test case: Verifies that the placeholder text is correctly rendered
   * when options are empty.
   */
  it('should render placeholder when options are empty', () => {
    componentRef.setInput('options', []);
    fixture.detectChanges();
    const placeholderText = fixture.debugElement
      .query(By.css('p-multiSelect'))
      .nativeElement.getAttribute('placeholder');
    expect(placeholderText).toBe('Select Options');
  });

  /**
   * Test case: Verifies that all options are deselected when the "select all"
   * checkbox is unchecked.
   */
  it('should deselect all options when onSelectAllChange is triggered with checked false', () => {
    componentRef.setInput('options', [
      { name: 'New York', code: 'NY' },
      { name: 'Los Angeles', code: 'LA' },
      { name: 'Chicago', code: 'CH' },
    ]);
    fixture.detectChanges();
    component.onSelectAllChange({ checked: false });
    expect(component.selectedOptions.length).toBe(0);
  });
});
