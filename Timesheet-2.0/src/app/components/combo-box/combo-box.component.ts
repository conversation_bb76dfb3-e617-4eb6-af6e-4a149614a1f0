import {
  Component,
  Input,
  ViewChild,
  ViewEncapsulation,
  forwardRef,
  input,
  signal,
} from '@angular/core';
import {
  ControlValueAccessor,
  NG_VALUE_ACCESSOR,
  FormsModule,
} from '@angular/forms';
import { MultiSelectModule } from 'primeng/multiselect';
import { MultiSelect } from 'primeng/multiselect';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'tms-combo-box',
  standalone: true,
  imports: [MultiSelectModule, FormsModule, CommonModule],
  templateUrl: './combo-box.component.html',
  styleUrl: './combo-box.component.css',
  encapsulation: ViewEncapsulation.None,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => ComboBoxComponent),
      multi: true,
    },
  ],
})
export class ComboBoxComponent implements ControlValueAccessor {
  @ViewChild('ms') ms!: MultiSelect;
  options = input<any>([]);
  disabled = input<boolean>(false);
  selectionLimit = input<number>();
  isSearchEnabled = input<boolean>(true);

  selectedOptions: any[] = [];
  selectAll: boolean = false;

  onChange: any = () => {};
  onTouched: any = () => {};

  writeValue(value: any[]): void {
    if (value !== undefined) {
      this.selectedOptions = value;
    }
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  onSelectAllChange(event: any) {
    this.selectedOptions = event.checked ? [...this.ms.visibleOptions()] : [];
    this.selectAll = event.checked;
    this.onChange(this.selectedOptions);
    this.onTouched();
  }

  onSelectionChange() {
    this.onChange(this.selectedOptions);
    this.onTouched();
  }

  onClear() {
    // Reset the selected options when the "X" (clear) button is clicked
    this.selectedOptions = [];
    this.onChange(this.selectedOptions);
    this.onTouched();
  }
}
