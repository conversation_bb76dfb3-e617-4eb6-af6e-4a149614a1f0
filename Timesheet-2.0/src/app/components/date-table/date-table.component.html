<div class="flex">
  @if (filteredWorklogs().length > 0) {
    @for (w of filteredWorklogs(); track $index) {
      <div
        class="w-6 h-6 rounded-full mx-0.5 text-[0.6rem] flex justify-center items-center"
        [ngClass]="getCellClass(w)"
      >
        {{ getCellValue(w) }}
      </div>
    }
  } @else {
    <div
      class="w-6 h-6 rounded-full mx-0.5 text-xs flex justify-center items-center bg-slate-100"
    >
      -
    </div>
  }
</div>
