import { CommonModule } from '@angular/common';
import { Component, computed, input } from '@angular/core';
import { format } from 'date-fns';
import { LEAVE_STATUS } from '../../constants/constant';
import { DATE_FORMAT_YYYY_MM_DD } from '../../settings';

export interface WorkLog {
  workDate: string;
  minutes: number;
  attendance: string;
  isOnLeave?: boolean;
  isWeekOff?: boolean;
  isCompanyOff?: boolean;
  isPresent?: boolean;
  billingStatus?: string;
}

export interface LeaveDetail {
  date: string;
  leaveStatus: number;
}

@Component({
  selector: 'tms-date-table',
  standalone: true,
  imports: [CommonModule],
  styleUrls: ['./date-table.component.scss'],
  templateUrl: './date-table.component.html',
})
export class DateTableComponent {
  worklogs = input<WorkLog[]>([]);
  isHoursView = input<boolean>(true);
  leaveDetails = input<LeaveDetail[]>([]);
  worklogDate = input<Date | null>(new Date());

  /**
   * Computes and filters the worklogs for the selected date.
   *
   * @returns {WorkLog[]} An array of worklogs filtered for the selected date.
   */
  readonly filteredWorklogs = computed(() => {
    const selectedDate = this.worklogDate();
    if (!selectedDate) return [];

    // Convert selectedDate to YYYY-MM-DD
    const selectedDateString = format(
      new Date(selectedDate),
      DATE_FORMAT_YYYY_MM_DD
    );

    // Find all worklogs for the selected date
    const worklogsForDay = this.worklogs()
      .flat()
      .filter((entry) => {
        if (!entry?.workDate) return false;

        const entryDateString = entry.workDate.split('T')[0];
        return entryDateString === selectedDateString;
      });

    if (worklogsForDay.length === 0) return [];

    // Aggregate all worklogs for the same date
    const aggregatedLog: WorkLog = {
      workDate: worklogsForDay[0]?.workDate?.split('T')[0] ?? '',
      minutes: worklogsForDay.reduce(
        (total, entry) => total + (entry.minutes || 0),
        0
      ),
      attendance: worklogsForDay[0]?.attendance ?? '',
      isOnLeave: worklogsForDay.some((entry) => entry.isOnLeave),
      isWeekOff: worklogsForDay.some((entry) => entry.isWeekOff),
      isCompanyOff: worklogsForDay.some((entry) => entry.isCompanyOff),
      isPresent: worklogsForDay.some((entry) => entry.isPresent),
      billingStatus: worklogsForDay[0]?.billingStatus ?? 'not-billable',
    };

    return [aggregatedLog];
  });

  /**
   * Checks if a given work date corresponds to a leave day and determines the leave type.
   *
   * @param {string} workDate - The work date to check (formatted as YYYY-MM-DD).
   * @returns {'full-day' | 'half-day' | ''} The leave type: 'full-day', 'half-day', or an empty string if not on leave.
   */
  checkIsOnLeave(workDate: string): 'full-day' | 'half-day' | '' {
    const detail = this.leaveDetails().find((d) => {
      if (!d?.date) return false;
      try {
        const formatted = format(new Date(d.date), DATE_FORMAT_YYYY_MM_DD);
        return formatted === workDate;
      } catch (e) {
        console.error('Invalid date found in leaveDetails:', d.date);
        return false;
      }
    });
    if (detail?.leaveStatus === LEAVE_STATUS.FULL_DAY_LEAVE) return 'full-day';
    if (detail?.leaveStatus === LEAVE_STATUS.HALF_DAY_LEAVE) return 'half-day';
    return '';
  }

  /**
   * Determines the CSS class for a table cell based on the worklog's properties.
   *
   * @param {WorkLog} w - The worklog object for the specific day.
   * @returns {string} The CSS class for the cell.
   */
  getCellClass(w: WorkLog): string {
    const leave = this.checkIsOnLeave(w.workDate);
    if (w.isWeekOff) return 'bg-yellow-100';
    if (leave === 'full-day') return 'bg-red-200 text-black';
    if (leave === 'half-day') return 'bg-purple-200 text-black';
    if (w.isCompanyOff) return 'bg-blue-300';
    if (w.isPresent) return 'bg-green-200';
    if (this.minutesToHours(w.minutes) < 5 && w.minutes > 0)
      return 'bg-orange-300';
    return 'bg-slate-100';
  }

  /**
   * Determines the value to display in a table cell based on the worklog's properties.
   *
   * @param {WorkLog} w - The worklog object for the specific day.
   * @returns {string} The value to display in the cell (e.g., hours worked, 'L' for leave, 'H' for half-day leave).
   */
  getCellValue(w: WorkLog): string {
    const leave = this.checkIsOnLeave(w.workDate);
    if (w.minutes > 0) return this.minutesToHours(w.minutes).toString();
    if (leave === 'full-day') return 'L';
    if (leave === 'half-day') return 'H';
    return '';
  }

  minutesToHours(minutes: number): number {
    return Math.round((minutes / 60) * 100) / 100;
  }
}
