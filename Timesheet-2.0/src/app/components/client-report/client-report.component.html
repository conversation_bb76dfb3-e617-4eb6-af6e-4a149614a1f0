<div class="flex flex-col gap-4">
  <div class="flex items-center flex-wrap justify-between gap-2">
    <div class="flex items-center flex-wrap gap-3">
      <p-dropdown
        #clientDropdown
        placeholder="Select Client"
        filterPlaceholder="Search client"
        [filter]="true"
        [options]="getClientDropdownOptions()"
        [ngModel]="selectedClientId()"
        (onChange)="onClientChange($event)"
        optionLabel="label"
        optionValue="value"
        styleClass="w-40 rounded-lg"
        [resetFilterOnHide]="true"
        [loading]="allClientsQuery.isLoading()"
        [emptyMessage]="'No Client Found.'"
        (onClear)="onClientClear(); clientDropdown.hide()"
        [panelStyleClass]="'w-64'"
      >
        <ng-template let-option pTemplate="item">
          <div class="whitespace-normal text-base w-40 truncate">
            {{ option.label }}
          </div>
        </ng-template>
      </p-dropdown>
      <p-multiSelect
        #projectsDropdown
        placeholder="Select Projects"
        filterPlaceholder="Projects"
        [options]="availableProjects()"
        [ngModel]="selectedProjectsForBinding()"
        (ngModelChange)="onProjectsModelChange($event)"
        optionLabel="label"
        styleClass="w-48 rounded-lg"
        [resetFilterOnHide]="true"
        [showClear]="true"
        [loading]="allClientsQuery.isLoading()"
        [emptyMessage]="'No Project Found.'"
        (onChange)="onProjectsChange($event)"
        (onClear)="onProjectsClear(); projectsDropdown.hide()"
        [panelStyleClass]="'w-64'"
        [disabled]="availableProjects().length === 0"
        multiple="true"
      >
        <ng-template let-option pTemplate="item">
          <div class="whitespace-normal text-base">
            {{ option.label }}
          </div>
        </ng-template>
      </p-multiSelect>
      <p-calendar
        [(ngModel)]="date"
        view="month"
        dateFormat="M yy"
        inputStyleClass="outline-none shadow-none ring-0"
        (onSelect)="onDateSelect($event)"
        [maxDate]="maxDate"
        [showIcon]="true"
        class="w-36 rounded-lg"
      ></p-calendar>

      <!-- This block is intentionally commented out per request.  Do not remove without confirmation. -->
      <!-- <p-dropdown
        placeholder="Select Status"
        [options]="statusOptions"
        [(ngModel)]="selectedStatus"
        (onChange)="onStatusChange($event)"
        optionLabel="label"
        optionValue="value"
        styleClass="w-40 rounded-lg"
        [disabled]="availableProjects().length === 0"
      ></p-dropdown> -->
      <p-button
        label="Clear Filters"
        aria-label="Clear all filters"
        icon="pi pi-filter-slash"
        (click)="clearFilters()"
        outlined="true"
        class="ml-6"
      ></p-button>

      <div class="flex items-center ml-2">
        <p-checkbox
          [binary]="true"
          [ngModel]="cappedHours()"
          (onChange)="onCappedHoursChange($event.checked)"
          inputId="cappedHoursCheckbox"
        ></p-checkbox>
        <label for="cappedHoursCheckbox" class="ml-2">Capped hours</label>
        <i
          class="pi pi-info-circle text-blue-400 ml-1 cursor-pointer"
          [pTooltip]="
            'Shows worklog hours capped at a maximum of 8 hours per day when enabled'
          "
          tooltipPosition="top"
        ></i>
      </div>
    </div>
  </div>
  <div class="flex flex-col gap-4 mt-2">
    @for (project of storedProjects(); track project) {
      <tms-client-project-report
        [project]="project"
        [showCheckbox]="true"
        [cappedHours]="cappedHours()"
        [currentDate]="date()"
      ></tms-client-project-report>
    }
  </div>
</div>
