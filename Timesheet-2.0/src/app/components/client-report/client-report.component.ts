import {
  ChangeDetectionStrategy,
  Component,
  computed,
  signal,
  inject,
  viewChild,
  effect,
  input,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CalendarModule } from 'primeng/calendar';
import { MultiSelectChangeEvent, MultiSelectModule } from 'primeng/multiselect';
import { TableModule } from 'primeng/table';
import { InputTextModule } from 'primeng/inputtext';
import { ButtonModule } from 'primeng/button';
import { DropdownModule } from 'primeng/dropdown';
import { CommonModule } from '@angular/common';
import { CheckboxModule } from 'primeng/checkbox';
import { ClientService } from '../../services/client/client.service';
import {
  ClientResponse,
  ClientReportProject,
} from '../../services/client/client.model';
import { MultiSelect } from 'primeng/multiselect';
import { ClientProjectReportComponent } from '../../components/client-project-report/client-project-report.component';
import { TooltipModule } from 'primeng/tooltip';

@Component({
  selector: 'tms-client-report-page',
  templateUrl: './client-report.component.html',
  styleUrl: './client-report.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
  standalone: true,
  imports: [
    TableModule,
    CommonModule,
    DropdownModule,
    FormsModule,
    CalendarModule,
    InputTextModule,
    MultiSelectModule,
    ButtonModule,
    CheckboxModule,
    ClientProjectReportComponent,
    TooltipModule,
  ],
})

/**
 * ClientReportComponent
 *
 * This Angular standalone component provides a comprehensive report view for clients and their associated projects.
 * It allows users to:
 *   - Select a client from a dropdown (with filtering)
 *   - Filter projects associated with the selected client
 *   - Filter by project status (All, Active, Inactive)
 *   - Select a month/year for the report
 *   - Optionally toggle a "capped hours" view
 *   - Clear all filters with a single action
 *
 * The component fetches client and project data via the injected ClientService, and uses signals for reactive state management.
 * It leverages PrimeNG UI components (Table, Dropdown, MultiSelect, Calendar, etc.) for a rich user experience.
 *
 * Key Features:
 *   - Reactive state using Angular signals and computed properties
 *   - Efficient data fetching and error handling for client/project reports
 *   - Integration with PrimeNG for UI consistency
 *   - Modular, standalone design for easy reuse
 *
 * Usage:
 *   - Used as <tms-client-report-page> in templates
 *   - Expects ClientService to be provided in the application
 *   - Designed for use in dashboards or reporting pages where client/project analytics are needed
 *
 */
export class ClientReportComponent {
  private readonly clientService = inject(ClientService);

  clientDropdown = viewChild<MultiSelect>('clientDropdown');
  projectsDropdown = viewChild<MultiSelect>('projectsDropdown');

  readonly date = signal<Date>(new Date());
  readonly maxDate = new Date();
  readonly selectedClientId = signal<string | null>(null);
  readonly selectedProjectsIds = signal<string[]>([]);
  readonly selectedStatus = signal<string>('');
  readonly cappedHours = signal<boolean>(false);

  storedProjects = signal<ClientReportProject[]>([]);
  readonly clientReportProjects = signal<ClientReportProject[]>([]);

  readonly statusOptions = [
    { label: 'All', value: 'all' },
    { label: 'Active', value: 'active' },
    { label: 'Inactive', value: 'inactive' },
  ];

  readonly allClientsQuery = this.clientService.getAllClientsQuery();

  getClientDropdownOptions() {
    return (
      this.allClientsQuery.data()?.map((client: ClientResponse) => ({
        label: client.name,
        value: client.id,
        projects: client.projects || [],
      })) ?? []
    );
  }

  readonly availableProjects = computed(() => {
    return this.clientReportProjects().map((project) => ({
      label: project.projectName,
      value: project.projectId,
      ...project,
    }));
  });

  // Computed property that returns selected projects in the format expected by MultiSelect
  readonly selectedProjectsForBinding = computed(() => {
    const selectedIds = this.selectedProjectsIds();
    if (selectedIds.length === 0) return [];

    const result = this.availableProjects().filter((option) =>
      selectedIds.includes(option.value)
    );
    return result;
  });

  constructor() {}

  private async fetchClientReport(
    clientId: string | null,
    date: Date,
    status: string
  ) {
    if (clientId) {
      const month = date.getMonth() + 1;
      const year = date.getFullYear();
      try {
        const report = await this.clientService.getClientReport(
          clientId,
          month,
          year,
          status || 'all'
        );
        this.clientReportProjects.set(report.projects);
      } catch (error) {
        this.clientReportProjects.set([]);
      }
    } else {
      this.clientReportProjects.set([]);
    }
  }

  async onClientChange(event: { value: string }): Promise<void> {
    this.selectedClientId.set(event.value);
    this.selectedProjectsIds.set([]);
    this.storedProjects.set([]);
    this.clientReportProjects.set([]);
    await this.fetchClientReport(
      event.value,
      this.date(),
      this.selectedStatus()
    );
  }

  onProjectsChange(event: MultiSelectChangeEvent): void {
    const selectedProjects = event.value as ClientReportProject[];
    this.selectedProjectsIds.set(
      selectedProjects.map((project) => project.projectId)
    );
    this.storedProjects.set(selectedProjects);
  }

  onProjectsModelChange(value: any[]): void {
    // Handle the case where value might be the option objects or just the project objects
    const selectedProjects = value.map((item) => {
      // If it's an option object (has label, value), extract the project data
      if (item.label && item.value) {
        const { label, value, ...projectData } = item;
        return projectData as ClientReportProject;
      }
      // If it's already a project object, use it directly
      return item as ClientReportProject;
    });

    this.selectedProjectsIds.set(
      selectedProjects.map((project) => project.projectId)
    );
    this.storedProjects.set(selectedProjects);
  }

  // Helper method to get selected projects from current available projects
  private getSelectedProjectsFromAvailable(): ClientReportProject[] {
    const selectedIds = this.selectedProjectsIds();
    if (selectedIds.length === 0) return [];

    return this.clientReportProjects().filter((project) =>
      selectedIds.includes(project.projectId)
    );
  }

  onStatusChange(event: { value: string }) {
    this.selectedStatus.set(event.value);

    // Store the currently selected project IDs before fetching new data
    const currentSelectedProjectIds = this.selectedProjectsIds();

    // Fetch new data
    this.fetchClientReport(
      this.selectedClientId(),
      this.date(),
      event.value
    ).then(() => {
      // After fetching new data, restore the selected projects if they still exist
      if (currentSelectedProjectIds.length > 0) {
        const availableProjectIds = this.clientReportProjects().map(
          (p) => p.projectId
        );
        const validSelectedIds = currentSelectedProjectIds.filter((id) =>
          availableProjectIds.includes(id)
        );

        if (validSelectedIds.length > 0) {
          // Update the selected IDs and reconstruct stored projects from new data
          this.selectedProjectsIds.set(validSelectedIds);
          this.storedProjects.set(this.getSelectedProjectsFromAvailable());
        } else {
          // If none of the previously selected projects are available in the new data, clear selections
          this.storedProjects.set([]);
          this.selectedProjectsIds.set([]);
        }
      }
    });
  }

  onDateSelect(selectedDate: Date): void {
    this.date.set(selectedDate);

    // Store the currently selected project IDs before fetching new data
    const currentSelectedProjectIds = this.selectedProjectsIds();

    // Fetch new data
    this.fetchClientReport(
      this.selectedClientId(),
      selectedDate,
      this.selectedStatus()
    ).then(() => {

      // After fetching new data, restore the selected projects if they still exist
      if (currentSelectedProjectIds.length > 0) {
        const availableProjectIds = this.clientReportProjects().map(
          (p) => p.projectId
        );
        const validSelectedIds = currentSelectedProjectIds.filter((id) =>
          availableProjectIds.includes(id)
        );

        if (validSelectedIds.length > 0) {
          // Update the selected IDs and reconstruct stored projects from new data
          this.selectedProjectsIds.set(validSelectedIds);
          this.storedProjects.set(this.getSelectedProjectsFromAvailable());
        } else {
          // If none of the previously selected projects are available in the new data, clear selections
          this.storedProjects.set([]);
          this.selectedProjectsIds.set([]);
        }
      }
    });
  }
  clearFilters() {
    this.selectedClientId.set(null);
    this.selectedProjectsIds.set([]);
    this.selectedStatus.set('');
    this.cappedHours.set(false);
    const defaultDate = new Date(
      new Date().getFullYear(),
      new Date().getMonth()
    );
    this.date.set(defaultDate);
    this.storedProjects.set([]);
    this.clientReportProjects.set([]); // Clear projects to disable dropdown
    this.clientDropdown()?.resetFilter();
    this.projectsDropdown()?.resetFilter();
  }
  onCappedHoursChange(checked: boolean) {
    this.cappedHours.set(checked);
  }
  onClientClear() {
    this.selectedClientId.set(null);
    this.selectedProjectsIds.set([]);
    this.storedProjects.set([]);
    this.clientReportProjects.set([]); // Clear projects to disable dropdown
  }
  onProjectsClear() {
    this.selectedProjectsIds.set([]);
    this.storedProjects.set([]);
  }
}
