import { HttpClient, HttpHandler } from '@angular/common/http';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { QueryClient } from '@tanstack/angular-query-experimental';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { ContractById } from '../../services/contract/contract.model';
import { Resource } from '../contract-form/contract-form.component';
import { AddResourceFormComponent } from './add-resource-form.component';

const contractData: ContractById = {
  id: '94ee0244-b3a3-4d91-9545-4161d7bd645f',
  startDate: '2025-02-03T18:30:00.000Z',
  endDate: '2025-02-27T18:30:00.000Z',
  customContractId: 'CB-003',
  contractStatus: 'active',
  renewedFrom: null,
  project: {
    id: '4561be37-ad67-4ecd-812c-7d0914b351a6',
    projectName: 'AI - <PERSON><PERSON>',
    contactName: 'Akshay',
    contactEmail: '<EMAIL>',
    contactPhoneNumber: '+917894444444',
    contractResource: [
      {
        contractResource: {
          id: '8315a2e0-0b54-40e3-8913-13da2c3f727f',
          name: 'Akshay Bhat',
          role: 'admin',
          email: '<EMAIL>',
          phoneNumber: '8277492619',
          profilePicUrl: null,
        },
      },
    ],
  },
  contractResource: [
    {
      projectId: '4561be37-ad67-4ecd-812c-7d0914b351a6',
      contractId: '94ee0244-b3a3-4d91-9545-4161d7bd645f',
      isActive: true,
      contractResource: {
        id: '8315a2e0-0b54-40e3-8913-13da2c3f727f',
        name: 'Akshay Bhat',
        designation: 'Software Engineer',
        kekaId: 'cc539',
        profilePicUrl: null,
        resourceContractBillingRate: [],
      },
    },
  ],
  contractComments: [],
  contractBudgetDetail: [
    {
      id: 'a61d68fb-0653-4344-924a-db3d8120dc6c',
      isBillable: false,
      billingSettings: null,
      amount: 0,
      contractId: '94ee0244-b3a3-4d91-9545-4161d7bd645f',
    },
  ],
  contractManagers: [
    {
      id: '8315a2e0-0b54-40e3-8913-13da2c3f727f',
      name: 'Akshay Bhat',
      email: '<EMAIL>',
      kekaId: 'cc539',
      profilePicUrl: null,
    },
  ],
};

const resource: Resource & {
  kekaId: string;
  designation?: string | null;
} = {
  id: '456',
  name: 'Jane Smith',
  profilePicUrl: 'url2',
  kekaId: 'cc-001',
  designation: 'Software Engineer',
};

describe('AddResourceFormComponent', () => {
  let component: AddResourceFormComponent;
  let fixture: ComponentFixture<AddResourceFormComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [AddResourceFormComponent],
      providers: [
        HttpClient,
        HttpHandler,
        {
          provide: DynamicDialogConfig,
          useValue: { data: { contract: contractData } },
        },
        DynamicDialogRef,
        QueryClient,
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(AddResourceFormComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize form with contract resources if in edit mode', () => {
    expect(component.isEditMode()).toBeTrue();
    expect(
      component.addContractResourcesFormGroup.get('contractResources')?.value
    ).toEqual(component.mapContractResources());
  });

  it('should validate form requires at least one resource', () => {
    expect(component.isEditMode()).toBeTrue();
    const contractResourcesControl =
      component.addContractResourcesFormGroup.get('contractResources');
    contractResourcesControl?.setValue([]);
    component.addContractResourcesFormGroup.updateValueAndValidity();
    expect(contractResourcesControl?.errors?.['required']).toBeTruthy();
  });

  it('should remove selected resource from form', () => {
    const contractResourcesControl =
      component.addContractResourcesFormGroup.get('contractResources');
    contractResourcesControl?.setValue([resource]);

    const mockEvent = new Event('click');
    spyOn(mockEvent, 'stopPropagation');

    component.removeSelectedItem(resource, mockEvent);

    expect(mockEvent.stopPropagation).toHaveBeenCalled();
    expect(contractResourcesControl?.value).toEqual([]);
  });

  it('should not submit form if invalid', () => {
    component.addContractResourcesFormGroup
      .get('contractResources')
      ?.setValue([]);
    spyOn(component.addContractResourcesFormGroup, 'markAllAsTouched');

    component.onSubmit();

    expect(
      component.addContractResourcesFormGroup.markAllAsTouched
    ).toHaveBeenCalled();
    expect(component.isSubmitting()).toBeFalse();
  });
});
