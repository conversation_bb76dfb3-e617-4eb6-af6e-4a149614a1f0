<form
  [formGroup]="addContractResourcesFormGroup"
  (ngSubmit)="onSubmit()"
  class="flex flex-col w-full gap-4"
>
  <div class="flex flex-col h-[60vh] mt-2">
    <p-multiSelect
      formControlName="contractResources"
      [options]="resourcesQuery().data()"
      optionLabel="name"
      id="contractResources"
      placeholder="Select Employees"
      [filter]="true"
      filterBy="name,kekaId"
      [showClear]="true"
      [style]="{ width: '100%' }"
      [autofocus]="true"
      [showClear]="false"
      [resetFilterOnHide]="true"
      scrollHeight="250px"
    >
      <!-- Selected Items Template -->
      <ng-template let-value pTemplate="selectedItems" class="ml-16">
        <div class="flex items-center justify-center w-max">
          @for (option of value; track $index) {
            <div class="inline-flex align-items-center gap-2 px-1">
              <div
                class="flex items-center justify-center space-x-2 bg-gray-50 rounded-full px-2 py-0.5"
              >
                @if (option.profilePicUrl) {
                  <p-avatar [image]="option.profilePicUrl" shape="circle" />
                } @else {
                  <p-avatar [label]="option.name.charAt(0)" shape="circle" />
                }
                <div>{{ option.name }}</div>
                <i
                  class="pi pi-times cursor-pointer hover:bg-gray-200 p-2 rounded-full transition-colors"
                  (click)="removeSelectedItem(option, $event)"
                  aria-hidden="true"
                ></i>
              </div>
            </div>
          }
        </div>

        @if (!value || value.length === 0) {
          <span>Select Resources</span>
        }
      </ng-template>

      <!-- Template for dropdown options -->
      <ng-template pTemplate="item" let-resource>
        <div class="flex items-center space-x-1">
          @if (resource.profilePicUrl) {
            <p-avatar [image]="resource.profilePicUrl" shape="circle" />
          } @else {
            <p-avatar [label]="resource.name.charAt(0)" shape="circle" />
          }
          <div>{{ resource.name + ' ' + '(' + resource.kekaId + ')' }}</div>
        </div>
      </ng-template>
    </p-multiSelect>

    <div class="bg-gray-100 p-4 rounded-lg h-full overflow-y-auto mt-4">
      <div class="grid grid-cols-1 md:grid-cols-1 lg:grid-cols-2 gap-3">
        @if (
          addContractResourcesFormGroup.get('contractResources')?.value?.length
        ) {
          @for (
            resource of addContractResourcesFormGroup.get('contractResources')
              ?.value;
            track $index
          ) {
            <div
              class="bg-white p-4 rounded-lg shadow-md flex items-center space-x-4 w-full"
            >
              @if (resource.profilePicUrl) {
                <p-avatar [image]="resource.profilePicUrl" shape="circle" />
              } @else {
                <p-avatar [label]="resource.name.charAt(0)" shape="circle" />
              }
              <div class="flex flex-col gap-1 w-full">
                <span
                  class="font-semibold text-sm text-neutral-500 line-clamp-1"
                >
                  {{ resource.name }}
                </span>
                <span class="text-sm line-clamp-1">
                  {{ resource.designation ?? 'Designation: N/A' }}
                </span>
                <span class="text-sm">
                  {{ resource.kekaId }}
                </span>
              </div>
            </div>
          }
        } @else {
          <div class="col-span-2 text-center text-gray-500 place-self-center">
            Selected resources will appear here
          </div>
        }
      </div>
    </div>
  </div>
  <div class="flex justify-end mt-4">
    <p-button
      type="submit"
      [label]="
        formSubmitButtonLabelService.getFormSubmitButtonLabel(
          isSubmitting(),
          isEditMode()
        )
      "
      [disabled]="!addContractResourcesFormGroup.valid || isSubmitting()"
      styleClass="p-button-primary"
      [loading]="isSubmitting()"
    ></p-button>
  </div>
</form>
