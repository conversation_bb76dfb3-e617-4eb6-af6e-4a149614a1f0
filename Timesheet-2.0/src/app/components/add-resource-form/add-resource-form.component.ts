import { Component, computed, signal } from '@angular/core';
import {
  <PERSON><PERSON><PERSON>er,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { MultiSelectModule } from 'primeng/multiselect';
import { ResourceService } from '../../services/resource/resource.service';
import { DynamicDialogConfig, DynamicDialogRef } from 'primeng/dynamicdialog';
import { AvatarModule } from 'primeng/avatar';
import { ButtonModule } from 'primeng/button';
import { injectMutation } from '@tanstack/angular-query-experimental';
import { AuthService } from '../../services/auth/auth-service';
import { ContractService } from '../../services/contract/contract.service';
import {
  ContractById,
  UpdateContract,
} from '../../services/contract/contract.model';
import { FormSubmitButtonLabelService } from '../../services/formsubmitbuttonlabel/formsubmitbuttonlabel.service';
import { Resource } from '../contract-form/contract-form.component';
import { TooltipModule } from 'primeng/tooltip';

interface AddResourcesFormData {
  contractResources: (Resource & {
    kekaId: string;
    designation: string | null;
  })[];
  assignedBy: string;
}

@Component({
  selector: 'tms-add-resource-form',
  standalone: true,
  imports: [
    MultiSelectModule,
    FormsModule,
    ReactiveFormsModule,
    AvatarModule,
    ButtonModule,
    TooltipModule,
  ],
  templateUrl: './add-resource-form.component.html',
})
export class AddResourceFormComponent {
  addContractResourcesFormGroup: FormGroup;
  contract: ContractById;
  isSubmitting = signal<boolean>(false);
  isEditMode = signal<boolean>(false);
  resourcesQuery = computed(() => this.resourceService.resourcesQuery);
  contractsQuery = computed(() =>
    this.contractService.contractsQuery(this.contract.id)
  );

  constructor(
    private resourceService: ResourceService,
    private formBuilder: FormBuilder,
    private dialogConfig: DynamicDialogConfig,
    private dialogRef: DynamicDialogRef,
    private authService: AuthService,
    private contractService: ContractService,
    public formSubmitButtonLabelService: FormSubmitButtonLabelService
  ) {
    this.contract = this.dialogConfig.data?.contract || [];
    this.isEditMode.set(!!this.contract.contractResource.length);
    this.addContractResourcesFormGroup = this.initializeForm();
  }

  private initializeForm() {
    const contractResources = this.mapContractResources();
    return this.formBuilder.group({
      contractResources: [contractResources, [Validators.required]],
    });
  }
  mapContractResources(): Resource[] {
    return (
      this.contract.contractResource?.map((resource) => ({
        id: resource.contractResource.id,
        name: resource.contractResource.name,
        profilePicUrl: resource.contractResource.profilePicUrl,
        designation: resource.contractResource.designation,
        kekaId: resource.contractResource.kekaId.toUpperCase(),
      })) ?? []
    );
  }

  removeSelectedItem(manager: Resource, event: Event): void {
    event.stopPropagation();
    const contractResourcesControl =
      this.addContractResourcesFormGroup.get('contractResources');

    if (!contractResourcesControl) return;

    const currentValue = contractResourcesControl.value ?? [];
    const updatedValue = currentValue.filter(
      (item: Resource) => item.id !== manager.id
    );

    contractResourcesControl.setValue(updatedValue);
  }

  onSubmit() {
    if (this.addContractResourcesFormGroup.invalid) {
      this.addContractResourcesFormGroup.markAllAsTouched();
      return;
    }
    this.isSubmitting.set(true);
    const formValue: AddResourcesFormData =
      this.addContractResourcesFormGroup.value;

    const contractResources: UpdateContract = {
      resourceIds: formValue.contractResources.map((resource) => resource.id),
      assignedBy: this.authService.userId(),
      contractManagerIds: this.contract.contractManagers?.map(
        (manager) => manager.id
      ),
    };

    this.updateContractResourceQuery.mutate(contractResources);
    this.addContractResourcesFormGroup.disable();
  }

  /**
   * Mutation hook for create a new contract.
   * It triggers a mutation to create a new contract and handles success/error responses.
   */
  updateContractResourceQuery = injectMutation(() => ({
    mutationKey: ['update-contract-resource', this.authService.userId()],
    mutationFn: (contractData: UpdateContract) =>
      this.contractService.updateContract(contractData, this.contract?.id),
    onSuccess: (data) => {
      this.contractsQuery().refetch();
      this.dialogRef.close({
        type: 'success',
        severity: data.status,
        message: this.isEditMode()
          ? 'Resource updated successfully'
          : 'Resource added successfully',
        summary: 'Contract updated.',
      });
    },
    onError: (error) => {
      console.error('Error updating resource:', error);
      this.dialogRef.close({
        type: 'error',
        severity: 'error',
        message: this.isEditMode()
          ? 'Failed to update the resource'
          : 'Failed add the resource',
        summary: 'Error',
      });
    },
  }));
}
