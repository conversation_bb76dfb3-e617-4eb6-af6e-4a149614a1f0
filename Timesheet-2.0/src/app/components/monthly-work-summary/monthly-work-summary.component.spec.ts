import { ComponentFixture, TestBed } from '@angular/core/testing';

import { MonthlyWorkSummaryComponent } from './monthly-work-summary.component';
import { By } from '@angular/platform-browser';

describe('MonthlyWorkSummaryComponent', () => {
  let component: MonthlyWorkSummaryComponent;
  let fixture: ComponentFixture<MonthlyWorkSummaryComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [MonthlyWorkSummaryComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(MonthlyWorkSummaryComponent);
    component = fixture.componentInstance;
    fixture.componentRef.setInput('totalWorkedHoursOfMonth', '');
    fixture.componentRef.setInput('totalWorkedDaysOfMonth', 0);
    fixture.componentRef.setInput('averageWorkedHours', '');
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should display correct total worked hours', () => {
    fixture.componentRef.setInput('totalWorkedHoursOfMonth', '160');
    fixture.detectChanges();
    expect(component.totalWorkedHoursOfMonth()).toBe('160');
  });

  it('should display correct total worked days', () => {
    fixture.componentRef.setInput('totalWorkedDaysOfMonth', 20);
    fixture.detectChanges();
    expect(component.totalWorkedDaysOfMonth()).toBe(20);
  });

  it('should display correct average worked hours', () => {
    fixture.componentRef.setInput('averageWorkedHours', '8');
    fixture.detectChanges();
    expect(component.averageWorkedHours()).toBe('8');
  });
});
