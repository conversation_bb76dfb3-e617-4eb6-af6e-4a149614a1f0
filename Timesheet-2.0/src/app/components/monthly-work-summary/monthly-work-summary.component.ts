import { Component, input, ViewEncapsulation } from '@angular/core';

@Component({
  selector: 'tms-monthly-work-summary',
  standalone: true,
  imports: [],
  templateUrl: './monthly-work-summary.component.html',
  styleUrl: './monthly-work-summary.component.css',
  encapsulation: ViewEncapsulation.None,
})
export class MonthlyWorkSummaryComponent {
  totalWorkedHoursOfMonth = input.required<string | 0>();
  totalWorkedDaysOfMonth = input.required<number>();
  averageWorkedHours = input.required<string | 0>();
}
