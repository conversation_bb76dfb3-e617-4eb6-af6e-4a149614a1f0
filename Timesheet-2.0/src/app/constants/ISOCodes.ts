import { Option } from '../services/common.model';

export const acceptedIsoCodes: Option[] = [
  {
    label: '+91 India',
    value: '+91',
  },
  {
    label: '+1 United States',
    value: '+1',
  },
  {
    label: '+1 Canada',
    value: '+1',
  },
  {
    label: '+44 United Kingdom',
    value: '+44',
  },
];

export const currencyLocaleCodes: Option[] = [
  {
    label: 'INR',
    value: 'en-IN',
  },
  {
    label: 'USD',
    value: 'en-US',
  },
  {
    label: 'EUR',
    value: 'en-IE',
  },
];
