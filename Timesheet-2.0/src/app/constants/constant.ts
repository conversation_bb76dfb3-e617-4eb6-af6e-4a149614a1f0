import { Option } from '../services/common.model';

export const COMPANY_NAME = 'CodeCraft Technologies';

export const LOCATION_LIST: Option[] = [
  { label: 'Mangalore', value: 'mangalore' },
  { label: 'Bangalore', value: 'bangalore' },
  { label: 'Toronto', value: 'toronto' },
  { label: 'US', value: 'us' },
];

export const TIMELINE_MIN_VISIBLE = 4;

export const COMMENTS_MIN_VISIBLE = 4;

export const LEAVE_STATUS = {
  FULL_DAY_LEAVE: 2,
  HALF_DAY_LEAVE: 1,
} as const;

export const DAY_OFF_TYPE = {
  WEEK_OFF: 'weekOff',
  HOLIDAY: 'holiday',
  FULL_DAY_LEAVE: 'fullDayLeave',
  HALF_DAY_LEAVE: 'halfDayLeave',
} as const;

export const WORKLOG_STATUS = {
  MISSING: 'missing',
  SUBMITTED: 'submitted',
  APPROVED: 'approved',
  REVISED: 'revised',
  REJECTED: 'rejected',
} as const;

export const MILLISECONDS_PER_WEEK = 7 * 24 * 60 * 60 * 1000;

export const COUNTRIES: Option[] = [
  { label: 'India', value: 'India' },
  { label: 'United States', value: 'United States' },
  { label: 'United Kingdom', value: 'United Kingdom' },
  { label: 'Canada', value: 'Canada' },
  { label: 'Australia', value: 'Australia' },
  { label: 'Germany', value: 'Germany' },
  { label: 'Japan', value: 'Japan' },
] as const;

export const WORKLOG_LOCATION: Option[] = [
  { label: 'CodeCraft Office', value: 'codecraftOffice' },
  { label: 'Client Office', value: 'clientOffice' },
  { label: 'Remote', value: 'remoteHybrid' },
] as const;

export const TECHNOLOGY_LIST: string[] = [
  'HTML5',
  'CSS3',
  'JavaScript',
  'TypeScript',
  'React.js',
  'Angular',
  'Vue.js',
  'Svelte',
  'Next.js',
  'Nuxt.js',
  'Bootstrap',
  'Tailwind CSS',
  'Material-UI',
  'Ant Design',
  'Chakra UI',
  'Redux',
  'MobX',
  'Zustand',
  'Vuex',
  'Webpack',
  'Vite',
  'Parcel',
  'Node.js',
  'Python',
  'Java',
  'Go',
  'PHP',
  'Ruby',
  'C#',
  'Rust',
  'Express.js',
  'Django',
  'Flask',
  'Spring Boot',
  'ASP.NET Core',
  'Laravel',
  'Ruby on Rails',
  'FastAPI',
  'NestJS',
  'REST',
  'GraphQL',
  'gRPC',
  'WebSockets',
  'PostgreSQL',
  'MySQL',
  'SQL Server',
  'Oracle DB',
  'MongoDB',
  'Firebase Realtime DB',
  'Couchbase',
  'Cassandra',
  'DynamoDB',
  'Redis',
  'GitHub Actions',
  'GitLab CI/CD',
  'Jenkins',
  'CircleCI',
  'Travis CI',
  'AWS',
  'Azure',
  'GCP',
  'DigitalOcean',
  'Heroku',
  'Vercel',
  'Netlify',
  'Docker',
  'Kubernetes',
  'Helm',
  'Terraform',
  'CloudFormation',
  'Git',
  'GitHub',
  'GitLab',
  'Bitbucket',
  'Jira',
  'Trello',
  'Asana',
  'ClickUp',
  'Monday.com',
  'Jest',
  'Mocha/Chai',
  'NUnit',
  'JUnit',
  'PyTest',
  'Cypress',
  'Selenium',
  'Playwright',
  'TestCafe',
  'Swift',
  'Kotlin',
  'Flutter',
  'React Native',
  'Ionic',
  'Xamarin',
  'Monolith',
  'Microservices',
  'Serverless',
  'Event-driven',
  'Layered Architecture',
  'OAuth2',
  'JWT',
  'SAML',
  'Firebase Auth',
  'Auth0',
  'Okta',
  'Sentry',
  'Datadog',
  'Prometheus',
  'Grafana',
  'Google Analytics',
  'New Relic',
  'Kafka',
  'RabbitMQ',
  'MQTT',
  'Amazon SNS/SQS',
  'WordPress',
  'Drupal',
  'Strapi',
  'Contentful',
  'Magento',
  'Shopify',
];
