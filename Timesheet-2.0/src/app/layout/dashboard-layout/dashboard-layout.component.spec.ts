import { ComponentFixture, TestBed } from '@angular/core/testing';
import { DashboardLayoutComponent } from './dashboard-layout.component';
import { SidebarComponent } from '../../components/sidebar/sidebar.component';
import { NavBarComponent } from '../../components/nav-bar/nav-bar.component';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { BrowserModule, By } from '@angular/platform-browser';
import { RouterTestingModule } from '@angular/router/testing';
import { HttpClientModule } from '@angular/common/http';
import { QueryClient } from '@tanstack/angular-query-experimental';

/**
 * Test suite for Dashboard
 */
describe('DashboardLayoutComponent', () => {
  let component: DashboardLayoutComponent;
  let fixture: ComponentFixture<DashboardLayoutComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        DashboardLayoutComponent,
        SidebarComponent,
        NavBarComponent,
        BrowserAnimationsModule,
        BrowserModule,
        RouterTestingModule,
        HttpClientModule,
      ],
      providers: [QueryClient],
    }).compileComponents();

    fixture = TestBed.createComponent(DashboardLayoutComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  /**
   * Test case: Verifies the component is created successfully.
   */
  it('should create', () => {
    expect(component).toBeTruthy();
  });

  /**
   * Test case: Verifies the presence of the <tms-sidebar> component.
   */
  it('should render the tms-sidebar component', () => {
    const sidebarElement = fixture.debugElement.query(By.css('tms-sidebar'));
    expect(sidebarElement).toBeTruthy();
  });

  /**
   * Test case: Verifies the <router-outlet> is present for content rendering.
   */
  it('should have a router-outlet for dynamic content', () => {
    const routerOutletElement = fixture.debugElement.query(
      By.css('router-outlet')
    );
    expect(routerOutletElement).toBeTruthy();
  });

  /**
   * Test case: Verifies the layout structure and classes.
   */
  it('should have correct layout structure and styles', () => {
    const rootElement = fixture.debugElement.query(By.css('.flex.h-screen'));
    const sidebarElement = rootElement.query(By.css('tms-sidebar'));
    const mainContentElement = rootElement.query(
      By.css('.w-full.py-8.px-12.overflow-y-hidden')
    );

    expect(rootElement).toBeTruthy();
    expect(sidebarElement).toBeTruthy();
    expect(mainContentElement).toBeTruthy();
  });
});
