import { Component, computed, signal, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, RouterModule, RouterOutlet } from '@angular/router';
import { MenuItem } from 'primeng/api';
import { BreadcrumbModule } from 'primeng/breadcrumb';
import { ButtonModule } from 'primeng/button';
import { SidebarComponent } from '../../components/sidebar/sidebar.component';
import { BreadcrumbService } from '../../services/breadcrumb-service/breadcrumb-service.service';
import { CommonModule } from '@angular/common';
import { AuthService } from '../../services/auth/auth-service';
/**
 * DashboardLayoutComponent
 *
 * This component serves as the primary layout for the dashboard section of the application.
 * It organizes the application into a sidebar for navigation, a top navigation bar,
 * and a central outlet for displaying routed views.
 */
@Component({
  selector: 'tms-dashboard-layout',
  standalone: true,
  imports: [
    SidebarComponent,
    RouterOutlet,
    BreadcrumbModule,
    ButtonModule,
    CommonModule,
    RouterModule,
  ],
  templateUrl: './dashboard-layout.component.html',
  styleUrl: './dashboard-layout.component.css',
  encapsulation: ViewEncapsulation.None,
})
export class DashboardLayoutComponent {
  role = computed(() => this.authService.userRole());
  /**
   * Menu items to be displayed in the sidebar.
   */
  menuItems: MenuItem[] = [];

  breadcrumbs = signal<MenuItem[]>([]);
  home: MenuItem = { icon: 'pi pi-home text-lg', routerLink: '/' };

  constructor(
    private breadcrumbService: BreadcrumbService,
    private route: ActivatedRoute,
    private authService: AuthService
  ) { }

  ngOnInit() {
    this.breadcrumbService.initialize(this.route);
    this.breadcrumbService.breadcrumbs$.subscribe((breadcrumbs) => {
      this.breadcrumbs.set(breadcrumbs);
    });
    this.setMenuItemsBasedOnRole();
  }

  private setMenuItemsBasedOnRole() {
    const role = this.role();

    const baseMenuItems = [];
    switch (role) {
      case 'admin':
        baseMenuItems.push(
          { label: 'Project Overview', icon: 'pi pi-file', url: '/dashboard/project-overview' },
          { label: 'Tracker', icon: 'pi pi-stopwatch', url: '/dashboard/tracker' },
          { label: 'Projects', icon: 'pi pi-briefcase', url: '/dashboard/projects' },
          { label: 'Logsheets', icon: 'pi pi-book', url: '/dashboard/logsheets' },
          { label: 'Leaves', icon: 'pi pi-calendar-minus', url: '/dashboard/leaves' },
          { label: 'Reports', icon: 'pi pi-chart-bar', url: '/dashboard/reports' },
          { label: 'Clients', icon: 'pi pi-id-card', url: '/dashboard/clients' },
          { label: 'Teams', icon: 'pi pi-users', url: '/dashboard/teams' }
        );
        break;
      case 'manager':
        baseMenuItems.push(
          { label: 'Project Overview', icon: 'pi pi-file', url: '/dashboard/project-overview' },
          { label: 'Tracker', icon: 'pi pi-stopwatch', url: '/dashboard/tracker' },
          { label: 'Projects', icon: 'pi pi-briefcase', url: '/dashboard/projects' },
          { label: 'Logsheets', icon: 'pi pi-book', url: '/dashboard/logsheets' },
          { label: 'Leaves', icon: 'pi pi-calendar-minus', url: '/dashboard/leaves' },
          { label: 'Reports', icon: 'pi pi-chart-bar', url: '/dashboard/reports' },
          { label: 'Teams', icon: 'pi pi-users', url: '/dashboard/teams' }
        );
        break;
      case 'finance':
        baseMenuItems.push(
          { label: 'Tracker', icon: 'pi pi-stopwatch', url: '/dashboard/tracker' },
          { label: 'Projects', icon: 'pi pi-briefcase', url: '/dashboard/projects' },
          { label: 'Reports', icon: 'pi pi-chart-bar', url: '/dashboard/reports' }
        );
        break;
      default:
        baseMenuItems.push(
          { label: 'Tracker', icon: 'pi pi-stopwatch', url: '/dashboard/tracker' },
          { label: 'Projects', icon: 'pi pi-briefcase', url: '/dashboard/projects' }
        );
    }

    this.menuItems = [
      {
        items: baseMenuItems,
      },
    ];
  }
}
