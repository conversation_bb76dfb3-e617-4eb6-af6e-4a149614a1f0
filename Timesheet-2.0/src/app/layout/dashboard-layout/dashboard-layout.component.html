<!-- 
  Dashboard component that renders sidebar which is used to navigate to a particular page, nav-bar to check the notifications and profile and the router 
  outlet for displaying the routed views
-->

<div class="flex h-screen bg-neutral-50">
  <tms-sidebar #sidebar [menuItems]="menuItems"></tms-sidebar>
  <div class="w-full py-6 px-12 overflow-y-hidden">
    @if (breadcrumbs().length) {
      <div class="flex gap-4 items-center justify-start mb-8 mt-2">
        <p-breadcrumb class="max-w-full" [model]="breadcrumbs()" [home]="home">
          <ng-template pTemplate="item" let-item>
            @if (item.isLast) {
              <div
                class="flex gap-2 justify-center items-center text-primary-500 font-semibold"
              >
                @if (item.icon) {
                  <i [class]="item.icon"></i>
                }
                <span>{{ item.label }}</span>
              </div>
            } @else {
              <a [routerLink]="item.route" class="p-menuitem-link">
                <div
                  class="flex gap-2 justify-center items-center text-neutral-500"
                >
                  @if (item.icon) {
                    <i [class]="item.icon"></i>
                  }
                  <span>{{ item.label }}</span>
                </div>
              </a>
            }
          </ng-template>
        </p-breadcrumb>
      </div>
    }
    <router-outlet></router-outlet>
  </div>
  <!--Button to open the sidebar-->
  @if (sidebar.isSidebarVisible() === false) {
    <p-button
      icon="pi pi-bars"
      (onClick)="sidebar.toggleSidebar()"
      styleClass="py-2 text-neutral-600 sidebar-button absolute left-0 top-[6%] transform -translate-y-1/2 bg-primary-500 text-white hover:bg-primary-600"
      [text]="true"
      [style]="{
        borderTopRightRadius: '0.75rem',
        borderTopLeftRadius: '0',
        borderBottomLeftRadius: '0',
        borderBottomRightRadius: '0.75rem',
        zIndex: '10',
      }"
      size="large"
    ></p-button>
  }
</div>
