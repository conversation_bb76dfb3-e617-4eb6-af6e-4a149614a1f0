import { provideHttpClient, withFetch } from '@angular/common/http';
import {
  ComponentFixture,
  DeferBlockState,
  TestBed,
} from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import {
  QueryClient,
  provideTanStackQuery,
} from '@tanstack/angular-query-experimental';
import { TrackerPageComponent } from './trackerpage.component';
import { DialogService } from 'primeng/dynamicdialog';
import { DynamicDialogModule } from 'primeng/dynamicdialog';

/**
 * Test suite for Tracker Page
 */
describe('TrackerpageComponent', () => {
  let component: TrackerPageComponent;
  let fixture: ComponentFixture<TrackerPageComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TrackerPageComponent, DynamicDialogModule],
      providers: [
        provideHttpClient(withFetch()),
        provideTanStackQuery(new QueryClient()),
        DialogService,
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(TrackerPageComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  /**
   * Test case: Verifies the component is created successfully.
   */
  it('should create', () => {
    expect(component).toBeTruthy();
  });

  /**
   * Test case: Verifies that the <tms-calendar> component is rendered.
   */
  it('should render the tms-calendar component', async () => {
    await fixture.whenStable();
    const deferBlockFixture = (await fixture.getDeferBlocks())[0];
    await deferBlockFixture.render(DeferBlockState.Complete);
    const calendarElement = fixture.debugElement.query(By.css('tms-calendar'));
    expect(calendarElement).toBeTruthy();
  });
});
