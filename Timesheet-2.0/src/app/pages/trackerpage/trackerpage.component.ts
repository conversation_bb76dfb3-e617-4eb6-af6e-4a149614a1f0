import { CommonModule } from '@angular/common';
import { Component, computed, signal } from '@angular/core';
import { injectQuery } from '@tanstack/angular-query-experimental';
import {
  format,
  isFuture,
  isPast,
  isSameMonth,
  isToday,
  isWeekend,
} from 'date-fns';
import { MessageService, SelectItemGroup } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { DialogService } from 'primeng/dynamicdialog';
import { MultiSelectChangeEvent, MultiSelectModule } from 'primeng/multiselect';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { SkeletonModule } from 'primeng/skeleton';
import { ToastModule } from 'primeng/toast';
import { TooltipModule } from 'primeng/tooltip';
import { CalendarComponent } from '../../components/calendar/calendar.component';
import { MonthlyWorkSummaryComponent } from '../../components/monthly-work-summary/monthly-work-summary.component';
import { ProgressChartComponent } from '../../components/progress-chart/progress-chart.component';
import { WorklogDisplayComponent } from '../../components/worklog-display/worklog-display.component';
import { WorklogFormComponent } from '../../components/worklog-form/worklog-form.component';
import { AuthService } from '../../services/auth/auth-service';
import { ProjectsService } from '../../services/project/project.service';
import { Worklog } from '../../services/worklog/worklog.model';
import { WorkLogService } from '../../services/worklog/worklog.service';
import {
  DATE_FORMAT_YYYY_MM_DD,
  EXPECTED_WORK_DAYS_PER_MONTH,
  FULL_DAY_MINUTES,
  HALF_DAY_MINUTES,
} from '../../settings';
import {
  DAY_OFF_TYPE,
  LEAVE_STATUS,
  WORKLOG_STATUS,
} from '../../constants/constant';

/** Interface for selected filter options */
interface SelectedFilterOptions {
  group: 'worklogStatus' | 'attendance' | 'projects';
  selectedOptions: string[];
}

/**
 * TrackerPageComponent
 *
 * This component serves as the main container for the Tracker Page. It is a standalone component
 * and integrates the `CalendarComponent` to display a calendar view. The component structure
 * can be expanded with additional features and components as required for the tracker functionality.
 */
@Component({
  selector: 'tms-trackerpage',
  standalone: true,
  imports: [
    ToastModule,
    CalendarComponent,
    MonthlyWorkSummaryComponent,
    OverlayPanelModule,
    MultiSelectModule,
    ButtonModule,
    CommonModule,
    WorklogDisplayComponent,
    TooltipModule,
    ProgressChartComponent,
    SkeletonModule,
  ],
  providers: [MessageService],
  templateUrl: './trackerpage.component.html',
  styleUrl: './trackerpage.component.css',
})
export class TrackerPageComponent {
  calenderSkeletonCells = Array(30);
  weekSkeletonCells = Array(7);
  EXPECTED_WORK_DAYS_PER_MONTH = EXPECTED_WORK_DAYS_PER_MONTH;
  isSameMonth = isSameMonth;
  isToday = isToday;
  isFuture = isFuture;

  employeeWorkLogQueryError = computed(() =>
    this.worklogService.employeeWorkLogQuery.error()
  );
  employeeWorkLogQuerySuccess = computed(() => {
    if (this.employeeWorkLogQueryError()) {
      this.showError('Unable to retrieve worklogs');
      return false;
    }
    return this.worklogService.employeeWorkLogQuery.isSuccess();
  });
  employeeWorkLogQueryLoading = computed(() =>
    this.worklogService.employeeWorkLogQuery.isLoading()
  );

  workLogQueryError = computed(() => this.worklogService.worklogQuery.error());
  workLogQuerySuccess = computed(() => {
    if (this.workLogQueryError()) {
      this.showError('Unable to retrieve worklogs');
      return false;
    }
    return this.worklogService.worklogQuery.isSuccess();
  });
  workLogQueryLoading = computed(() =>
    this.worklogService.worklogQuery.isLoading()
  );

  totalWorkedDaysOfMonth = computed(() =>
    this.worklogService.getTotalWorkedDaysOfMonth()
  );

  totalWorkedHoursOfMonth = computed(() => {
    const totalWorkedHoursOfMonth =
      this.worklogService.getTotalWorkedHoursOfMonth();
    if (+totalWorkedHoursOfMonth) {
      return this.formatHoursToTimeString(+totalWorkedHoursOfMonth);
    }
    return 0;
  });

  averageWorkedHours = computed(() => {
    const totalWorkedHoursOfMonth =
      this.worklogService.getTotalWorkedHoursOfMonth();
    if (+totalWorkedHoursOfMonth) {
      const totalAverageHours =
        +totalWorkedHoursOfMonth / this.totalWorkedDaysOfMonth();
      return this.formatHoursToTimeString(totalAverageHours);
    }
    return 0;
  });

  // Utility function to format decimal hours into HH:MM format
  formatHoursToTimeString(decimalHours: number): string {
    if (!decimalHours || isNaN(decimalHours)) return '0';

    const wholeHours = Math.floor(decimalHours);
    const minutes = Math.round((decimalHours - wholeHours) * 60);
    const formattedMinutes = minutes.toString().padStart(2, '0');

    const days = Math.floor(wholeHours / 24);
    const hours = wholeHours % 24;

    return `${days ? `${days}d ` : ''}${hours}h${minutes ? ` ${formattedMinutes}m` : ''}`;
  }

  filterOptions = signal<SelectItemGroup[]>([
    {
      label: 'Worklog Status',
      value: 'worklogStatus',
      items: [
        { label: 'Missing', value: WORKLOG_STATUS.MISSING },
        { label: 'Submitted', value: WORKLOG_STATUS.SUBMITTED },
        { label: 'Approved', value: WORKLOG_STATUS.APPROVED },
        { label: 'Revised', value: WORKLOG_STATUS.REVISED },
        { label: 'Rejected', value: WORKLOG_STATUS.REJECTED },
      ],
    },
    {
      label: 'Attendance',
      value: 'attendance',
      items: [
        { label: 'Full Day Leave', value: DAY_OFF_TYPE.FULL_DAY_LEAVE },
        { label: 'Half Day Leave', value: DAY_OFF_TYPE.HALF_DAY_LEAVE },
        { label: 'Holiday', value: DAY_OFF_TYPE.HOLIDAY },
      ],
    }
  ]);

  selectedFilterOptions: SelectedFilterOptions[] = [];

  onDateChange(date: Date) {
    this.worklogService.currentDate.set(date);
    if (this.employeeWorkLogQueryError() || this.workLogQueryError()) {
      this.showError('Unable to retrieve worklogs');
    }
  }

  onFilterClear() {
    this.selectedFilterOptions = [];
  }

  onFilterChange(event: MultiSelectChangeEvent) {
    const selectedItems = event.value;

    this.selectedFilterOptions = [];

    this.selectedFilterOptions = this.filterOptions().reduce<
      SelectedFilterOptions[]
    >((acc, group) => {
      const selectedValues = selectedItems.filter((item: any) =>
        group.items.some((gItem) => gItem.value === item)
      );

      if (selectedValues.length > 0) {
        acc.push({
          group: group.value,
          selectedOptions: selectedValues,
        });
      }

      return acc;
    }, []);
  }
  constructor(
    private messageService: MessageService,
    private dialogService: DialogService,
    private worklogService: WorkLogService,
    private projectService: ProjectsService,
    private authService: AuthService
  ) {
    if (this.employeeWorkLogQueryError() || this.workLogQueryError()) {
      this.showError('Unable to retrieve worklogs');
    }
  }

  openWorklogForm(date: Date) {
    const dialogRef = this.dialogService.open(WorklogFormComponent, {
      data: {
        date,
      },
      header: 'Add Worklog',
      dismissableMask: false,
      width: '30rem',
    });
    dialogRef.onClose.subscribe((result) => {
      if (result) {
        if (result.type === 'success') {
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: result.message,
          });
        } else if (result.type === 'error') {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: result.message,
          });
        }
      }
    });
  }

  showError(errorMessage: string) {
    this.messageService.add({
      severity: 'error',
      summary: 'Error',
      detail: errorMessage,
    });
  }

  getChipDetails(date: Date): {
    chipText: string;
    tooltipText: string;
    chipClass: string;
  } | null {
    const worklog = this.worklogService.findEmployeeWorklog(date);
    const leaveDetail = this.worklogService.findEmployeeLeaveDetail(date);
    const selectedAttendanceOptions = this.selectedFilterOptions.find(
      (option) => option.group === 'attendance'
    )?.selectedOptions;

    const selectedWorklogStatusOptions = this.selectedFilterOptions.find(
      (option) => option.group === 'worklogStatus'
    )?.selectedOptions;

    const onlyAttendanceOptionIsSelected =!selectedWorklogStatusOptions;

    const worklogs = this.getWorklogs(date);

    const optionsMap = [
      {
        selectionCondition: () =>
          selectedAttendanceOptions?.includes(DAY_OFF_TYPE.HOLIDAY) &&
          worklog?.isCompanyOff,
        defaultCondition: () => worklog?.isCompanyOff,
        chipDetails: {
          chipText: 'HLD',
          tooltipText: 'Holiday',
          chipClass: 'bg-green-500 text-white',
        },
      },
      {
        selectionCondition: () => worklog?.isWeekOff,
        defaultCondition: () => worklog?.isWeekOff,
        chipDetails: {
          chipText: 'W-OFF',
          tooltipText: 'Weekend Off',
          chipClass: 'bg-primary-500 text-white',
        },
      },
      {
        selectionCondition: () =>
          selectedAttendanceOptions?.includes(DAY_OFF_TYPE.FULL_DAY_LEAVE) &&
          leaveDetail?.leaveStatus === LEAVE_STATUS.FULL_DAY_LEAVE,
        defaultCondition: () =>
          leaveDetail?.leaveStatus === LEAVE_STATUS.FULL_DAY_LEAVE,
        chipDetails: {
          chipText: 'FDL',
          tooltipText: 'Full Day Leave',
          chipClass: 'bg-red-500 text-white',
        },
      },
      {
        selectionCondition: () =>
          selectedAttendanceOptions?.includes(DAY_OFF_TYPE.HALF_DAY_LEAVE) &&
          leaveDetail?.leaveStatus === LEAVE_STATUS.HALF_DAY_LEAVE,
        defaultCondition: () =>
          leaveDetail?.leaveStatus === LEAVE_STATUS.HALF_DAY_LEAVE,
        chipDetails: {
          chipText: 'HDL',
          tooltipText: 'Half Day Leave',
          chipClass: 'bg-amber-500 text-white',
        },
      },
    ];

    // If only attendance filters are selected, display the all the badges based on the selected attendance type
    if (
      selectedAttendanceOptions?.length
    ) {
      for (const option of optionsMap) {
        if (option.selectionCondition()) {
          return option.chipDetails;
        }
      }
    }
    // If multiple filters are selected, display the badge only when a worklog is present for that day
    else if (!onlyAttendanceOptionIsSelected) {
      for (const option of optionsMap) {
        if (option.defaultCondition() 
          &&(option.chipDetails.chipText === 'W-OFF' || worklogs.length))
        {
          return option.chipDetails;
        }
      }
    } else {
      // Fall back to conditions not requiring selectedOptions
      for (const option of optionsMap) {
        if (option.defaultCondition()) {
          return option.chipDetails;
        }
      }
    }

    return null;
  }

  workHourIsLessThanExpected(day: Date) {
    const selectedWorklogStatus = this.selectedFilterOptions.find(
      (option) => option.group === 'worklogStatus'
    )?.selectedOptions;

    // If "missing" status is selected, return false and not display a warning for that day.
    if (selectedWorklogStatus?.includes(WORKLOG_STATUS.MISSING)) return false;

    const worklogs = this.getWorklogs(day);
    // If no work logs are found, return false and not display a warning for that day.
    if (worklogs.length === 0) return false;

    const workMinutes = this.worklogService.totalWorkMinutes(day);
    return (
      (this.worklogService.isHalfDay(day) && workMinutes < HALF_DAY_MINUTES) ||
      (this.worklogService.isFullDay(day) && workMinutes < FULL_DAY_MINUTES)
    );
  }

  /**
   * Retrieves and filters worklogs for a specific day based on selected filter options.
   */
  getWorklogs = (day: Date): Worklog[] => {
    if (!this.workLogQuerySuccess()) {
      return [];
    }

    const worklogs = this.worklogService
      .worklogDateMap()
      .get(format(day, DATE_FORMAT_YYYY_MM_DD));
    if (!worklogs) return [];

    // If no filter options are selected, return all worklogs
    if (!this.selectedFilterOptions || this.selectedFilterOptions.length <= 0) {
      return worklogs;
    }
    return this.filteredWorklogs(worklogs, day);
  };

  filteredWorklogs(worklogs: Worklog[], day: Date) {
    // Extract the selected project IDs from the filter options.
    const selectedProjectIds = this.selectedFilterOptions
      .filter((option) => option.group === 'projects')
      .flatMap((option) => option.selectedOptions);

    // Extract the selected statuses from the filter options.
    const selectedStatuses = this.selectedFilterOptions
      .filter((option) => option.group === 'worklogStatus')
      .flatMap((option) => option.selectedOptions);

    // Check if the work hours for the given day are less than expected.
    const isMissingWorklog = this.isEffortsMissing(day);

    // If the "missing" status is selected and work hours are less than expected,
    // return all worklogs for the day as-is.
    if (
      selectedStatuses?.includes(WORKLOG_STATUS.MISSING) &&
      isMissingWorklog
    ) {
      return worklogs;
    }

    // If attendance filters are selected, check if the day matches any of the selected attendance types
    const selectedAttendanceOptions = this.selectedFilterOptions.find(
      (option) => option.group === 'attendance'
    )?.selectedOptions;

    if (selectedAttendanceOptions?.length) {
      const worklog = this.worklogService.findEmployeeWorklog(day);
      const leaveDetail = this.worklogService.findEmployeeLeaveDetail(day);

      const isHoliday =
        worklog?.isCompanyOff &&
        selectedAttendanceOptions.includes(DAY_OFF_TYPE.HOLIDAY);
      const isFullDayLeave =
        leaveDetail?.leaveStatus === LEAVE_STATUS.FULL_DAY_LEAVE &&
        selectedAttendanceOptions.includes(DAY_OFF_TYPE.FULL_DAY_LEAVE);
      const isHalfDayLeave =
        leaveDetail?.leaveStatus === LEAVE_STATUS.HALF_DAY_LEAVE &&
        selectedAttendanceOptions.includes(DAY_OFF_TYPE.HALF_DAY_LEAVE);

      // Match if any leave condition is met
      const isOnLeave = isHoliday || isFullDayLeave || isHalfDayLeave;

      // Match if any status condition is met
         const worklogsMatchingSelectedStatus = worklogs.some((worklog) =>
         selectedStatuses.includes(worklog.workLogStatus?.status ?? '')
       );

      // Display results if either status or leave matches
      if (!isOnLeave && !worklogsMatchingSelectedStatus) {
       return [];
     }
    }

    return worklogs.filter((worklog) => {
      const isStatusMatch =
        selectedStatuses.length === 0 ||
        selectedStatuses.includes(worklog.workLogStatus?.status ?? '');
      return isStatusMatch;
    });
  }

  isEffortsMissing(date: Date): boolean {
    const isFilterActive = this.selectedFilterOptions?.length > 0;
    const isMissingSelected = this.selectedFilterOptions?.some((option) =>
      option.selectedOptions.includes(WORKLOG_STATUS.MISSING)
    );

    // If the filter is active but "missing" is NOT selected, return false
    if (isFilterActive && !isMissingSelected) {
      return false;
    }

    // Default missing efforts check
    const formattedDate = format(date, DATE_FORMAT_YYYY_MM_DD);
    return (
      !this.worklogService.worklogDateMap().get(formattedDate) &&
      this.worklogService.isFullDay(date) &&
      isPast(date) &&
      !isWeekend(date) &&
      !isToday(date)
    );
  }
}
