<!-- Embeds the CalendarComponent -->
@defer {
  <tms-calendar
    [onCalendarCellClick]="openWorklogForm.bind(this)"
    (onDateChange)="onDateChange($event)"
    [cellContentTemplate]="cellContentTemplate"
    [cellHeaderTemplate]="cellHeaderTemplate"
    [calendarHeaderTemplate]="calendarHeaderTemplate"
  >
    <ng-template #calendarHeaderTemplate>
      <div class="flex flex-wrap items-center justify-between gap-3 md:gap-4">
        <div class="flex items-center gap-2">
          <div class="h-3 w-3 rounded-full bg-primary-200"></div>
          <span class="text-gray-500">Submitted</span>
        </div>
        <div class="flex items-center gap-2">
          <div class="h-3 w-3 rounded-full bg-green-200"></div>
          <span class="text-gray-500">Approved</span>
        </div>
        <div class="flex items-center r gap-2">
          <div class="h-3 w-3 rounded-full bg-purple-200"></div>
          <span class="text-gray-500">Revised</span>
        </div>
        <div class="flex items-center gap-2">
          <div class="h-3 w-3 rounded-full bg-red-200"></div>
          <span class="text-gray-500">Rejected</span>
        </div>

        <p-multiSelect
          #multiSelectFilter
          [options]="filterOptions()"
          [group]="true"
          placeholder="Select Filters"
          scrollHeight="250px"
          display="chip"
          [style]="{ width: '200px', borderRadius: '0.5rem' }"
          [filter]="false"
          [showHeader]="false"
          [showClear]="true"
          (onClear)="onFilterClear(); multiSelectFilter.hide()"
          (onChange)="onFilterChange($event)"
        >
          <ng-template let-group pTemplate="group">
            <div class="flex align-items-center">
              <span>{{ group.label }}</span>
            </div>
          </ng-template>

          <ng-template pTemplate="clearicon">
            <i class="pi pi-filter-slash absolute right-1/2"></i>
          </ng-template>
        </p-multiSelect>

        <tms-progress-chart
          [actualValue]="totalWorkedDaysOfMonth()"
          [baseValue]="EXPECTED_WORK_DAYS_PER_MONTH"
          suffix="d"
          class="min-w-11"
          [pTooltip]="worklogSummary"
          [autoHide]="false"
          positionLeft="-200"
        />

        <ng-template #worklogSummary>
          <tms-monthly-work-summary
            [averageWorkedHours]="averageWorkedHours()"
            [totalWorkedDaysOfMonth]="totalWorkedDaysOfMonth()"
            [totalWorkedHoursOfMonth]="totalWorkedHoursOfMonth()"
          />
        </ng-template>
      </div>
    </ng-template>

    <ng-template #cellHeaderTemplate let-day>
      @if (employeeWorkLogQuerySuccess() && workLogQuerySuccess()) {
        <div
          class="m-1 text-[0.625rem] px-2 py-0.5 rounded-full font-semibold tracking-wider"
          [ngClass]="getChipDetails(day)?.chipClass"
          pTooltip="{{ getChipDetails(day)?.tooltipText }}"
          tooltipPosition="top"
        >
          {{ getChipDetails(day)?.chipText }}
        </div>
        <i
          [ngClass]="{
            'pi pi-exclamation-circle text-red-500':
              workHourIsLessThanExpected(day),
          }"
          pTooltip="Logged hours are insufficient"
        >
        </i>
      } @else if (
        !isFuture(day) &&
        (employeeWorkLogQueryLoading() || workLogQueryLoading())
      ) {
        <div class="flex gap-2">
          <p-skeleton height="1rem" width="2rem"></p-skeleton>
          <p-skeleton height="1rem" width="1rem" shape="circle"></p-skeleton>
        </div>
      }
    </ng-template>

    <ng-template #cellContentTemplate let-day>
      <div>
        @if (workLogQuerySuccess()) {
          <tms-worklog-display
            [worklogs]="getWorklogs(day)"
            [effortsMissing]="isEffortsMissing(day)"
          />
        } @else if (!isFuture(day) && employeeWorkLogQueryLoading()) {
          <div class="flex flex-col gap-2">
            <p-skeleton height="1rem"></p-skeleton>
            <p-skeleton h eight="1rem"></p-skeleton>
          </div>
        }
      </div>
    </ng-template>
  </tms-calendar>
} @placeholder {
  <div class="h-screen w-full flex flex-col gap-2">
    <div class="flex justify-between">
      <div class="flex gap-2">
        <p-skeleton height="30px" width="70px"></p-skeleton>
        <p-skeleton height="30px" width="50px"></p-skeleton>
        <p-skeleton height="30px" width="50px"></p-skeleton>
        <p-skeleton height="30px" width="100px"></p-skeleton>
      </div>
      <div class="flex gap-4">
        <p-skeleton height="30px" width="90px"></p-skeleton>
        <p-skeleton height="30px" width="90px"></p-skeleton>
        <p-skeleton height="30px" width="90px"></p-skeleton>
        <p-skeleton height="30px" width="90px"></p-skeleton>
        <p-skeleton height="30px" width="200px"></p-skeleton>
        <p-skeleton height="30px" width="30px" shape="circle"></p-skeleton>
      </div>
    </div>

    <div class="grid grid-cols-7 gap-2">
      @for (item of weekSkeletonCells; track $index) {
        <p-skeleton height="40px"></p-skeleton>
      }
    </div>

    <div class="grid grid-cols-7 gap-2">
      @for (item of calenderSkeletonCells; track $index) {
        <p-skeleton height="140px"></p-skeleton>
      }
    </div>
  </div>
}
<p-toast preventOpenDuplicates="true" />
