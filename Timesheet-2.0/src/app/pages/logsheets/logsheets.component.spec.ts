import { ComponentFixture, TestBed } from '@angular/core/testing';

import { LogsheetsComponent } from './logsheets.component';
import { DialogService } from 'primeng/dynamicdialog';
import { HttpClient, HttpHandler } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { QueryClient } from '@tanstack/angular-query-experimental';
import { startOfMonth, endOfMonth } from 'date-fns';

describe('LogsheetsComponent', () => {
  let component: LogsheetsComponent;
  let fixture: ComponentFixture<LogsheetsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [LogsheetsComponent],
      providers: [
        HttpClient,
        DialogService,
        provideHttpClientTesting,
        HttpHandler,
        QueryClient,
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(LogsheetsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should update columns on date change', () => {
    const testDate = new Date(2025, 0, 15);
    component.onDateSelect(testDate);
    expect(component.selectedMonth()).toBe('January');
    expect(component.selectedYear()).toBe('2025');
    expect(component.columnHeader().length).not.toBe(0);
  });

  it('should update columns when date is selected', () => {
    const newDate = new Date('2025-02-15');
    component.onDateSelect(newDate);

    const columns = component.columnHeader();
    expect(columns.length).toBe(29);
    expect(component.columnHeader().length).not.toBe(0);
    expect(component.selectedMonth()).toBe('February');
    expect(component.selectedYear()).toBe('2025');
  });

  it('should generate correct column headers on init', () => {
    const columns = component.columnHeader();
    expect(columns[0]).toEqual({ field: 'name', header: 'Resource Name' });
  });

  it('should calculate correct date range', () => {
    const mockDate = new Date('2025-01-15');
    component.date.set(mockDate);
    const dateRange = component.dateRange();
    expect(dateRange.startDate).toEqual(startOfMonth(mockDate));
    expect(dateRange.endDate).toEqual(endOfMonth(mockDate));
    expect(dateRange.daysInMonth).toBe(31);
  });
});
