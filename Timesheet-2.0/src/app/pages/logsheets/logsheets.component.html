<div class="flex flex-col gap-4">
  <div class="flex items-center flex-wrap justify-between gap-2">
    <div class="flex items-center flex-wrap justify-between w-full gap-4">
      <div class="flex items-center flex-wrap gap-2">
        <p-multiSelect
          #projectDropdown
          placeholder="Select Projects"
          [options]="getProjectDropdownOptions()"
          [ngModel]="projectsQuery().isLoading() ? [] : storedProjects"
          (ngModelChange)="storedProjects = $event"
          optionLabel="label"
          id="project"
          (onChange)="onProjectChange($event)"
          [loading]="projectsQuery().isLoading()"
          [emptyMessage]="'No Projects Found.'"
          styleClass="outline-none shadow-none ring-0"
          [resetFilterOnHide]="true"
          [showClear]="true"
          (onClear)="onProjectClear(); projectDropdown.hide()"
          [style]="{ width: '250px', borderRadius: '0.5rem' }"
        >
          <ng-template pTemplate="clearicon">
            <i class="pi pi-filter-slash absolute right-1/2"></i>
          </ng-template>
        </p-multiSelect>

        <div
          class="flex items-center px-2 rounded-md border border-neutral-300 bg-white"
        >
          <i class="pi pi-search text-black mr-2"></i>
          <input
            pInputText
            type="text"
            [(ngModel)]="globalSearchValue"
            (input)="applyGlobalFilter($event)"
            placeholder="Search Resources"
            class="w-full max-w-xs border-none rounded-md outline-none ring-0"
          />
        </div>

        <p-calendar
          [(ngModel)]="date"
          view="month"
          dateFormat="MM yy"
          inputStyleClass="outline-none shadow-none ring-0"
          (onSelect)="onDateSelect($event)"
          [maxDate]="maxDate"
          [showIcon]="true"
        ></p-calendar>
      </div>
      @if (this.selectedProjectIds().length && this.selectedResources.length) {
        <p-button
          (onClick)="approvalConfirmation($event)"
          [label]="approveButtonLabel"
        />
      }
    </div>
  </div>

  <div class="flex items-center flex-wrap gap-4">
    <div class="flex items-center gap-2">
      <div class="h-3 w-3 rounded-full bg-primary-200"></div>
      <span class="text-gray-500">Submitted</span>
    </div>
    <div class="flex items-center gap-2">
      <div class="h-3 w-3 rounded-full bg-green-200"></div>
      <span class="text-gray-500">Approved</span>
    </div>
    <div class="flex items-center r gap-2">
      <div class="h-3 w-3 rounded-full bg-purple-200"></div>
      <span class="text-gray-500">Revised</span>
    </div>
    <div class="flex items-center gap-2">
      <div class="h-3 w-3 rounded-full bg-red-200"></div>
      <span class="text-gray-500">Rejected</span>
    </div>
    <div class="flex items-center gap-2">
      <div class="h-3 w-3 rounded-full bg-orange-200"></div>
      <span class="text-gray-500">Holiday</span>
    </div>
    <div class="flex items-center gap-2">
      <div class="h-3 w-3 rounded-full bg-pink-200"></div>
      <span class="text-gray-500">FDL</span>
    </div>
    <div class="flex items-center gap-2">
      <div class="h-3 w-3 rounded-full bg-amber-200"></div>
      <span class="text-gray-500">HDL</span>
    </div>
  </div>
  <div class="w-full bg-white overflow-hidden shadow rounded-xl">
    <p-table
      #logSheetTable
      [columns]="columnHeader()"
      [value]="resourcesWithWorklog()"
      [(selection)]="selectedResources"
      [scrollable]="true"
      [scrollHeight]="'calc(100vh - 250px)'"
      [tableStyle]="{ 'min-width': '50rem' }"
      dataKey="id"
      [globalFilterFields]="['name']"
      [loading]="getWorklogsQuery().isLoading()"
      [showLoader]="false"
    >
      <ng-template pTemplate="header">
        <tr class="bg-transparent">
          @for (col of columnHeader(); track col.field) {
            <th class="p-1.5 text-center text-sm bg-primary-50">
              @if (col.field === 'name') {
                <div class="flex gap-2 text-start">
                  <p-tableHeaderCheckbox />
                  {{ col.header }}
                </div>
              } @else {
                <div class="flex flex-col">
                  <span>{{ col.header.split(' ')[0] }}</span>
                  <span>{{ col.header.split(' ')[1] }}</span>
                </div>
              }
            </th>
          }
        </tr>
      </ng-template>

      <ng-template pTemplate="body" let-resource>
        <tr class="hover:bg-gray-100">
          @for (col of columnHeader(); track col.field) {
            <td class="text-center p-0">
              @if (col.field === 'name') {
                <div class="flex items-center gap-2 text-sm text-start ml-1.5">
                  <p-tableCheckbox [value]="resource" />
                  <span
                    #resourceName
                    class="truncate max-w-32"
                    [pTooltip]="
                      resourceName.offsetWidth < resourceName.scrollWidth
                        ? resource.name
                        : ''
                    "
                    [tooltipStyleClass]="
                      'text-xs text-gray-700  whitespace-pre-wrap'
                    "
                    tooltipPosition="top"
                    >{{ resource.name }}</span
                  >
                </div>
              } @else {
                <!-- Display time and apply background based on status -->
                <tms-logsheets-detailedview
                  [worklogDayData]="resource[col.field]"
                  [dateRange]="dateRange()"
                  [selectedProjectIds]="selectedProjectIds()"
                ></tms-logsheets-detailedview>
              }
            </td>
          }
        </tr>
      </ng-template>

      <ng-template pTemplate="emptymessage">
        <tr>
          <td [attr.colspan]="columnHeader().length" class="text-center p-4">
            @if (selectedProjectIds().length <= 0) {
              No projects selected.
            } @else {
              No worklogs found.
            }
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="loadingbody">
        <tr>
          <td [attr.colspan]="columnHeader().length" class="text-center text-gray-500 p-4">
            <i class="pi pi-spin pi-spinner mr-2"></i> Loading worklogs...
          </td>
        </tr>
        @for (_ of skeletonRows; track $index) {
          <tr>
            @for (col of columnHeader(); track $index) {
              <td class="text-sm">
                <p-skeleton width="100%" height="1.5rem"></p-skeleton>
              </td>
            }
          </tr>
        }
      </ng-template>

      <ng-template pTemplate="summary">
        <div class="flex align-items-center justify-content-between">
          {{ table()?.filteredValue?.length ?? resourcesWithWorklog().length }}
          rows
        </div>
      </ng-template>
    </p-table>
  </div>

  <p-toast preventOpenDuplicates="true" />
  <p-confirmDialog />
</div>
