import { CommonModule } from '@angular/common';
import {
  Component,
  computed,
  OnInit,
  output,
  signal,
  viewChild,
  ViewEncapsulation,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { CalendarModule } from 'primeng/calendar';
import { MultiSelectChangeEvent, MultiSelectModule } from 'primeng/multiselect';
import { TableModule, Table } from 'primeng/table';
import { InputTextModule } from 'primeng/inputtext';
import { ButtonModule } from 'primeng/button';
import { AuthService } from '../../services/auth/auth-service';
import { WorkLogService } from '../../services/worklog/worklog.service';
import { ProjectsService } from '../../services/project/project.service';
import { Option } from '../../services/common.model';
import { Worklog } from '../../services/worklog/worklog.model';
import { SkeletonModule } from 'primeng/skeleton';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ToastModule } from 'primeng/toast';
import { injectMutation } from '@tanstack/angular-query-experimental';
import { startOfMonth, endOfMonth, getDaysInMonth } from 'date-fns';
import { SELECTED_PROJECTS } from '../../settings';
import { TooltipModule } from 'primeng/tooltip';
import { LogsheetsDetailedViewComponent } from '../../components/logsheets-detailedview/logsheets-detailedview.component';

interface Column {
  field: string;
  header: string;
}

export interface WorklogDayData {
  totalHours: string;
  statuses: WorklogStatus[];
  totalMinutes: number;
  date: Date;
  resourceId: string;
}

interface Resource {
  id: string;
  name: string;
  worklog: Worklog[];
  [key: `day${number}`]: WorklogDayData;
}

export type WorklogStatus =
  | 'no worklogs'
  | 'holiday'
  | 'weekoff'
  | 'leave'
  | 'halfDayLeave'
  | 'submitted'
  | 'approved'
  | 'revised'
  | 'rejected';

@Component({
  selector: 'tms-logsheets',
  standalone: true,
  imports: [
    TableModule,
    CommonModule,
    FormsModule,
    CalendarModule,
    InputTextModule,
    MultiSelectModule,
    ButtonModule,
    SkeletonModule,
    ConfirmDialogModule,
    ToastModule,
    TooltipModule,
    LogsheetsDetailedViewComponent,
  ],
  providers: [ConfirmationService, MessageService],
  templateUrl: './logsheets.component.html',
  styleUrl: './logsheets.component.css',
})
export class LogsheetsComponent implements OnInit {
  table = viewChild<Table>('logSheetTable');

  private readonly DEFAULT_HOURS = '-';
  private readonly DEFAULT_STATUS: WorklogStatus = 'no worklogs';
  readonly skeletonRows = Array(3);

  readonly date = signal<Date>(new Date());
  readonly maxDate = new Date();
  readonly columnHeader = signal<Column[]>([]);
  readonly selectedMonth = signal<string>('');
  readonly selectedYear = signal<string>('');
  readonly selectedProjectIds = signal<string[]>([]);
  readonly dateRange = computed(() => this.getDateRange(this.date()));

  worklogDetails = output<WorklogDayData>();

  selectedResources: Resource[] = [];
  storedProjects: Option[] = [];
  globalSearchValue = '';

  readonly resourcesWithWorklog = computed(() => {
    if (!this.selectedProjectIds().length) return [];

    const { daysInMonth } = this.dateRange();
    const worklogsByResource = this.getWorklogsQuery().data();

    if (!worklogsByResource?.length) return [];

    const baseStructure = this.initializeResourceStructure(
      worklogsByResource,
      daysInMonth
    );
    return this.processWorklogs(worklogsByResource, baseStructure);
  });

  readonly projectsQuery = computed(() =>
    this.projectService.projectListQuery()
  );

  readonly approveWorkLogQuery = injectMutation(() => ({
    mutationKey: ['approve-worklog', this.authService.userId()],
    mutationFn: (workLogIdList: string[]) =>
      this.worklogService.approveWorkLogs(workLogIdList),
    onSuccess: () => this.handleApprovalSuccess(),
    onError: () => this.handleApprovalError(),
  }));

  readonly getWorklogsQuery = computed(() =>
    this.worklogService.getWorkLogsByManagerIdQuery(
      this.authService.userId(),
      this.dateRange().startDate.toISOString(),
      this.dateRange().endDate.toISOString(),
      this.selectedProjectIds()
    )
  );

  get approveButtonLabel() {
    return `Bulk Approve (${this.selectedResources.length})`;
  }

  get approvalMessage(): string {
    const resourceCount = this.selectedResources.length;
    return `Are you sure you want to approve the worklogs for the selected ${resourceCount === 1 ? 'resource' : 'resources'}?`;
  }

  constructor(
    private readonly authService: AuthService,
    private readonly worklogService: WorkLogService,
    private readonly projectService: ProjectsService,
    private readonly confirmationService: ConfirmationService,
    private readonly messageService: MessageService
  ) {}

  ngOnInit(): void {
    this.updateColumns(this.date());
  }

  onDateSelect(selectedDate: Date): void {
    this.date.set(selectedDate);
    this.updateColumns(selectedDate);
  }

  applyGlobalFilter(event: Event): void {
    const value = (event.target as HTMLInputElement).value;
    this.table()?.filterGlobal(value, 'contains');
  }

  clearGlobalFilter(): void {
    this.globalSearchValue = '';
    this.table()?.filterGlobal('', 'contains');
  }

  getProjectDropdownOptions(): Option[] {
    return (
      this.projectsQuery()
        .data()
        ?.map((project) => ({
          label: project.projectName,
          value: project.id,
        })) ?? []
    );
  }

  onProjectChange(event: MultiSelectChangeEvent): void {
    this.selectedProjectIds.set(
      event.value.map((option: Option) => option.value)
    );
    localStorage.setItem(SELECTED_PROJECTS, JSON.stringify(event.value));
  }

  approvalConfirmation(event: Event): void {
    this.confirmationService.confirm({
      target: event.target as EventTarget,
      message: this.approvalMessage,
      header: 'Confirm Approval',
      acceptIcon: 'none',
      rejectIcon: 'none',
      acceptLabel: 'Approve',
      rejectLabel: 'Cancel',
      acceptButtonStyleClass: 'p-button-primary',
      rejectButtonStyleClass: 'p-button-danger',
      defaultFocus: 'none',
      accept: () => this.approveWorklogs(),
    });
  }

  private getDateRange(date: Date) {
    const year = date.getFullYear();
    const month = date.getMonth();

    return {
      startDate: new Date(Date.UTC(year, month, 1)),
      endDate: new Date(Date.UTC(year, month + 1, 0, 23, 59, 59, 999)),
      daysInMonth: getDaysInMonth(date),
    };
  }

  private updateColumns(date: Date): void {
    const monthIndex = date.getMonth();
    const year = date.getFullYear();

    this.selectedMonth.set(date.toLocaleString('default', { month: 'long' }));
    this.selectedYear.set(year.toString());

    const daysInMonth = new Date(year, monthIndex + 1, 0).getDate();
    const newCols: Column[] = [
      { field: 'name', header: 'Resource Name' },
      ...this.generateDayColumns(daysInMonth, year, monthIndex),
    ];

    this.columnHeader.set(newCols);
  }

  private generateDayColumns(
    daysInMonth: number,
    year: number,
    monthIndex: number
  ): Column[] {
    return Array.from({ length: daysInMonth }, (_, i) => {
      const currentDate = new Date(year, monthIndex, i + 1);
      const day = currentDate.toLocaleString('default', { weekday: 'short' });
      return {
        field: `day${i + 1}`,
        header: `${i + 1} ${day.charAt(0)}`,
      };
    });
  }

  private initializeResourceStructure(
    worklogsByResource: Worklog[],
    daysInMonth: number
  ): Record<string, Resource> {
    return worklogsByResource.reduce(
      (baseStructure, worklog) => {
        const { employeeId: resourceId, employeeResource } = worklog;

        if (!baseStructure[resourceId] && employeeResource) {
          baseStructure[resourceId] = {
            id: resourceId,
            name: employeeResource.name,
            worklog: [],
            ...this.generateEmptyDays(daysInMonth),
          };
        }

        return baseStructure;
      },
      {} as Record<string, Resource>
    );
  }

  private generateEmptyDays(
    daysInMonth: number
  ): Record<string, WorklogDayData> {
    return Object.fromEntries(
      Array.from({ length: daysInMonth }, (_, i) => [
        `day${i + 1}`,
        {
          totalHours: this.DEFAULT_HOURS,
          status: this.DEFAULT_STATUS,
          statuses: [],
          totalMinutes: 0,
          date: new Date(
            this.date().getFullYear(),
            this.date().getMonth(),
            i + 1
          ),
          resourceId: '',
        },
      ])
    );
  }

  private processWorklogs(
    worklogsByResource: Worklog[],
    baseStructure: Record<string, Resource>
  ): Resource[] {
    const updatedStructure = worklogsByResource.reduce(
      (acc, worklog) => {
        const resourceId = worklog.employeeId;
        if (!acc[resourceId]) return acc;

        const day = new Date(worklog.workDate).getUTCDate();
        if (acc[resourceId]?.[`day${day}`]) {
          acc[resourceId][`day${day}`] = this.processWorklogDay(
            worklog,
            acc[resourceId][`day${day}`]
          );
        }
        acc[resourceId].worklog.push(worklog);

        return acc;
      },
      { ...baseStructure }
    );

    return Object.values(updatedStructure);
  }

  private processWorklogDay(
    worklog: Worklog,
    currentData: WorklogDayData
  ): WorklogDayData {
    const totalMinutes = currentData.totalMinutes + (worklog.minutes || 0);
    const newStatus = this.determineWorklogStatus(worklog);
    const statuses =
      newStatus !== this.DEFAULT_STATUS
        ? [...currentData.statuses, newStatus]
        : currentData.statuses;

    return {
      totalHours: this.formatHours(totalMinutes),
      statuses,
      totalMinutes,
      resourceId: worklog.employeeId,
      date: worklog.workDate,
    };
  }

  private determineWorklogStatus(worklog: Worklog): WorklogStatus {
    if (worklog.workLogStatus?.status === 'approved' && worklog.minutes > 0)
      return 'approved';
    if (worklog.isCompanyOff) return 'holiday';
    if (worklog.isOnFirstHalfLeave || worklog.isOnSecondHalfLeave)
      return 'halfDayLeave';
    if (worklog.isOnLeave) return 'leave';

    return (
      (worklog.workLogStatus?.status as WorklogStatus) || this.DEFAULT_STATUS
    );
  }

  private formatHours(totalMinutes: number): string {
    if (totalMinutes <= 0) return this.DEFAULT_HOURS;
    const hours = totalMinutes / 60;
    return totalMinutes % 60 === 0
      ? Math.floor(hours).toString()
      : hours.toFixed(2);
  }

  private approveWorklogs(): void {
    const worklogIdsToApprove = this.getSubmittedWorklogIds();
    if (!worklogIdsToApprove.size) {
      this.showMessage(
        'info',
        'No Approval Pending ',
        'There are no worklogs in a submitted / revised state.'
      );
      return;
    }

    this.approveWorkLogQuery.mutate(Array.from(worklogIdsToApprove));
    this.getWorklogsQuery().refetch();
    this.selectedResources = [];
  }

  private getSubmittedWorklogIds(): Set<string> {
    return new Set(
      this.selectedResources
        .flatMap((resource) => resource.worklog)
        .filter(
          (worklog) =>
            worklog.workLogStatus?.status === 'submitted' ||
            worklog.workLogStatus?.status === 'revised'
        )
        .map((worklog) => worklog.id)
    );
  }

  private handleApprovalSuccess(): void {
    this.getWorklogsQuery().refetch();
    this.showMessage(
      'success',
      'Approved',
      'Successfully approved the worklogs'
    );
  }

  private handleApprovalError(): void {
    this.showMessage('error', 'Failed', 'Failed to approve the worklogs');
  }

  private showMessage(severity: string, summary: string, detail: string): void {
    this.messageService.add({ severity, summary, detail });
  }

  getStatusClass(status: WorklogStatus): Record<string, boolean> {
    return {
      'bg-green-200': status === 'approved',
      'bg-pink-200': status === 'leave',
      'bg-amber-200': status === 'halfDayLeave',
      'bg-orange-200': status === 'holiday',
      'bg-primary-200': status === 'submitted',
      'bg-purple-200': status === 'revised',
      'bg-red-200': status === 'rejected',
    };
  }

  onProjectClear() {
    this.selectedProjectIds.set([]);
    localStorage.removeItem(SELECTED_PROJECTS);
  }
}
