import { Component, computed, inject, signal } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { CommonModule } from '@angular/common';
import { AuthService } from '../../services/auth/auth-service';
import { ButtonModule } from 'primeng/button';
import { ResourceReportComponent } from '../../components/resource-report/resource-report.component';
import { ClientReportComponent } from '../../components/client-report/client-report.component'; 

type ReportTab = 'client' | 'resource';

@Component({
  selector: 'tms-report-page',
  standalone: true,
  imports: [CommonModule, ButtonModule, ResourceReportComponent,ClientReportComponent],
  templateUrl: './report-page.component.html',
  styleUrl: './report-page.component.css',
})
export class ReportPageComponent {
  private route = inject(ActivatedRoute);
  private router = inject(Router);

  readonly activeTab = signal(0);

  constructor(private authService: AuthService) {}

  selectedTab = signal<ReportTab>('resource');

  role = computed(() => this.authService.userRole());

  readonly isAllowed = computed(() =>
    ['admin', 'finance', 'manager'].includes(this.role()!)
  );
}
