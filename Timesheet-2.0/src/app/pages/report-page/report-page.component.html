<div
  class="min-h-screen h-screen overflow-y-auto flex flex-col space-y-6 pb-40 hide-scrollbar"
>
  <div class="flex gap-3 mt-2">
    <p-button
      label="Resource"
      [outlined]="selectedTab() !== 'resource'"
      [severity]="selectedTab() === 'resource' ? 'primary' : 'secondary'"
      (onClick)="selectedTab.set('resource')"
      styleClass="text-sm font-medium"
    ></p-button>
    <p-button
      label="Client"
      [outlined]="selectedTab() !== 'client'"
      [severity]="selectedTab() === 'client' ? 'primary' : 'secondary'"
      (onClick)="selectedTab.set('client')"
      styleClass="text-sm font-medium"
    ></p-button>
  </div>

  @if (isAllowed() && selectedTab() === 'resource') {
    <div class="mt-6 bg-white p-4 shadow rounded-lg">
      <tms-resource-report-page></tms-resource-report-page>
    </div>
  }
  @if (isAllowed() && selectedTab() === 'client') {
    <div class="mt-6 bg-white p-4 shadow rounded-lg">
      <tms-client-report-page></tms-client-report-page>
    </div>
  }
</div>
