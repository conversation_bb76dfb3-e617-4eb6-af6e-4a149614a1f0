import { CommonModule } from '@angular/common';
import {
  Component,
  computed,
  signal,
  viewChild,
  ChangeDetectionStrategy,
  OnInit,
  OnDestroy,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { finalize } from 'rxjs/operators';
import { ConfirmationService, MenuItem, MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { ChipModule } from 'primeng/chip';
import { DialogModule } from 'primeng/dialog';
import { DialogService } from 'primeng/dynamicdialog';
import { DropdownModule } from 'primeng/dropdown';
import {
  FileUpload,
  FileUploadHandlerEvent,
  FileUploadModule,
} from 'primeng/fileupload';
import { InputTextModule } from 'primeng/inputtext';
import { Menu, MenuModule } from 'primeng/menu';
import { MultiSelectModule } from 'primeng/multiselect';
import { Table, TableModule, TableLazyLoadEvent } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { TooltipModule } from 'primeng/tooltip';
import { SkeletonModule } from 'primeng/skeleton';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import {
  injectQuery,
  injectQueryClient,
} from '@tanstack/angular-query-experimental';
import { Subject, Subscription } from 'rxjs';
import { debounceTime, map } from 'rxjs/operators';

import { AuthService } from '../../services/auth/auth-service';
import { DepartmentService } from '../../services/department/department.service';
import { StringUtilsService } from '../../services/stringutils/stringutils.service';
import { Department } from '../../services/department/department.model';
import { TeamsService } from '../../services/teams/teams.service';
import { Resource, TeamsQueryParams } from '../../services/teams/teams.model';
import { endpoints } from '../../endpoints';
import { EmployeeFormComponent } from '../../components/employee-form/employee-form.component';

/**
 * Component for displaying and managing employee/team data.
 *
 * This component fetches employee data, displays it in a table, and provides
 * functionality for filtering and interacting with employee records.
 */
@Component({
  selector: 'tms-teams',
  standalone: true,
  imports: [
    InputTextModule,
    ButtonModule,
    TableModule,
    ChipModule,
    MenuModule,
    ToastModule,
    FormsModule,
    TooltipModule,
    FileUploadModule,
    CommonModule,
    DialogModule,
    DropdownModule,
    MultiSelectModule,
    SkeletonModule,
    ConfirmDialogModule,
  ],
  providers: [MessageService, ConfirmationService],
  templateUrl: './teams.component.html',
  styleUrl: './teams.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TeamsComponent implements OnInit, OnDestroy {
  teamsTable = viewChild<Table>('teamsTable');
  employeeUploader = viewChild<FileUpload>('employeeUploader');
  readonly MAX_FILE_SIZE = 10000000; // 10 MB
  globalSearchValue: string = '';
  skeletonRows = Array(3);
  menuItems: MenuItem[] = [];
  isUploading: boolean = false;

  // Inject query client for cache invalidation
  private queryClient = injectQueryClient();

  // RxJS-based debounce for query parameter updates
  private updateParams$ = new Subject<void>();
  private subscription = new Subscription();

  // Filter signals
  selectedDepartment = signal<string | null>(null);
  selectedRole = signal<string | null>(null);
  selectedLocation = signal<string | null>(null);

  // Client-side sorting signals
  sortField = signal<string | null>(null);
  sortOrder = signal<'asc' | 'desc' | null>(null);

  // Query parameters for filtering
  queryParams = signal<TeamsQueryParams>({
    pageNumber: 1,
    countPerPage: 10,
  });

  // Current page for PrimeNG table (0-based)
  currentFirst = computed(() => {
    const params = this.queryParams();
    return (params.pageNumber - 1) * params.countPerPage;
  });

  // Pagination configuration
  readonly paginationConfig = {
    rowsPerPageOptions: [10, 20, 50, 100],
    maxPageLinks: 4, // Show up to 4 page links
  };

  // Computed property for total pages
  totalPages = computed(() => {
    const total = this.totalRecords();
    const perPage = this.queryParams().countPerPage;
    return Math.ceil(total / perPage);
  });

  // Single query instance to avoid multiple API calls
  private teamsQuery = injectQuery(() => ({
    queryKey: [
      'getTeamsWithFilters',
      this.authService.userId(),
      this.queryParams(),
    ],
    queryFn: () => this.teamsService.getTeamsList(this.queryParams()),
  }));

  // Computed property that fetches employee data from API
  employees = computed(() => {
    const response = this.teamsQuery.data();
    const employees = response?.data || [];
    return this.sortEmployees(employees);
  });

  // Computed property for total count
  totalRecords = computed(() => {
    const response = this.teamsQuery.data();
    const count = response?.count || 0;
    return count;
  });

  hasError = computed(() => {
    return this.teamsQuery.isError();
  });

  isLoading = computed(() => {
    return this.teamsQuery.isLoading();
  });

  role = computed(() => this.authService.userRole());

  // Department query using injectQuery directly
  private allDepartmentsQuery = injectQuery(() => ({
    queryKey: ['listAllDepartments'],
    queryFn: () => this.departmentService.listAllDepartments(),
  }));

  departmentHasError = computed(() => this.allDepartmentsQuery.isError());

  // Filter options
  departmentOptions = computed(() => {
    const departments = this.allDepartmentsQuery.data();
    return (
      departments
        ?.filter(
          (dept: Department) =>
            dept.departmentName && dept.departmentName.trim() !== ''
        )
        .map((dept: Department) => ({
          label: dept.departmentName,
          value: dept.departmentName,
        })) || []
    );
  });

  /**
   * Get department query for template access
   */
  getDepartmentQuery() {
    return this.allDepartmentsQuery;
  }

  roleOptions = [
    { label: 'Admin', value: 'admin' },
    { label: 'Manager', value: 'manager' },
    { label: 'Employee', value: 'employee' },
  ];

  locationOptions = [
    { label: 'Mangalore', value: 'mangalore' },
    { label: 'Bangalore', value: 'bangalore' },
    { label: 'Toronto', value: 'toronto' },
    { label: 'US', value: 'us' },
  ];

  constructor(
    public stringUtilsService: StringUtilsService,
    private dialogService: DialogService,
    private messageService: MessageService,
    private authService: AuthService,
    private confirmationService: ConfirmationService,
    private departmentService: DepartmentService,
    private teamsService: TeamsService,
    private http: HttpClient
  ) {}

  // Base table columns (visible to all roles)
  private baseTableColumns = [
    {
      field: 'name',
      header: 'Employee Name',
      sortable: true,
      class: '',
    },
    {
      field: 'email',
      header: 'Email ID',
      sortable: true,
      class: '',
    },
    {
      field: 'phoneNumber',
      header: 'Contact Number',
      sortable: false,
      class: '',
    },
    {
      field: 'role',
      header: 'Role',
      sortable: true,
      class: '',
    },
    {
      field: 'designation',
      header: 'Designation',
      sortable: true,
      class: '',
    },
    {
      field: 'location',
      header: 'Location',
      sortable: true,
      class: '',
    },
  ];

  // Actions column (only visible to admin)
  private actionsColumn = {
    field: 'actions',
    header: 'Actions',
    sortable: false,
    class: 'text-center',
  };

  // Computed table columns based on user role
  employeeTableColumns = computed(() => {
    const userRole = this.role();
    if (userRole === 'admin') {
      return [...this.baseTableColumns, this.actionsColumn];
    }
    return this.baseTableColumns;
  });

  /**
   * Handles input changes and triggers API call with debounce
   */
  onInputChange(event: Event) {
    const target = event.target as HTMLInputElement;
    this.globalSearchValue = target.value;
    this.updateQueryParams();
  }

  /**
   * Handles search on Enter key press
   */
  onSearchEnter(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      this.updateQueryParams();
    }
  }

  /**
   * Updates query parameters and triggers API call
   */
  updateQueryParams() {
    this.updateParams$.next();
  }

  /**
   * Handles department filter change
   */
  onDepartmentChange(event: { value: string | null }) {
    this.selectedDepartment.set(event.value);
    this.updateQueryParams();
  }

  /**
   * Handles role filter change
   */
  onRoleChange(event: { value: string | null }) {
    this.selectedRole.set(event.value);
    this.updateQueryParams();
  }

  /**
   * Handles location filter change
   */
  onLocationChange(event: { value: string | null }) {
    this.selectedLocation.set(event.value);
    this.updateQueryParams();
  }

  /**
   * Clears all filters
   */
  clearFilters() {
    this.selectedDepartment.set(null);
    this.selectedRole.set(null);
    this.selectedLocation.set(null);
    this.globalSearchValue = '';

    // Clear sorting
    this.sortField.set(null);
    this.sortOrder.set(null);

    // Reset to default pagination and trigger API call
    this.queryParams.set({
      pageNumber: 1,
      countPerPage: 10,
    });
  }

  /**
   * Handles lazy loading events (pagination and rows per page changes)
   */
  onPageChange(event: TableLazyLoadEvent) {
    // Extract pagination info from the event
    const first = event.first || 0;
    const rows = event.rows || 10;

    // Calculate the new page number (1-based)
    const newPageNumber = Math.floor(first / rows) + 1;
    const newRowsPerPage = rows;

    const currentParams = this.queryParams();

    // Only update if the values have actually changed to prevent infinite loops
    if (
      currentParams.pageNumber !== newPageNumber ||
      currentParams.countPerPage !== newRowsPerPage
    ) {
      const newParams: TeamsQueryParams = {
        ...currentParams,
        pageNumber: newPageNumber,
        countPerPage: newRowsPerPage,
      };

      this.queryParams.set(newParams);
    }
  }

  /**
   * Handles sorting events for client-side sorting
   */
  onSort(event: { field: string; order: number }) {
    this.sortField.set(event.field);
    this.sortOrder.set(event.order === 1 ? 'asc' : 'desc');
  }

  /**
   * Sorts employees array based on current sort field and order
   */
  private sortEmployees(employees: Resource[]): Resource[] {
    const sortField = this.sortField();
    const sortOrder = this.sortOrder();
    // Fields that should not be sorted
    const nonSortableFields = ['phoneNumber', 'actions'];

    // If no sort is applied or the field is in the excluded list, return as-is
    if (!sortField || !sortOrder || nonSortableFields.includes(sortField)) {
      return employees;
    }

    return [...employees].sort((a, b) => {
      let aValue: unknown = a[sortField as keyof Resource];
      let bValue: unknown = b[sortField as keyof Resource];

      // Handle null/undefined values
      if (aValue === null || aValue === undefined) aValue = '';
      if (bValue === null || bValue === undefined) bValue = '';

      // Convert to string for comparison
      const aString = String(aValue).toLowerCase();
      const bString = String(bValue).toLowerCase();

      if (sortOrder === 'asc') {
        return aString.localeCompare(bString);
      } else {
        return bString.localeCompare(aString);
      }
    });
  }

  /**
   * Handles menu clicks for employee actions.
   */
  onMenuClick(event: Event, employee: Resource, menu: Menu) {
    menu.model = this.getMenuItems(employee);
    menu.show(event);
  }

  /**
   * Generates menu items based on the employee.
   */
  getMenuItems(employee: Resource): MenuItem[] {
    const menuItems: MenuItem[] = [];

    if (employee.deleted) {
      // For deactivated employees, show reactivate option
      menuItems.push({
        label: 'Reactivate Employee',
        icon: 'pi pi-user-plus',
        command: () => {
          this.reactivateEmployee(employee);
        },
      });
    } else {
      // For active employees, show edit and deactivate options
      menuItems.push(
        {
          label: 'Edit Employee',
          icon: 'pi pi-pencil',
          command: () => {
            this.editEmployee(employee);
          },
        },
        {
          label: 'Deactivate Employee',
          icon: 'pi pi-user-minus',
          command: () => {
            this.deactivateEmployee(employee);
          },
        }
      );
    }

    return menuItems;
  }

  /**
   * Opens a dialog to add a new employee.
   */
  openAddEmployeeForm() {
    const dialogRef = this.dialogService.open(EmployeeFormComponent, {
      header: 'Add Employee',
      dismissableMask: false,
      styleClass: 'overflow-hidden w-[90vw] md:w-[80vw]',
      data: {
        isEditMode: false,
        employeeData: null,
      },
    });

    dialogRef.onClose.subscribe((result) => {
      if (result) {
        if (result.type === 'success') {
          this.messageService.add({
            severity: 'success',
            summary: result.summary,
            detail: result.message,
          });
          // Invalidate and refetch teams queries
          this.queryClient.invalidateQueries({
            queryKey: ['getTeamsWithFilters'],
          });
        } else if (result.type === 'error') {
          this.messageService.add({
            severity: 'error',
            summary: result.summary,
            detail: result.message,
          });
        }
      }
    });
  }

  /**
   * Opens a dialog to edit an existing employee.
   */
  editEmployee(employee: Resource) {
    const dialogRef = this.dialogService.open(EmployeeFormComponent, {
      header: 'Edit Employee',
      dismissableMask: false,
      styleClass: 'overflow-hidden w-[90vw] md:w-[80vw]',
      data: {
        isEditMode: true,
        employeeData: employee,
      },
    });

    dialogRef.onClose.subscribe((result) => {
      if (result) {
        if (result.type === 'success') {
          this.messageService.add({
            severity: 'success',
            summary: result.summary,
            detail: result.message,
          });
          // Invalidate and refetch teams queries
          this.queryClient.invalidateQueries({
            queryKey: ['getTeamsWithFilters'],
          });
        } else if (result.type === 'error') {
          this.messageService.add({
            severity: 'error',
            summary: result.summary,
            detail: result.message,
          });
        }
      }
    });
  }

  /**
   * Deactivates an employee.
   */
  deactivateEmployee(employee: Resource) {
    this.confirmationService.confirm({
      header: 'Confirm Deactivation',
      message: `Are you sure you want to deactivate "${employee.name}"?`,
      icon: 'pi pi-exclamation-triangle',
      acceptLabel: 'Yes, Deactivate',
      rejectLabel: 'Cancel',
      accept: () => {
        this.performDeactivation(employee);
      },
      reject: () => {
        // User cancelled the action
      },
    });
  }

  /**
   * Performs the actual deactivation after confirmation
   */
  private async performDeactivation(employee: Resource) {
    try {
      const response = await this.teamsService.deactivateEmployee(employee.id);
      this.messageService.add({
        severity: 'success',
        summary: 'Success',
        detail: `Employee "${employee.name}" has been deactivated successfully.`,
      });
      // Invalidate and refetch teams queries
      this.queryClient.invalidateQueries({
        queryKey: ['getTeamsWithFilters'],
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : 'Failed to deactivate employee.';
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: errorMessage,
      });
    }
  }

  /**
   * Reactivates an employee.
   */
  reactivateEmployee(employee: Resource) {
    this.confirmationService.confirm({
      header: 'Confirm Reactivation',
      message: `Are you sure you want to reactivate "${employee.name}"?`,
      icon: 'pi pi-exclamation-triangle',
      acceptLabel: 'Yes, Reactivate',
      rejectLabel: 'Cancel',
      accept: () => {
        this.performReactivation(employee);
      },
      reject: () => {
        // User cancelled the action
      },
    });
  }

  /**
   * Performs the actual reactivation after confirmation
   */
  private async performReactivation(employee: Resource) {
    try {
      const response = await this.teamsService.reactivateEmployee(employee.id);
      this.messageService.add({
        severity: 'success',
        summary: 'Success',
        detail: `Employee "${employee.name}" has been reactivated successfully.`,
      });
      // Invalidate and refetch teams queries
      this.queryClient.invalidateQueries({
        queryKey: ['getTeamsWithFilters'],
      });
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : 'Failed to reactivate employee.';
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: errorMessage,
      });
    }
  }

  /**
   * Triggered when a file is selected for bulk upload.
   */
  onFileSelected(event: { files: File[] }) {
    const file = event.files?.[0];
    if (!file) return;

    this.confirmationService.confirm({
      header: 'Confirm Upload',
      message: `Are you sure you want to upload "${file.name}" for bulk employee import?`,
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.employeeUploader()?.upload();
      },
      reject: () => {
        this.employeeUploader()?.clear();
      },
    });
  }

  /**
   * Handles the file upload process for bulk employee import.
   */
  handleFileUpload(event: FileUploadHandlerEvent) {
    const file: File = event.files?.[0];
    if (!file) return;

    this.isUploading = true;

    const formData = new FormData();
    formData.append('file', file);

    this.http
      .post(endpoints.teams.bulkUpload, formData)
      .pipe(
        finalize(() => {
          this.isUploading = false;
          this.employeeUploader()?.clear(); // clears uploaded file from UI
        })
      )
      .subscribe({
        next: (res: { message?: string }) => {
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: res?.message || 'Employees imported successfully',
          });
          // Invalidate and refetch teams queries
          this.queryClient.invalidateQueries({
            queryKey: ['getTeamsWithFilters'],
          });
        },
        error: (err: any) => {
          const errorMsg =
            err?.error?.message || err?.message || 'Upload failed.';
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: errorMsg,
          });
        },
      });
  }

  ngOnInit(): void {
    // Set up reactive debounce for query parameter updates
    this.subscription.add(
      this.updateParams$
        .pipe(
          debounceTime(300),
          map(() => {
            const currentParams = this.queryParams();
            return {
              pageNumber: 1, // Reset to first page when filters change
              countPerPage: currentParams.countPerPage,
              search: this.globalSearchValue.trim() || undefined,
              department: this.selectedDepartment() || undefined,
              role: this.selectedRole() || undefined,
              location: this.selectedLocation() || undefined,
            } as TeamsQueryParams;
          })
        )
        .subscribe((newParams) => {
          this.queryParams.set(newParams);
        })
    );
  }

  ngOnDestroy(): void {
    // Clean up subscription to prevent memory leaks
    this.subscription.unsubscribe();
  }

  /**
   * Downloads the standard employees upload template.
   */
  downloadTemplate(): void {
    const link = document.createElement('a');
    link.href = '/standard-templates/Employees_Upload_Template.xlsx';
    link.download = 'Employees_Upload_Template.xlsx';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}
