import { CommonModule } from '@angular/common';
import { Component, computed, signal, viewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ConfirmationService, MenuItem, MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { ChipModule } from 'primeng/chip';
import { DialogModule } from 'primeng/dialog';
import { DialogService } from 'primeng/dynamicdialog';
import { DropdownModule } from 'primeng/dropdown';
import { FileUpload, FileUploadModule } from 'primeng/fileupload';
import { InputTextModule } from 'primeng/inputtext';
import { Menu, MenuModule } from 'primeng/menu';
import { MultiSelectModule } from 'primeng/multiselect';
import { Table, TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { TooltipModule } from 'primeng/tooltip';
import { SkeletonModule } from 'primeng/skeleton';
import { ConfirmDialogModule } from 'primeng/confirmdialog';

import { AuthService } from '../../services/auth/auth-service';
import { DepartmentService } from '../../services/department/department.service';
import { StringUtilsService } from '../../services/stringutils/stringutils.service';
import { Department } from '../../services/department/department.model';
import { TeamsService } from '../../services/teams/teams.service';
import { Resource, TeamsQueryParams } from '../../services/teams/teams.model';

/**
 * Component for displaying and managing employee/team data.
 *
 * This component fetches employee data, displays it in a table, and provides
 * functionality for filtering and interacting with employee records.
 */
@Component({
  selector: 'tms-teams',
  standalone: true,
  imports: [
    InputTextModule,
    ButtonModule,
    TableModule,
    ChipModule,
    MenuModule,
    ToastModule,
    FormsModule,
    TooltipModule,
    FileUploadModule,
    CommonModule,
    DialogModule,
    DropdownModule,
    MultiSelectModule,
    SkeletonModule,
    ConfirmDialogModule,
  ],
  providers: [MessageService, ConfirmationService],
  templateUrl: './teams.component.html',
})
export class TeamsComponent {
  teamsTable = viewChild<Table>('teamsTable');
  employeeUploader = viewChild<FileUpload>('employeeUploader');

  globalSearchValue: string = '';
  skeletonRows = Array(3);
  menuItems: MenuItem[] = [];
  isUploading: boolean = false;
  private searchDebounceTimer: any;

  // Filter signals
  selectedDepartment = signal<string | null>(null);
  selectedRole = signal<string | null>(null);
  selectedLocation = signal<string | null>(null);

  // Query parameters for filtering
  queryParams = signal<TeamsQueryParams>({
    pageNumber: 1,
    countPerPage: 10,
  });

  // Pagination configuration
  readonly paginationConfig = {
    rowsPerPageOptions: [10, 20, 50, 100],
    maxPageLinks: 4, // Show up to 4 page links
  };

  // Computed property for total pages
  totalPages = computed(() => {
    const total = this.totalRecords();
    const perPage = this.queryParams().countPerPage;
    return Math.ceil(total / perPage);
  });

  // Computed property that fetches employee data from API
  employees = computed(() => {
    const query = this.teamsService.getTeamsQuery(this.queryParams());
    const response = query.data();
    return response?.data || [];
  });

  // Computed property for total count
  totalRecords = computed(() => {
    const query = this.teamsService.getTeamsQuery(this.queryParams());
    const response = query.data();
    return response?.count || 0;
  });

  hasError = computed(() => {
    const query = this.teamsService.getTeamsQuery(this.queryParams());
    return query.isError();
  });

  isLoading = computed(() => {
    const query = this.teamsService.getTeamsQuery(this.queryParams());
    return query.isLoading();
  });

  role = computed(() => this.authService.userRole());

  // Store the department query as a readonly property
  readonly allDepartmentsQuery!: ReturnType<
    typeof this.departmentService.listAllDepartmentsQuery
  >;

  departmentHasError = computed(() => this.getDepartmentQuery().isError());

  // Filter options
  departmentOptions = computed(() => {
    const departments = this.allDepartmentsQuery?.data();
    return (
      departments
        ?.filter(
          (dept: Department) =>
            dept.departmentName && dept.departmentName.trim() !== ''
        )
        .map((dept: Department) => ({
          label: dept.departmentName,
          value: dept.departmentName,
        })) || []
    );
  });

  /**
   * Get department query for template access
   */
  getDepartmentQuery() {
    return this.allDepartmentsQuery;
  }

  roleOptions = [
    { label: 'Admin', value: 'admin' },
    { label: 'Manager', value: 'manager' },
    { label: 'Employee', value: 'employee' },
    { label: 'Finance', value: 'finance' },
  ];

  locationOptions = [
    { label: 'Mangalore', value: 'mangalore' },
    { label: 'Bangalore', value: 'bangalore' },
    { label: 'Toronto', value: 'toronto' },
    { label: 'US', value: 'us' },
  ];

  constructor(
    public stringUtilsService: StringUtilsService,
    private dialogService: DialogService,
    private messageService: MessageService,
    private authService: AuthService,
    private confirmationService: ConfirmationService,
    private departmentService: DepartmentService,
    private teamsService: TeamsService
  ) {
    // Initialize the department query
    this.allDepartmentsQuery = this.departmentService.listAllDepartmentsQuery();
  }

  employeeTableColumns = [
    {
      field: 'name',
      header: 'Employee Name',
      sortable: true,
      class: '',
    },
    {
      field: 'email',
      header: 'Email ID',
      sortable: true,
      class: '',
    },
    {
      field: 'phoneNumber',
      header: 'Mobile No.',
      sortable: false,
      class: '',
    },
    {
      field: 'role',
      header: 'Role',
      sortable: true,
      class: '',
    },
    {
      field: 'designation',
      header: 'Designation',
      sortable: true,
      class: '',
    },
    {
      field: 'location',
      header: 'Location',
      sortable: true,
      class: '',
    },
    {
      field: 'actions',
      header: 'Actions',
      sortable: false,
      class: 'text-center',
    },
  ];

  /**
   * Handles input changes and triggers API call with debounce
   */
  onInputChange(event: Event) {
    const target = event.target as HTMLInputElement;
    this.globalSearchValue = target.value;
    this.updateQueryParams();
  }

  /**
   * Handles search on Enter key press
   */
  onSearchEnter(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      this.updateQueryParams();
    }
  }

  /**
   * Updates query parameters and triggers API call
   */
  updateQueryParams() {
    // Clear existing timer
    if (this.searchDebounceTimer) {
      clearTimeout(this.searchDebounceTimer);
    }

    // Set new timer for debouncing
    this.searchDebounceTimer = setTimeout(() => {
      const currentParams = this.queryParams();
      const newParams: TeamsQueryParams = {
        pageNumber: currentParams.pageNumber,
        countPerPage: currentParams.countPerPage,
        search: this.globalSearchValue.trim() || undefined,
        department: this.selectedDepartment() || undefined,
        role: this.selectedRole() || undefined,
        location: this.selectedLocation() || undefined,
      };

      this.queryParams.set(newParams);
      this.refreshQuery();
    }, 300);
  }

  /**
   * Refreshes the query with new parameters
   */
  refreshQuery() {
    // The query will automatically refresh when queryParams changes
    // due to the computed properties
  }

  /**
   * Handles department filter change
   */
  onDepartmentChange(event: any) {
    this.selectedDepartment.set(event.value);
    this.updateQueryParams();
  }

  /**
   * Handles role filter change
   */
  onRoleChange(event: any) {
    this.selectedRole.set(event.value);
    this.updateQueryParams();
  }

  /**
   * Handles location filter change
   */
  onLocationChange(event: any) {
    this.selectedLocation.set(event.value);
    this.updateQueryParams();
  }

  /**
   * Clears all filters
   */
  clearFilters() {
    this.selectedDepartment.set(null);
    this.selectedRole.set(null);
    this.selectedLocation.set(null);
    this.globalSearchValue = '';

    // Clear debounce timer
    if (this.searchDebounceTimer) {
      clearTimeout(this.searchDebounceTimer);
    }

    // Reset to default pagination and trigger API call
    this.queryParams.set({
      pageNumber: 1,
      countPerPage: 10,
    });
  }

  /**
   * Handles page change
   */
  onPageChange(event: any) {
    console.log('Page change event:', event);
    const currentParams = this.queryParams();

    // For lazy loading, event contains first, rows, page, pageCount
    // For regular pagination, event contains page
    const newPageNumber = Math.floor(event.first / event.rows) + 1;

    const newParams = {
      ...currentParams,
      pageNumber: newPageNumber,
    };

    console.log('Loading state isLoading:', this.isLoading());   

    console.log('Updating to page:', newPageNumber, 'New params:', newParams);
    // this.queryParams.set(newParams);
  }

  /**
   * Handles rows per page change
   */
  onRowsPerPageChange(event: any) {
    console.log('Rows per page change event:', event);
    const currentParams = this.queryParams();

    let newRowsPerPage: number;

    if (event.hasOwnProperty('rows')) {
      // Lazy loading event structure
      newRowsPerPage = event.rows;
    } else {
      console.error('Unknown rows per page event structure:', event);
      return;
    }

    const newParams = {
      ...currentParams,
      pageNumber: 1, // Reset to first page when changing rows per page
      countPerPage: newRowsPerPage,
    };

    console.log(
      'Updating rows per page:',
      newRowsPerPage,
      'New params:',
      newParams
    );
    this.queryParams.set(newParams);
  }

  /**
   * Handles menu clicks for employee actions.
   */
  onMenuClick(event: Event, employee: Resource, menu: Menu) {
    menu.model = this.getMenuItems(employee);
    menu.show(event);
  }

  /**
   * Generates menu items based on the employee.
   */
  getMenuItems(employee: Resource): MenuItem[] {
    const menuItems: MenuItem[] = [];

    menuItems.push(
      {
        label: 'Edit Employee',
        icon: 'pi pi-pencil',
        command: () => {
          this.editEmployee(employee);
        },
      },
      {
        label: 'Deactivate Employee',
        icon: 'pi pi-user-minus',
        command: () => {
          this.deactivateEmployee(employee);
        },
      }
    );

    return menuItems;
  }

  /**
   * Opens a dialog to add a new employee.
   */
  openAddEmployeeForm() {
    this.messageService.add({
      severity: 'info',
      summary: 'Info',
      detail: 'Add Employee functionality will be implemented later.',
    });
  }

  /**
   * Opens a dialog to edit an existing employee.
   */
  editEmployee(employee: Resource) {
    this.messageService.add({
      severity: 'info',
      summary: 'Info',
      detail: 'Edit Employee functionality will be implemented later.',
    });
  }

  /**
   * Deactivates an employee.
   */
  deactivateEmployee(employee: Resource) {
    this.messageService.add({
      severity: 'info',
      summary: 'Info',
      detail: 'Deactivate Employee functionality will be implemented later.',
    });
  }

  /**
   * Triggered when a file is selected for bulk upload.
   */
  onFileSelected(event: any) {
    const file = event.files?.[0];
    if (!file) return;

    this.confirmationService.confirm({
      header: 'Confirm Upload',
      message: `Are you sure you want to upload "${file.name}" for bulk employee import?`,
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        this.employeeUploader()?.upload();
      },
      reject: () => {
        this.employeeUploader()?.clear();
      },
    });
  }

  /**
   * Handles the file upload process for bulk employee import.
   */
  async handleFileUpload(event: any) {
    const file: File = event.files?.[0];
    if (!file) return;

    this.isUploading = true;

    try {
      const result = await this.teamsService.bulkUploadEmployees(file);
      this.messageService.add({
        severity: 'success',
        summary: 'Success',
        detail: result.message || 'Employees imported successfully',
      });
    } catch (error) {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: (error as Error).message || 'Upload failed',
      });
    } finally {
      this.isUploading = false;
      this.employeeUploader()?.clear();
    }
  }
}
