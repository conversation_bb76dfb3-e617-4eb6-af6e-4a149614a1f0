<!-- Main container for the component -->
<div class="min-h-screen flex flex-col space-y-6">
  <!-- Filter controls -->
  <div class="flex items-center justify-between flex-wrap gap-3">
    <!-- Left side - Search and filters -->
    <div class="flex items-center flex-wrap gap-3">
      <div
        class="flex items-center px-2 rounded-md border border-neutral-300 bg-white"
      >
        <i class="pi pi-search text-black mr-2"></i>
        <input
          pInputText
          type="text"
          [(ngModel)]="globalSearchValue"
          placeholder="Search Employees"
          (input)="onInputChange($event)"
          (keyup)="onSearchEnter($event)"
          class="w-full max-w-40 border-none rounded-md focus:outline-none ring-0"
        />
      </div>
      <!-- Department filter -->
      <p-dropdown
        placeholder="Select Department"
        filterPlaceholder="Search department"
        [filter]="true"
        [options]="departmentOptions()"
        [ngModel]="selectedDepartment()"
        (onChange)="onDepartmentChange($event)"
        optionLabel="label"
        optionValue="value"
        styleClass="w-48 rounded-lg"
        [resetFilterOnHide]="true"
        [loading]="getDepartmentQuery().isLoading()"
        [emptyMessage]="'No Department Found.'"
        (onClear)="onDepartmentChange({ value: null })"
        [panelStyleClass]="'w-64'"
      >
        <ng-template let-option pTemplate="item">
          <div class="whitespace-normal text-base w-40 truncate">
            {{ option.label }}
          </div>
        </ng-template>
      </p-dropdown>

      <!-- Role filter -->
      <p-dropdown
        placeholder="Select Role"
        [options]="roleOptions"
        [ngModel]="selectedRole()"
        (onChange)="onRoleChange($event)"
        optionLabel="label"
        optionValue="value"
        styleClass="w-40 rounded-lg"
      ></p-dropdown>

      <!-- Location filter -->
      <p-dropdown
        placeholder="Select Location"
        [options]="locationOptions"
        [ngModel]="selectedLocation()"
        (onChange)="onLocationChange($event)"
        optionLabel="label"
        optionValue="value"
        styleClass="w-44 rounded-lg"
      ></p-dropdown>

      <!-- Clear Filters button -->
      <p-button
        label="Clear Filters"
        aria-label="Clear all filters"
        icon="pi pi-filter-slash"
        (click)="clearFilters()"
        outlined="true"
      ></p-button>
    </div>

    <!-- Right side - Action buttons -->
    <div class="flex items-center gap-3">
      @if (role() === 'admin') {
        <!-- Get Standard Template button -->
        <p-button
          label="Get Standard Template"
          aria-label="Download employees upload template"
          icon="pi pi-download"
          (onClick)="downloadTemplate()"
          outlined="true"
        ></p-button>

        <!-- Bulk Upload button -->
        <p-fileUpload
          #employeeUploader
          name="file"
          mode="advanced"
          chooseLabel="Bulk Upload"
          chooseIcon="pi pi-upload"
          accept=".xlsx"
          [disabled]="isUploading"
          [customUpload]="true"
          [auto]="false"
          [maxFileSize]="MAX_FILE_SIZE"
          (onSelect)="onFileSelected($event)"
          (uploadHandler)="handleFileUpload($event)"
          appendTo="body"
          styleClass="upload-menu-item"
          [showUploadButton]="false"
          [showCancelButton]="false"
        />

        <!-- Add Employee button -->
        <p-button
          label="Add Employee"
          aria-label="Add new employee"
          icon="pi pi-plus text-xs"
          (onClick)="openAddEmployeeForm()"
        ></p-button>
      }
    </div>
  </div>

  <!-- Table container for displaying employee data -->
  <div class="w-full bg-white overflow-hidden shadow rounded-xl">
    <div class="px-4 py-2 bg-gray-50 border-b">
      <div class="text-sm text-gray-600 italic flex justify-end">
        <span class="text-red-500 mr-1">* </span> indicates employees who are
        contractors
      </div>
    </div>
    <p-table
      #teamsTable
      [value]="employees() || []"
      [globalFilterFields]="['name', 'email']"
      [sortField]="sortField()"
      [sortOrder]="sortOrder() === 'asc' ? 1 : sortOrder() === 'desc' ? -1 : 0"
      [scrollable]="true"
      scrollHeight="calc(100vh - 320px)"
      [showLoader]="false"
      [loading]="isLoading()"
      [lazy]="true"
      [paginator]="totalRecords() > 10 ? true : false"
      [rows]="queryParams().countPerPage"
      [first]="currentFirst()"
      [totalRecords]="totalRecords()"
      [showPageLinks]="true"
      [showFirstLastIcon]="true"
      [showCurrentPageReport]="true"
      currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
      (onLazyLoad)="onPageChange($event)"
      (onSort)="onSort($event)"
      styleClass="teams-table-dropdown-up"
      [paginatorDropdownAppendTo]="'body'"
    >
      <!-- Table header template -->
      <ng-template pTemplate="header">
        <tr class="bg-transparent">
          @for (col of employeeTableColumns(); track $index) {
            <th
              class="text-sm font-bold bg-primary-50 text-nowrap"
              [ngClass]="col.class"
              [pSortableColumn]="col.sortable ? col.field : undefined"
            >
              {{ col.header }}
              @if (col.sortable) {
                <p-sortIcon [field]="col.field"></p-sortIcon>
              }
            </th>
          }
        </tr>
      </ng-template>

      <!-- Table body template -->
      <ng-template pTemplate="body" let-employee>
        <tr class="text-sm" [class.bg-gray-200]="employee?.deleted">
          <td
            [class.text-primary-500]="!employee?.deleted"
            [class.line-through]="employee?.deleted"
            [class.text-gray-500]="employee?.deleted"
          >
            {{ employee?.name || 'N/A' }}
            @if (employee?.resourceContractor) {
              <span class="text-red-500">*</span>
            }
          </td>

          <td>
            <span
              class="hover:underline"
              [class.text-primary-500]="!employee?.deleted"
              [class.line-through]="employee?.deleted"
              [class.text-gray-500]="employee?.deleted"
            >
              {{ employee?.email || 'N/A' }}
            </span>
          </td>
          <td
            [class.line-through]="employee?.deleted"
            [class.text-gray-500]="employee?.deleted"
          >
            {{ employee?.phoneNumber || 'No contact number' }}
          </td>
          <td
            [class.line-through]="employee?.deleted"
            [class.text-gray-500]="employee?.deleted"
          >
            <span class="capitalize">{{ employee?.role || 'N/A' }}</span>
          </td>
          <td
            [class.line-through]="employee?.deleted"
            [class.text-gray-500]="employee?.deleted"
          >
            {{ employee?.designation || 'N/A' }}
          </td>
          <td
            [class.line-through]="employee?.deleted"
            [class.text-gray-500]="employee?.deleted"
          >
            <span class="capitalize">
              {{
                employee?.location?.toLowerCase() === 'us'
                  ? 'US'
                  : employee?.location || 'N/A'
              }}
            </span>
          </td>
          <!-- Actions column - only visible to admin -->
          @if (role() === 'admin') {
            <td class="text-center">
              <div class="relative inline-block space-x-3">
                <button
                  pButton
                  type="button"
                  icon="pi pi-ellipsis-h"
                  class="p-button-rounded p-button-text p-button-plain"
                  [pTooltip]="'Actions'"
                  tooltipPosition="left"
                  (click)="onMenuClick($event, employee, menu)"
                  style="width: 2.5rem; height: 2.5rem"
                  aria-label="menu"
                ></button>
                <p-menu
                  #menu
                  [model]="menuItems"
                  [popup]="true"
                  [styleClass]="'w-48 shadow-lg border-0 text-sm'"
                  appendTo="body"
                >
                  <ng-template pTemplate="item" let-item>
                    <a
                      class="flex items-center px-4 py-2 text-sm hover:bg-gray-100 cursor-pointer transition-colors duration-150 rounded-md"
                    >
                      <span [class]="item.icon"></span>
                      <span class="ml-2">{{ item.label }}</span>
                    </a>
                  </ng-template>
                </p-menu>
              </div>
            </td>
          }
        </tr>
      </ng-template>

      <ng-template pTemplate="loadingbody">
        @if (isLoading()) {
          @for (_ of skeletonRows; track $index) {
            <tr>
              @for (col of employeeTableColumns(); track $index) {
                <td class="text-sm">
                  <p-skeleton width="100%" height="1.5rem"></p-skeleton>
                </td>
              }
            </tr>
          }
        }
      </ng-template>

      @if (hasError()) {
        <ng-template>
          <tr>
            <td
              [attr.colspan]="employeeTableColumns().length"
              class="text-center text-gray-500 py-4"
            >
              Failed to load employees. Please try again later.
            </td>
          </tr>
        </ng-template>
      }

      <ng-template pTemplate="emptymessage">
        <tr>
          <td
            [attr.colspan]="employeeTableColumns().length"
            class="text-center p-6"
          >
            @if (isLoading()) {
              <p class="text-lg text-gray-500">Loading employees...</p>
            } @else if (hasError()) {
              <p class="text-lg text-red-500">
                Failed to load employees. Please try again.
              </p>
            } @else {
              <p class="text-lg text-gray-500">No employees found.</p>
            }
          </td>
        </tr>
      </ng-template>

      <!-- Custom paginator template -->
      <ng-template pTemplate="paginatorleft" let-state>
        <div class="flex items-center gap-2">
          <span class="text-base text-[#6C757D]"
            >Number of rows/records displayed:</span
          >
          <p-dropdown
            [options]="paginationConfig.rowsPerPageOptions"
            [ngModel]="state.rows"
            (onChange)="onPageChange({ first: 0, rows: $event.value })"
            styleClass="w-18 h-8 text-sm"
            appendTo="body"
          ></p-dropdown>
        </div>
      </ng-template>
    </p-table>
  </div>
</div>

<p-toast [preventOpenDuplicates]="true"></p-toast>
<p-confirmDialog></p-confirmDialog>
