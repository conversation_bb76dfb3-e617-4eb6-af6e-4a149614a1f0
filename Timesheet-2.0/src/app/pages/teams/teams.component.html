<!-- Main container for the component -->
<div class="min-h-screen flex flex-col space-y-6">
  <!-- Filter controls -->
  <div class="flex items-center justify-between flex-wrap gap-3">
    <!-- Left side - Search and filters -->
    <div class="flex items-center flex-wrap gap-3">
      <div
        class="flex items-center px-2 rounded-md border border-neutral-300 bg-white"
      >
        <i class="pi pi-search text-black mr-2"></i>
        <input
          pInputText
          type="text"
          [(ngModel)]="globalSearchValue"
          placeholder="Search Employees"
          (input)="onInputChange($event)"
          (keyup)="onSearchEnter($event)"
          class="w-full max-w-40 border-none rounded-md focus:outline-none ring-0"
        />
      </div>
      <!-- Department filter -->
      <p-dropdown
        placeholder="Select Department"
        filterPlaceholder="Search department"
        [filter]="true"
        [options]="departmentOptions()"
        [ngModel]="selectedDepartment()"
        (onChange)="onDepartmentChange($event)"
        optionLabel="label"
        optionValue="value"
        styleClass="w-48 rounded-lg"
        [resetFilterOnHide]="true"
        [loading]="getDepartmentQuery().isLoading()"
        [emptyMessage]="'No Department Found.'"
        (onClear)="onDepartmentChange({ value: null })"
        [panelStyleClass]="'w-64'"
      >
        <ng-template let-option pTemplate="item">
          <div class="whitespace-normal text-base w-40 truncate">
            {{ option.label }}
          </div>
        </ng-template>
      </p-dropdown>

      <!-- Role filter -->
      <p-dropdown
        placeholder="Select Role"
        [options]="roleOptions"
        [ngModel]="selectedRole()"
        (onChange)="onRoleChange($event)"
        optionLabel="label"
        optionValue="value"
        styleClass="w-40 rounded-lg"
      ></p-dropdown>

      <!-- Location filter -->
      <p-dropdown
        placeholder="Select Location"
        [options]="locationOptions"
        [ngModel]="selectedLocation()"
        (onChange)="onLocationChange($event)"
        optionLabel="label"
        optionValue="value"
        styleClass="w-44 rounded-lg"
      ></p-dropdown>

      <!-- Clear Filters button -->
      <p-button
        label="Clear Filters"
        aria-label="Clear all filters"
        icon="pi pi-filter-slash"
        (click)="clearFilters()"
        outlined="true"
      ></p-button>
    </div>

    <!-- Right side - Action buttons -->
    <div class="flex items-center gap-3">
      <!-- Bulk Upload button -->
      <p-button
        label="Bulk Upload"
        aria-label="Bulk upload employees"
        icon="pi pi-upload"
        (click)="employeeUploader()?.choose()"
        [loading]="isUploading"
      ></p-button>

      <!-- Add Employee button -->
      @if (role() === 'admin') {
        <p-button
          label="Add Employee"
          aria-label="Add new employee"
          icon="pi pi-plus text-xs"
          (onClick)="openAddEmployeeForm()"
        ></p-button>
      }
    </div>
  </div>

  <!-- Table container for displaying employee data -->
  <div class="w-full bg-white overflow-hidden shadow rounded-xl">
    <p-table
      #teamsTable
      [value]="employees() || []"
      [globalFilterFields]="['name', 'email']"
      [sortOrder]="1"
      [scrollable]="true"
      scrollHeight="calc(100vh - 280px)"
      [showLoader]="false"
      [loading]="isLoading()"
      [paginator]="true"
      [rows]="queryParams().countPerPage"
      [totalRecords]="totalRecords()"
      [rowsPerPageOptions]="paginationConfig.rowsPerPageOptions"
      [showPageLinks]="true"
      [showFirstLastIcon]="true"
      [showCurrentPageReport]="true"
      currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
      (onPage)="onPageChange($event)"
    >
      <!-- Table header template -->
      <ng-template pTemplate="header">
        <tr class="bg-transparent">
          @for (col of employeeTableColumns; track $index) {
            <th
              [pSortableColumn]="col.field"
              class="text-sm font-bold bg-primary-50 text-nowrap"
              [ngClass]="col.class"
            >
              {{ col.header }}
              @if (col.sortable) {
                <p-sortIcon [field]="col.field"></p-sortIcon>
              }
            </th>
          }
        </tr>
      </ng-template>

      <!-- Table body template -->
      <ng-template pTemplate="body" let-employee>
        <tr class="text-sm">
          <td class="text-primary-500">
            {{ employee?.name || 'N/A' }}
          </td>
          <td>
            <span class="text-primary-500 hover:underline">
              {{ employee?.email || 'N/A' }}
            </span>
          </td>
          <td>{{ employee?.phoneNumber || 'No contact number' }}</td>
          <td>
            <span class="capitalize">{{ employee?.role || 'N/A' }}</span>
          </td>
          <td>{{ employee?.designation || 'N/A' }}</td>
          <td>
            <span class="capitalize">{{ employee?.location || 'N/A' }}</span>
          </td>
          <td class="text-center">
            <div class="relative inline-block space-x-3">
              <button
                pButton
                type="button"
                icon="pi pi-ellipsis-h"
                class="p-button-rounded p-button-text p-button-plain"
                [pTooltip]="'Actions'"
                tooltipPosition="left"
                (click)="onMenuClick($event, employee, menu)"
                style="width: 2.5rem; height: 2.5rem"
                aria-label="menu"
              ></button>
              <p-menu
                #menu
                [model]="menuItems"
                [popup]="true"
                [styleClass]="'w-48 shadow-lg border-0 text-sm'"
                appendTo="body"
              >
                <ng-template pTemplate="item" let-item>
                  @if (item.isUpload) {
                    <div
                      (keyup)="$event.stopPropagation()"
                      (click)="$event.stopPropagation()"
                    >
                      <p-fileUpload
                        #employeeUploader
                        mode="advanced"
                        name="file"
                        chooseLabel="Upload Employees"
                        chooseIcon="pi pi-upload"
                        accept=".xlsx,.csv"
                        [disabled]="isUploading"
                        [customUpload]="true"
                        [auto]="false"
                        [maxFileSize]="10000000"
                        (onSelect)="onFileSelected($event)"
                        (uploadHandler)="handleFileUpload($event)"
                        styleClass="w-full"
                        appendTo="body"
                        [showUploadButton]="false"
                        [showCancelButton]="false"
                      />
                    </div>
                  } @else {
                    <a
                      class="flex items-center px-4 py-2 text-sm hover:bg-gray-100 cursor-pointer transition-colors duration-150 rounded-md"
                    >
                      <span [class]="item.icon"></span>
                      <span class="ml-2">{{ item.label }}</span>
                    </a>
                  }
                </ng-template>
              </p-menu>
            </div>
          </td>
        </tr>
      </ng-template>

      <ng-template pTemplate="loadingbody">
        @if (isLoading()) {
          @for (_ of skeletonRows; track $index) {
            <tr>
              @for (col of employeeTableColumns; track $index) {
                <td class="text-sm">
                  <p-skeleton width="100%" height="1.5rem"></p-skeleton>
                </td>
              }
            </tr>
          }
        }
      </ng-template>

      @if (hasError()) {
        <ng-template>
          <tr>
            <td colspan="7" class="text-center text-gray-500 py-4">
              Failed to load employees. Please try again later.
            </td>
          </tr>
        </ng-template>
      }

      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="7" class="text-center p-6">
            @if (isLoading()) {
              <p class="text-lg text-gray-500">Loading employees...</p>
            } @else if (hasError()) {
              <p class="text-lg text-red-500">
                Failed to load employees. Please try again.
              </p>
            } @else {
              <p class="text-lg text-gray-500">No employees found.</p>
            }
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</div>

<p-toast [preventOpenDuplicates]="true"></p-toast>
<p-confirmDialog></p-confirmDialog>
