<!-- Profile Page Template -->
<!-- This template is designed to display user profile information in a structured layout. -->

<div class="container mx-auto py-4">
  <h1 class="text-3xl font-bold text-neutral-700 mb-6">Profile</h1>
  <div
    class="profile-container bg-white shadow rounded-lg overflow-hidden h-[calc(100vh-10rem)] lg:h-full overflow-y-auto"
  >
    <div class="p-6">
      <!-- Profile header section -->
      <div class="bg-gray-50 rounded-lg p-6 mb-6">
        <div class="sm:flex sm:items-center sm:justify-between">
          <div class="sm:flex sm:items-center sm:space-x-6 mb-4 sm:mb-0">
            <div class="avatar w-24 h-24 mx-auto sm:mx-0">
              <p-avatar
                [image]="resourceDetails()?.profilePicurl ?? undefined"
                [icon]="resourceDetails()?.profilePicurl ? '' : 'pi pi-user'"
                styleClass="w-full h-full"
                shape="circle"
              ></p-avatar>
            </div>
            <!-- User name and job title section -->
            <div class="text-center sm:text-left mt-4 sm:mt-0">
              <div class="flex space-x-3 items-center">
                <h1 class="text-2xl font-bold text-gray-900">
                  {{
                    stringUtilsService.capitalizeFirstLetter(
                      resourceDetails()?.name
                    )
                  }}
                </h1>
                <p-tag
                  [value]="stringUtilsService.capitalizeFirstLetter(userRole())"
                />
              </div>
              <p class="text-xl text-gray-600">
                {{ resourceDetails()?.jobTitle }}
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Grid layout for contact and job details -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Contact Details Card -->
        <div class="bg-white shadow rounded-lg overflow-hidden">
          <div class="py-4 px-6 border-b">
            <h2 class="text-lg font-semibold">Contact Details</h2>
          </div>
          <div class="py-4 px-6 space-y-4">
            @for (contact of contactDetails(); track contact) {
              <div class="flex items-center space-x-3">
                <i class="text-primary-500 {{ contact.icon }}"></i>
                <span class="text-md">{{ contact.value }}</span>
              </div>
            }
          </div>
        </div>
        <!-- Job Details Card -->
        <div class="bg-white shadow rounded-lg overflow-hidden">
          <div class="py-4 px-6 border-b">
            <h2 class="text-lg font-semibold">Job Details</h2>
          </div>
          <div class="py-4 px-6">
            <dl class="grid grid-cols-1 gap-4 sm:grid-cols-2">
              @for (job of jobDetails(); track job) {
                <div class="sm:col-span-1">
                  <dt class="text-sm font-medium text-gray-500">
                    {{ job.label }}
                  </dt>
                  <dd class="mt-1 text-sm text-gray-900">
                    {{ job.value }}
                  </dd>
                </div>
              }
            </dl>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
