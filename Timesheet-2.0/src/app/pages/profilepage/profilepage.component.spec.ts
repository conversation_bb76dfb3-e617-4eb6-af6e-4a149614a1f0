import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ProfilepageComponent } from './profilepage.component';
import { HttpClient, HttpClientModule } from '@angular/common/http';
import { QueryClient } from '@tanstack/angular-query-experimental';

// Test suite for ProfilepageComponent
describe('ProfilepageComponent', () => {
  let component: ProfilepageComponent;
  let fixture: ComponentFixture<ProfilepageComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ProfilepageComponent, HttpClientModule],
      providers: [QueryClient],
    }).compileComponents();

    fixture = TestBed.createComponent(ProfilepageComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  // Test to check if the component is created successfully
  it('should create', () => {
    expect(component).toBeTruthy();
  });

  // Test to verify that profile header and job title are rendered correctly in the template
  it('should render profile header correctly', () => {
    const compiled = fixture.nativeElement;
    const header = compiled.querySelector('h1');
    const jobTitle = compiled.querySelector('p.text-xl');
    expect(header).toBeTruthy();
    expect(jobTitle).toBeTruthy();
  });

  // Test to ensure contact details are rendered in the template
  it('should render contact details in the template', () => {
    const compiled = fixture.nativeElement;
    const contactIcons = compiled.querySelectorAll('.pi');
    const contactValues = compiled.querySelectorAll('.text-md');

    expect(contactIcons).toBeTruthy();
    expect(contactValues).toBeTruthy();
  });

  // Test to ensure job details are rendered in the template
  it('should render job details in the template', () => {
    const compiled = fixture.nativeElement;
    const jobLabels = compiled.querySelectorAll('dt');
    const jobValues = compiled.querySelectorAll('dd');

    expect(jobLabels).toBeTruthy();
    expect(jobValues).toBeTruthy();
  });
});
