import { Component, computed } from '@angular/core';
import { AvatarModule } from 'primeng/avatar';
import { AvatarGroupModule } from 'primeng/avatargroup';
import { ResourceService } from '../../services/profile/profile.service';
import { COMPANY_NAME } from '../../constants/constant';
import { StringUtilsService } from '../../services/stringutils/stringutils.service';
import { ChipModule } from 'primeng/chip';
import { AuthService } from '../../services/auth/auth-service';
import { TagModule } from 'primeng/tag';

/**
 * ProfilepageComponent
 *
 * This component is responsible for displaying the profile page of a user.
 * It utilizes Angular's standalone component feature and integrates with
 * the PrimeNG library for UI components such as Avatar and AvatarGroup.
 */
@Component({
  selector: 'tms-profilepage',
  standalone: true,
  imports: [AvatarModule, AvatarGroupModule, TagModule],
  templateUrl: './profilepage.component.html',
  styleUrl: './profilepage.component.css',
})
export class ProfilepageComponent {
  constructor(
    private resourceService: ResourceService,
    private authService: AuthService,
    public stringUtilsService: StringUtilsService
  ) {}

  /**
   * It uses the ResourceDetailsQuery from the resourceService.
   */
  resourceDetails = computed(() =>
    this.resourceService.ResourceDetailsQuery.data()
  );

  userRole = computed(() => this.authService.userRole());

  /**
   * Computed property to format job-related details of the user.
   */
  jobDetails = computed(() => {
    const details = this.resourceDetails;
    return [
      {
        label: 'Employee Number',
        value: this.stringUtilsService.capitalizeEmployeeNumber(
          details()?.employeeNo
        ),
      },
      {
        label: 'Department',
        value: details()?.department,
      },
      {
        label: 'Reporting Manager',
        value: details()?.reportingTo,
      },
      {
        label: 'Business Unit',
        value: COMPANY_NAME,
      },
    ];
  });

  /**
   * Computed property to format contact-related details of the user.
   */
  contactDetails = computed(() => {
    const details = this.resourceDetails;
    return [
      {
        icon: 'pi pi-envelope',
        value: details()?.email,
      },
      {
        icon: 'pi pi-phone',
        value: details()?.phoneNumber,
      },
      {
        icon: 'pi pi-map-marker',
        value: this.stringUtilsService.capitalizeFirstLetter(
          details()?.location
        ),
      },
    ];
  });
}
