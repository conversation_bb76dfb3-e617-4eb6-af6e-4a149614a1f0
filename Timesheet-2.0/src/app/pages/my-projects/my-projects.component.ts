import { CommonModule } from '@angular/common';
import {
  Component,
  computed,
  viewChild,
  ViewEncapsulation,
  OnInit,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { ChipModule } from 'primeng/chip';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import { DialogService } from 'primeng/dynamicdialog';
import {
  MultiSelect,
  MultiSelectChangeEvent,
  MultiSelectModule,
} from 'primeng/multiselect';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { SkeletonModule } from 'primeng/skeleton';
import { Table, TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { TooltipModule } from 'primeng/tooltip';
import { ProjectFormComponent } from '../../components/project-form/project-form.component';
import { AuthService } from '../../services/auth/auth-service';
import { ClientService } from '../../services/client/client.service';
import {
  EditProjectByResourceId,
  ProjectByResourceId,
} from '../../services/project/project.model';
import { ProjectsService } from '../../services/project/project.service';
import { ResourceService } from '../../services/resource/resource.service';
import { StringUtilsService } from '../../services/stringutils/stringutils.service';
import { formatHoursToTimeString } from '../../utils/date.utils';

@Component({
  selector: 'tms-my-projects',
  standalone: true,
  imports: [
    FormsModule,
    MultiSelectModule,
    TableModule,
    CommonModule,
    SkeletonModule,
    ChipModule,
    OverlayPanelModule,
    ConfirmDialogModule,
    DialogModule,
    TooltipModule,
    ButtonModule,
    ToastModule,
  ],
  providers: [MessageService, ConfirmationService],
  templateUrl: './my-projects.component.html',
  styleUrls: ['./my-projects.component.css'],
  encapsulation: ViewEncapsulation.None,
})
export class MyProjectsComponent implements OnInit {
  projectsTable = viewChild<Table>('projectsTable');
  clientMultiSelect = viewChild<MultiSelect>('clientMultiSelect');
  skeletonRows = Array(3);
  formatHoursToTimeString = formatHoursToTimeString;

  isEmployeeProjectQueryLoading = computed(() =>
    this.myProjectService.employeeProjectsQuery().isLoading()
  );

  projects = computed(() => {
    return this.myProjectService
      .employeeProjectsQuery()
      .data()
      ?.map((project) => {
        return {
          ...project,
          projectManagerName: this.getProjectManagerName(project),
        };
      });
  });

  hasError = computed(() =>
    this.myProjectService.employeeProjectsQuery().isError()
  );

  role = computed(() => this.authService.userRole());

  columnHeadings = computed(() => {
    const baseColumns = [
      { field: 'projectName', header: 'Project', sortable: true, class: '' },
      { field: 'clientName', header: 'Client', sortable: true, class: '' },
      {
        field: 'projectManagerName',
        header: 'Project Manager',
        sortable: true,
        class: '',
      },
      { field: 'startDate', header: 'Start Date', sortable: true, class: '' },
      { field: 'endDate', header: 'End Date', sortable: true, class: '' },
    ];

    if (this.role() === 'admin') {
      return [
        ...baseColumns,
        {
          field: 'contracts',
          header: 'Contracts',
          sortable: true,
          class: 'text-center',
        },
        {
          field: 'edit',
          header: 'Edit',
          sortable: false,
          class: 'text-center',
        },
      ];
    } else if (this.role() === 'manager') {
      return [
        ...baseColumns,
        {
          field: 'contracts',
          header: 'Contracts',
          sortable: true,
          class: 'text-center',
        },
      ];
    } else {
      return [
        ...baseColumns,
        {
          field: 'totalMinutes',
          header: 'Total Hours',
          sortable: true,
          class: 'text-center',
        },
      ];
    }
  });

  /**
   * Query to fetch list of clients.
   */
  clientsQuery = computed(() => this.clientService.getClientsQuery().data());

  /**
   * Query to fetch list of resources.
   */
  resourcesQuery = computed(() =>
    this.resourceService.getResourcesQuery().data()
  );

  clientFilterOptions = computed(() => {
    const clients = this.clientService.ClientQuery.data();
    if (!clients) return [];
    return clients?.map((client) => client.name);
  });

  selectedClients: string[] = [];

  globalSearchValue: string = '';

  constructor(
    private myProjectService: ProjectsService,
    private clientService: ClientService,
    private dialogService: DialogService,
    private messageService: MessageService,
    private authService: AuthService,
    private resourceService: ResourceService,
    public stringUtilsService: StringUtilsService,
    private router: Router
  ) {}

  ngOnInit() {
    // Ensure clients and resources data is loaded when component initializes
    this.clientService.getClientsQuery().refetch();
    this.resourceService.getResourcesQuery().refetch();
  }

  /**
   * Handles input changes and filters the global data in the table
   * @param event The input event that is triggered when the user types in the input field
   */
  onInputChange(event: Event) {
    const target = event.target as HTMLInputElement;
    const table = this.projectsTable();
    this.globalSearchValue = target.value;
    if (table && target) {
      table.filterGlobal(this.globalSearchValue, 'contains');
    }
  }

  getManagerInfoTooltip(project: ProjectByResourceId): string {
    return (
      project.projectManager
        ?.map(
          (manager) =>
            `${manager.name}\n${manager.email}\n${manager.phoneNumber}`
        )
        .join('\n\n') ?? 'N/A'
    ); // Separate multiple managers with a blank line for readability
  }

  getProjectManagerName(project: ProjectByResourceId): string {
    return project?.projectManager?.length
      ? project.projectManager.map((manager) => manager.name).join(', ')
      : 'N/A';
  }

  navigateToDetails(projectId: string) {
    this.router.navigate(['/dashboard/projects', projectId]);
  }

  /**
   * Handles changes in selected clients filter.
   */
  onClientsFilterChange(event: MultiSelectChangeEvent) {
    this.selectedClients = event.value;
  }

  /**
   * Filters projects based on selected clients and project status.
   */
  filteredProjects = () => {
    const filteredProjectsByClient =
      this.selectedClients.length > 0
        ? this.projects()?.filter((project) =>
            this.selectedClients.includes(project.clientName)
          )
        : this.projects();

    return filteredProjectsByClient;
  };

  /**
   * Clears all filters and resets the global search value.
   */
  clearFilters() {
    this.selectedClients = [];
    const table = this.projectsTable();
    if (table) {
      table.filterGlobal('', 'contains');
    }
    this.globalSearchValue = '';
    this.clientMultiSelect()?.resetFilter();
  }

  openAddProjectForm() {
    const dialogRef = this.dialogService.open(ProjectFormComponent, {
      header: 'Add Project',
      dismissableMask: false,
      styleClass: 'overflow-hidden w-[90vw] md:w-[50vw]',
      data: {
        isEditMode: false,
        projectData: null,
      },
    });
    dialogRef.onClose.subscribe((result) => {
      if (result) {
        if (result.type === 'success') {
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: result.message,
          });
        } else if (result.type === 'error') {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: result.message,
          });
        }
      }
    });
  }

  /**
   * Opens a dialog to edit a project.
   *
   * This function prepares the project data for editing and opens a dialog
   * with the project form. It also handles the dialog's close event to display
   * success or error messages based on the result.
   *
   * @param project The project to be edited, of type `ProjectByResourceId`.
   */
  editProject(project: ProjectByResourceId) {
    const editProjectData: EditProjectByResourceId = {
      client: this.clientsQuery()?.find(
        (client) => client.name === project.clientName
      ),
      projectManagerDetails: this.resourcesQuery()?.find((resource) =>
        project.projectManager?.some((manager) => manager.id === resource.id)
      ),

      ...project,
    };
    const dialogRef = this.dialogService.open(ProjectFormComponent, {
      header: 'Edit Project',
      dismissableMask: false,
      styleClass: 'overflow-hidden w-[90vw] md:w-[50vw]',
      data: {
        isEditMode: true,
        projectData: editProjectData,
      },
    });
    dialogRef.onClose.subscribe((result) => {
      if (result) {
        if (result.type === 'success') {
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: result.message,
          });
        } else if (result.type === 'error') {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: result.message,
          });
        }
      }
    });
  }

  getTotalHours(totalMinutes: number) {
    const formattedHours = formatHoursToTimeString(totalMinutes / 60);
    const [hours, minutes] = formattedHours.split(':');
    return minutes ? `${hours}h ${minutes}m` : `${hours}h`;
  }
}
