<div class="min-h-screen flex flex-col space-y-6">
  <div class="space-y-4">
    @if (role() === 'admin' || role() === 'manager') {
      <div class="flex items-center justify-between flex-wrap gap-2">
        <div
          class="flex items-center p-2 rounded-md border border-neutral-300 bg-white"
        >
          <i class="pi pi-search text-black mr-2"></i>
          <input
            pInputText
            type="text"
            [(ngModel)]="globalSearchValue"
            placeholder="Search Projects"
            (input)="onInputChange($event)"
            class="w-full max-w-xs border-none rounded-md focus:outline-none"
          />
        </div>

        <div class="flex items-center flex-grow flex-wrap gap-4">
          <p-multiSelect
            #clientMultiSelect
            [options]="clientFilterOptions()"
            [(ngModel)]="selectedClients"
            placeholder="Select Clients"
            dropdownIcon="pi pi-filter"
            [filter]="true"
            [showHeader]="true"
            display="chip"
            [style]="{ width: '200px', borderRadius: '0.5rem' }"
            (onChange)="onClientsFilterChange($event)"
            [resetFilterOnHide]="true"
          ></p-multiSelect>
        </div>
        <div class="flex items-center gap-4">
          <p-button
            label="Clear Filters"
            aria-label="Clear all filters"
            icon="pi pi-filter-slash"
            (onClick)="clearFilters()"
            outlined="true"
          ></p-button>
          @if (role() === 'admin') {
            <p-button
              label="Add Project"
              aria-label="Add new project"
              icon="pi pi-plus text-xs"
              (onClick)="openAddProjectForm()"
            ></p-button>
          }
        </div>
      </div>
    } @else {
      <div class="flex items-center justify-start">
        <div
          class="flex items-center p-2 rounded-md border border-neutral-300 bg-white"
        >
          <i class="pi pi-search text-black mr-2"></i>
          <input
            pInputText
            [(ngModel)]="globalSearchValue"
            type="text"
            placeholder="Search Projects"
            (input)="onInputChange($event)"
            class="w-full max-w-xs border-none rounded-md focus:outline-none bg-transparent"
          />
        </div>
      </div>
    }
  </div>

  <div class="w-full bg-white overflow-hidden shadow rounded-xl">
    <p-table
      #projectsTable
      [value]="filteredProjects() ?? []"
      [globalFilterFields]="[
        'projectName',
        'clientName',
        'projectStatus',
        'contactName',
      ]"
      [sortOrder]="1"
      [scrollable]="true"
      scrollHeight="calc(100vh - 200px)"
      [loading]="isEmployeeProjectQueryLoading()"
      [showLoader]="false"
    >
      <ng-template pTemplate="header">
        <tr class="bg-transparent">
          @for (col of columnHeadings(); track $index) {
            <th
              [pSortableColumn]="col.field"
              class="text-sm font-bold bg-primary-50 text-nowrap"
              [ngClass]="col.class"
            >
              {{ col.header }}
              @if (col.sortable) {
                <p-sortIcon [field]="col.field"></p-sortIcon>
              }
            </th>
          }
        </tr>
      </ng-template>

      <ng-template pTemplate="body" let-project>
        <tr class="text-sm">
          @if (role() === 'admin' || role() === 'manager') {
            <td
              class="text-primary-500 cursor-pointer hover:underline"
              (click)="navigateToDetails(project.id)"
            >
              <span>
                {{ project.projectName }}
              </span>
            </td>
          } @else {
            <td class="text-primary-500">
              <span>
                {{ project.projectName }}
              </span>
            </td>
          }
          <td>{{ project.clientName }}</td>

          <td class="text-primary-500 cursor-pointer">
            <span
              pTooltip="{{ getManagerInfoTooltip(project) }}"
              tooltipPosition="top"
              [tooltipStyleClass]="'text-xs text-gray-700 whitespace-pre-wrap'"
            >
              {{ getProjectManagerName(project) }}
            </span>
          </td>
          <td>{{ project.startDate | date }}</td>
          <td>
            @if (project.endDate) {
              {{ project.endDate | date }}
            } @else {
              <span pTooltip="End date is calculated based on Contracts" tooltipPosition="top" class="italic text-gray-500 cursor-help">Pending</span>
            }
          </td>
          @if (role() === 'employee') {
            <td class="text-center">
              {{ getTotalHours(project.totalMinutes) }}
            </td>
          }
          @if (role() === 'admin' || role() === 'manager') {
            <td class="text-center">{{ project.contracts }}</td>
          }
          @if (role() === 'admin') {
            <td class="text-center">
              <div class="relative inline-block">
                <p-button
                  type="button"
                  icon="pi pi-pencil"
                  [text]="true"
                  class="p-button-rounded p-button-text p-button-plain"
                  (click)="editProject(project)"
                  aria-label="edit project"
                ></p-button>
              </div>
            </td>
          }
        </tr>
      </ng-template>
      <ng-template pTemplate="loadingbody">
        @for (_ of skeletonRows; track $index) {
          <tr>
            @for (col of columnHeadings(); track $index) {
              <td class="text-sm">
                <p-skeleton width="100%" height="1.5rem"></p-skeleton>
              </td>
            }
          </tr>
        }
      </ng-template>

      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="8" class="text-center text-gray-500 py-4">
            No records found.
          </td>
        </tr>
      </ng-template>
      @if (hasError()) {
        <ng-template>
          <tr>
            <td colspan="8" class="text-center text-gray-500 py-4">
              Failed to load projects. Please try again later.
            </td>
          </tr>
        </ng-template>
      }
    </p-table>
  </div>
</div>
<p-toast></p-toast>
<p-confirmDialog [appendTo]="'body'"></p-confirmDialog>

