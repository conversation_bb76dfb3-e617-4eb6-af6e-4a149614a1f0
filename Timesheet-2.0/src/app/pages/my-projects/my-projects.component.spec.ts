import { CommonModule } from '@angular/common';
import { provideHttpClient, withFetch } from '@angular/common/http';
import { computed } from '@angular/core';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';
import {
  provideTanStackQuery,
  QueryClient,
} from '@tanstack/angular-query-experimental';
import { ChipModule } from 'primeng/chip';
import { DialogService } from 'primeng/dynamicdialog';
import { TableModule } from 'primeng/table';
import { TooltipModule } from 'primeng/tooltip';
import { ProjectByResourceId } from '../../services/project/project.model';
import { ProjectsService } from '../../services/project/project.service';
import { MyProjectsComponent } from './my-projects.component';

describe('MyProjectsComponent', () => {
  let component: MyProjectsComponent;
  let fixture: ComponentFixture<MyProjectsComponent>;
  let mockMyProjectService: jasmine.SpyObj<ProjectsService>;
  let refetchSpy: jasmine.Spy;

  const mockProjects: ProjectByResourceId[] = [
    {
      id: '1',
      projectName: 'Test Project 1',
      contactName: 'John Doe',
      contactEmail: '<EMAIL>',
      contactPhoneNumber: '************',
      description: 'Test project description',
      startDate: new Date('2023-01-01').toISOString(),
      endDate: new Date('2023-12-31').toISOString(),
      contracts: 2,
      clientName: 'Client A',
      totalMinutes: 160,
      projectStatus: 'active',
      billable: true,
      projectManager: [
        {
          email: '<EMAIL>',
          id: '1',
          name: 'Akshay Bhat',
          typeOfResource: 'projectManager',
          phoneNumber: '9087654321',
        },
      ],
    },
    {
      id: '2',
      projectName: 'Test Project 2',
      contactName: 'Jane Smith',
      contactEmail: '<EMAIL>',
      contactPhoneNumber: '************',
      description: 'Another test project description',
      startDate: new Date('2023-02-01').toISOString(),
      endDate: null,
      clientName: 'Client B',
      totalMinutes: null,
      projectStatus: 'completed',
      billable: false,
      projectManager: [
        {
          email: '<EMAIL>',
          id: '1',
          name: 'Akshay Bhat',
          typeOfResource: 'projectManager',
          phoneNumber: '9087654321',
        },
      ],
    },
  ];

  beforeEach(() => {
    refetchSpy = jasmine.createSpy('refetch');

    mockMyProjectService = jasmine.createSpyObj(
      'MyprojectsService',
      ['getUserRole'],
      {
        employeeProjectsQuery: () => ({
          data: computed(() => mockProjects), // Wrap `mockProjects` in a `computed` to simulate reactivity
          isLoading: computed(() => false), // Simulate query's loading state
          isError: computed(() => false), // Simulate query's error state
          refetch: refetchSpy,
        }),
      }
    );

    TestBed.configureTestingModule({
      imports: [
        MyProjectsComponent,
        NoopAnimationsModule,
        TableModule,
        ChipModule,
        TooltipModule,
        CommonModule,
      ],
      providers: [
        { provide: ProjectsService, useValue: mockMyProjectService },
        provideHttpClient(withFetch()),
        provideTanStackQuery(new QueryClient()),
        DialogService,
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(MyProjectsComponent);
    component = fixture.componentInstance;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should handle loading state', () => {
    component.isEmployeeProjectQueryLoading = computed(() => true);
    fixture.detectChanges();
    expect(component.isEmployeeProjectQueryLoading()).toBe(true);
  });
  it('should handle error state', () => {
    component.hasError = computed(() => true);
    fixture.detectChanges();
    expect(component.hasError()).toBe(true);
  });
});
