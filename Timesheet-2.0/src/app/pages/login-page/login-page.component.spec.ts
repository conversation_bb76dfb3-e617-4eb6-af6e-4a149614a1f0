import { ComponentFixture, TestBed } from '@angular/core/testing';

import { LoginPageComponent } from './login-page.component';
import { By } from '@angular/platform-browser';

/**
 * Test suite for Login Page
 */
describe('LoginPageComponent', () => {
  let component: LoginPageComponent;
  let fixture: ComponentFixture<LoginPageComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [LoginPageComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(LoginPageComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  /**
   * Test case: Verifies the component is created successfully.
   */
  it('should create', () => {
    expect(component).toBeTruthy();
  });

  // Test 2: Background image visibility
  it('should display the background image on large screens', () => {
    const backgroundImage = fixture.debugElement.query(
      By.css('.hidden.w-3\\/4 img')
    );
    expect(backgroundImage).toBeTruthy();
    expect(backgroundImage.nativeElement.getAttribute('src')).toBe(
      'assets/login-background.svg'
    );
  });

  // Test 3: Sign In header text
  it('should display the Sign In header', () => {
    const signInHeader = fixture.debugElement.query(
      By.css('.text-2xl.font-semibold')
    );
    expect(signInHeader).toBeTruthy();
    expect(signInHeader.nativeElement.textContent.trim()).toBe('Sign In');
  });

  // Test 4: Google login button
  it('should display the Google login button with correct elements', () => {
    const googleButton = fixture.debugElement.query(By.css('p-button'));
    expect(googleButton).toBeTruthy();

    const googleIcon = googleButton.query(By.css('img'));
    expect(googleIcon).toBeTruthy();
    expect(googleIcon.nativeElement.getAttribute('src')).toBe(
      'assets/google-logo.svg'
    );

    const buttonText = googleButton.query(By.css('span'));
    expect(buttonText.nativeElement.textContent.trim()).toBe('Google');
  });

  // Test 5: Redirection link for Google login
  it('should have the correct href for the Google login button', () => {
    const googleLoginLink = fixture.debugElement.query(By.css('a'));
    expect(googleLoginLink).toBeTruthy();
    expect(googleLoginLink.nativeElement.getAttribute('href')).toBe(
      'http://localhost:3000/api/v2/auth/google/callback'
    );
  });

  // Test 6: Footer text and logo
  it('should display the footer with correct text and CodeCraft logo', () => {
    const footerText = fixture.debugElement.query(
      By.css('.mt-auto.text-center p')
    );
    expect(footerText).toBeTruthy();
    expect(footerText.nativeElement.textContent.trim()).toBe('from');

    const footerLogo = fixture.debugElement.query(
      By.css('.mt-auto.text-center img')
    );
    expect(footerLogo).toBeTruthy();
    expect(footerLogo.nativeElement.getAttribute('src')).toBe(
      'assets/codecraft-logo.svg'
    );
  });
});
