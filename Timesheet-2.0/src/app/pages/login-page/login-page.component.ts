import { Component, OnInit } from '@angular/core';
import { ButtonModule } from 'primeng/button';
import { environment } from '../../../environments/environment';

/**
 * LoginPageComponent
 *
 * This component serves as the main container for the Login Page. It is a standalone component
 * and allows users to login to the dashboard and redirects to the tracker page only after the
 * authentication is successful
 */
@Component({
  selector: 'tms-login-page',
  standalone: true,
  imports: [ButtonModule],
  templateUrl: './login-page.component.html',
  styleUrl: './login-page.component.css',
})
export class LoginPageComponent {
  isLocalhost: boolean;
  baseUrl: string;

  constructor() {
    this.isLocalhost =
      window.location?.hostname === 'localhost' ||
      window.location?.hostname === '127.0.0.1';

    this.baseUrl = this.isLocalhost
      ? environment.apiUrl
      : window.location.origin;
  }
}
