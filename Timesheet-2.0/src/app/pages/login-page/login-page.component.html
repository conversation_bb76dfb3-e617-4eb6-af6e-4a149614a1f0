<!-- 
  Login Page Template

  This template represents the login page of the application, providing a user-friendly interface for signing in with Google.
  It includes a responsive design with a split-screen layout, showcasing a background image on larger screens and a compact sign-in 
  form on smaller screens. 

  Structure:
  - A visually appealing left section with a background image (hidden on smaller screens).
  - A right section with branding, sign-in options, and company information.
-->

<div class="flex min-h-screen">
  <div class="relative hidden w-3/4 lg:block md:block">
    <img
      src="assets/login-background.svg"
      alt="Workspace background"
      class="absolute inset-0 h-full w-full object-cover"
    />
  </div>
  <div class="flex w-full flex-col items-center justify-between p-8 lg:w-1/2">
    <div class="mb-8 text-center">
      <h1 class="text-4xl">
        <span class="text-primary-500">Time</span> Manage
      </h1>
      <p class="mt-1 text-sm text-neutral-400">TIME MANAGEMENT</p>
    </div>

    <div class="mt-40 space-y-4 flex flex-col">
      <h2 class="text-2xl font-semibold text-center">Sign In</h2>
      <a [attr.href]="baseUrl + '/api/v2/auth/google/callback'">
        <p-button
          class="flex w-full items-center justify-center"
          [size]="'large'"
          styleClass="space-x-2 rounded-lg"
          aria-label="Sign in with Google"
          ><img
            src="assets/google-logo.svg"
            alt="Google logo"
            class="h-6 w-6"
          />
          <span class="font-bold mb-[2px]">Google</span>
        </p-button>
      </a>
    </div>

    <div class="mt-auto text-center">
      <p class="text-sm text-gray-600 mb-2 font-semibold">from</p>
      <img
        src="assets/codecraft-logo.svg"
        alt="CodeCraft Technologies logo"
        class="mx-auto h-9"
      />
    </div>
  </div>
</div>
