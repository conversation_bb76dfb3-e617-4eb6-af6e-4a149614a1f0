import { Component, computed, inject, viewChild } from '@angular/core';
import { InputTextModule } from 'primeng/inputtext';
import { FileUpload, FileUploadModule } from 'primeng/fileupload';
import { endpoints } from '../../endpoints';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ToastModule } from 'primeng/toast';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { HttpClient } from '@angular/common/http';
import { finalize } from 'rxjs/operators';
import { Table, TableModule } from 'primeng/table';
import { Leaves } from '../../services/leave/leave.model';
import { LeaveService } from '../../services/leave/leave.service';
import { SkeletonModule } from 'primeng/skeleton';
import { CommonModule } from '@angular/common';
import { calculateTotalLeaveDays } from '../../utils/leave.utils';
import { LeaveFormData } from '../../services/leave/leave.model';
import { TooltipModule } from 'primeng/tooltip';
import { FormsModule } from '@angular/forms';
import { AuthService } from '../../services/auth/auth-service';
/**
 * LeavesComponent
 *
 * This component is responsible for handling the file upload of leave data.
 * It provides an interface for users to upload files, displays a loading state
 * during the upload process, and shows success or error messages upon completion.
 */
@Component({
  selector: 'tms-leaves',
  standalone: true,
  imports: [
    InputTextModule,
    FileUploadModule,
    ToastModule,
    ConfirmDialogModule,
    TableModule,
    SkeletonModule,
    CommonModule,
    TooltipModule,
    FormsModule,
  ],
  providers: [MessageService, ConfirmationService],
  templateUrl: './leaves.component.html',
  styleUrl: './leaves.component.css',
})
export class LeavesComponent {
  leavesTable = viewChild<Table>('leavesTable');
  leaveUploader = viewChild<FileUpload>('leaveUploader');
  skeletonRows = Array(3);
  uploadUrl = endpoints.leaves.upload;
  isUploading: boolean = false;
  readonly MAX_FILE_SIZE = 10000000;
  globalSearchValue = '';
  calculateTotalLeaveDays = calculateTotalLeaveDays;

  constructor(
    private leaveService: LeaveService,
    private messageService: MessageService,
    private authService: AuthService,
    private confirmationService: ConfirmationService,
    private http: HttpClient
  ) {}

  readonly leavesQuery = inject(LeaveService).listAllLeavesQuery();

  leaves = computed(() => {
    const rawData = this.leavesQuery.data();
    const withNoOfDays =
      rawData?.leaves.map((leave) => ({
        ...leave,
        noOfDays: this.getLeaveDays(leave),
        employeeName: leave.resource?.name,
      })) ?? [];

    return {
      leaves: withNoOfDays,
      totalCount: rawData?.totalCount ?? 0,
    };
  });

  hasError = computed(() => this.leavesQuery.isError());

  role = computed(() => this.authService.userRole());

  leaveTableColumns = [
    {
      field: 'employeeName',
      header: 'Employee(s)',
      sortable: true,
      filter: true,
      class: '',
    },
    {
      field: 'fromDate',
      header: 'Leave Dates',
      sortable: false,
      class: '',
    },
    { field: 'noOfDays', header: 'Number of Days', sortable: true, class: '' },
    {
      field: 'actionTakenOn',
      header: 'Action Taken On',
      sortable: false,
      class: '',
    },
    {
      field: 'approver',
      header: 'Approved By',
      sortable: false,
      filter: true,
      class: '',
    },
  ];

  /**
   * Returns the table instance if available.
   * This is used to access table methods like filterGlobal.
   */
  onInputChange(event: Event) {
    const target = event.target as HTMLInputElement;
    this.globalSearchValue = target.value;
    this.leavesTable()?.filterGlobal(this.globalSearchValue, 'contains');
  }

  /**
   * Handles the deletion of a leave request.
   * Displays a confirmation dialog before proceeding with the deletion.
   *
   * @param leave - The leave request to be deleted.
   */
  onDeleteLeave(leave: Leaves) {
    this.confirmationService.confirm({
      header: 'Confirm Delete',
      message: `Are you sure you want to delete leave for ${leave.resource?.name}?`,
      icon: 'pi pi-exclamation-triangle',
      accept: () => this.deleteLeave(leave.id),
    });
  }
  /**
   * Deletes a leave request by its ID.
   * Displays a success message upon successful deletion or an error message if it fails.
   *
   * @param id - The ID of the leave request to delete.
   */
  deleteLeave(id: string) {
    this.leaveService
      .deleteLeave(id)
      .then(() => {
        this.messageService.add({
          severity: 'success',
          summary: 'Deleted',
          detail: 'Leave deleted successfully.',
        });
        this.leaveService.listAllLeavesQuery().refetch();
      })
      .catch((error: any) => {
        this.messageService.add({
          severity: 'error',
          summary: 'Error',
          detail: error?.message || 'Failed to delete leave.',
        });
      });
  }

  /**
   * Handles the file selection event from the file upload component.
   * It confirms the upload action and starts the upload process if confirmed.
   *
   * @param event - The file selection event containing the selected file.
   */
  onFileSelected(event: any) {
    const file = event.files?.[0];
    if (!file) return;

    this.confirmationService.confirm({
      header: 'Confirm Upload',
      message: `Are you sure you want to upload "${file.name}"`,
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        // Start upload programmatically
        this.leaveUploader()?.upload();
      },
      reject: () => {
        //Clear file if rejected
        this.leaveUploader()?.clear();
      },
    });
  }

  /**
   * Handles the file upload process in advanced custom mode.
   * It sends the file to the server manually and handles success or error responses.
   *
   * @param event - The custom file upload event containing selected files.
   */
  handleFileUpload(event: any) {
    const file: File = event.files?.[0];
    if (!file) return;

    this.isUploading = true;

    const formData = new FormData();
    formData.append('file', file);

    this.http
      .post(this.uploadUrl, formData)
      .pipe(
        finalize(() => {
          this.isUploading = false;
          this.leaveUploader()?.clear(); // clears uploaded file from UI
        })
      )
      .subscribe({
        next: (res: any) => {
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: res?.message || 'Leaves added successfully',
          });
          this.leaveService.listAllLeavesQuery().refetch();
        },
        error: (err: any) => {
          const errorMsg = err?.error?.message || 'Upload failed.';
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: errorMsg,
          });
        },
      });
  }

  /**
   * Calculates the total number of leave days for a given leave request.
   * It uses the utility function to calculate the days based on the leave dates and types.
   *
   * @param leave - The leave request data containing fromDate, toDate, and types of leave.
   * @returns The total number of leave days.
   */
  getLeaveDays(leave: LeaveFormData): number {
    return calculateTotalLeaveDays(
      new Date(leave.fromDate),
      new Date(leave.toDate),
      leave.typeOfLeaveForFrom,
      leave.typeOfLeaveForTo
    );
  }
  /**
   * Applies a global filter to the leaves table based on the input value.
   * This allows for searching across all columns in the table.
   *
   * @param event - The input event containing the search value.
   */
  applyGlobalFilter(event: Event): void {
    const value = (event.target as HTMLInputElement).value;
    this.leavesTable()?.filterGlobal(value, 'contains');
  }
}
