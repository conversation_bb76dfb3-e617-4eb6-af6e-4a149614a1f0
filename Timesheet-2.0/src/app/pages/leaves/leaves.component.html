<!--
 * Template for Leaves Component
 * 
 * - Provides a search bar for filtering leave records.
 * - Includes a file upload button to bulk upload leave data in `.xlsx` format.
 * - Displays notifications on upload success or failure.
 * -->
<div class="flex items-center justify-between flex-wrap gap-2 pb-6">
  <div
    class="flex items-center px-2 rounded-md border border-neutral-300 bg-white"
  >
    <!-- Search Bar -->
    <i class="pi pi-search text-black mr-2"></i>
    <input
      pInputText
      type="text"
      [(ngModel)]="globalSearchValue"
      placeholder="Search Leaves"
      class="w-full max-w-xs border-none rounded-md focus:outline-none ring-0"
      (input)="applyGlobalFilter($event)"
    />
  </div>
  <!-- Action buttons -->
  @if (role() === 'admin') {
    <div class="flex items-center gap-3">
      <!-- Get Standard Template button -->
      <p-button
        label="Get Standard Template"
        aria-label="Download leaves upload template"
        icon="pi pi-download"
        (onClick)="downloadTemplate()"
        outlined="true"
      ></p-button>

      <!-- File Upload Button -->
      <p-fileUpload
        #leaveUploader
        name="file"
        mode="advanced"
        chooseLabel="Bulk Upload"
        chooseIcon="pi pi-upload"
        accept=".xlsx"
        [disabled]="isUploading"
        [customUpload]="true"
        [auto]="false"
        [maxFileSize]="MAX_FILE_SIZE"
        (onSelect)="onFileSelected($event)"
        (uploadHandler)="handleFileUpload($event)"
        appendTo="body"
        styleClass="upload-menu-item"
        [showUploadButton]="false"
        [showCancelButton]="false"
      />
    </div>
  }
</div>

<!-- Table container for displaying leave data -->
<div class="w-full bg-white shadow rounded-xl">
  <p-table
    #leavesTable
    [value]="leaves().leaves"
    [scrollable]="true"
    scrollHeight="calc(100vh - 230px)"
    [paginator]="leaves().totalCount > 10"
    [rows]="paginationState().limit"
    [totalRecords]="leaves().totalCount"
    [showLoader]="false"
    [globalFilterFields]="['employeeName', 'approver']"
    [loading]="isLoading()"
    [lazy]="true"
    [first]="currentFirst()"
    [sortField]="sortField()"
    [sortOrder]="sortOrder() === 'asc' ? 1 : sortOrder() === 'desc' ? -1 : 0"
    (onLazyLoad)="onLazyLoad($event)"
    (onSort)="onSort($event)"
    paginatorPosition="bottom"
    [showCurrentPageReport]="true"
    currentPageReportTemplate="Showing {first} to {last} of {totalRecords} entries"
    [showPageLinks]="true"
    [showFirstLastIcon]="true"
    styleClass="p-datatable-sm"
    [paginatorDropdownAppendTo]="'body'"
  >
    <!-- Table header -->
    <ng-template pTemplate="header">
      <tr class="bg-transparent">
        @for (col of leaveTableColumns; track $index) {
          <th
            [pSortableColumn]="col.sortable ? col.field : undefined"
            class="text-sm font-bold bg-primary-50 text-nowrap"
            [ngClass]="col.class"
          >
            <!-- Approver column with tooltip -->
            @if (col.field === 'approver') {
              {{ col.header }}
              <i
                class="pi pi-info-circle text-blue-400 ml-1 cursor-pointer"
                pTooltip="Some records have CodeCraft as approver due to missing data."
                tooltipPosition="top"
                tooltipStyleClass="text-xs text-neutral-700"
              ></i>
              @if (col.sortable) {
                <p-sortIcon [field]="col.field"></p-sortIcon>
              }
            } @else {
              {{ col.header }}
              @if (col.sortable) {
                <p-sortIcon [field]="col.field"></p-sortIcon>
              }
            }
          </th>
        }
      </tr>
    </ng-template>

    <!-- Table body -->
    <ng-template pTemplate="body" let-leave>
      <tr class="text-sm">
        <td class="text-primary-500">
          {{ leave?.employeeName }}
        </td>
        <td class="whitespace-nowrap text-sm leading-tight">
          <ng-container
            *ngIf="
              leave?.toDate && leave?.toDate > leave?.fromDate;
              else singleDay
            "
          >
            {{ leave?.fromDate | date: 'MMM d' }}
            <span *ngIf="leave?.typeOfLeaveForFrom === 'halfDay'"
              >(0.5Day)</span
            >
            to<br />
            {{ leave?.toDate | date: 'MMM d, y' }}
            <span *ngIf="leave?.typeOfLeaveForTo === 'halfDay'">(0.5Day)</span>
          </ng-container>
          <ng-template #singleDay>
            {{ leave?.fromDate | date: 'MMM d, y' }}
          </ng-template>
        </td>
        <td class="pl-14">{{ getLeaveDays(leave) }}</td>
        <td>{{ leave?.createdAt | date }}</td>
        <td>
          {{ leave?.approver || 'CodeCraft' }}
        </td>
      </tr>
    </ng-template>

    <!-- Loading skeleton -->
    <ng-template pTemplate="loadingbody">
      @for (_ of skeletonRows; track $index) {
        <tr>
          @for (col of leaveTableColumns; track $index) {
            <td class="text-sm">
              <p-skeleton width="100%" height="1.5rem"></p-skeleton>
            </td>
          }
        </tr>
      }
    </ng-template>

    <!-- Error state -->
    @if (hasError()) {
      <ng-template>
        <tr>
          <td colspan="7" class="text-center text-gray-500 py-4">
            Failed to load leave records. Please try again later.
          </td>
        </tr>
      </ng-template>
    }

    <!-- Empty state -->
    <ng-template pTemplate="emptymessage">
      <tr>
        <td colspan="7" class="text-center p-6">
          <p class="text-lg text-gray-500">No leave records found.</p>
        </td>
      </tr>
    </ng-template>

    <!-- Custom paginator template -->
    <ng-template pTemplate="paginatorleft" let-state>
      <div class="flex items-center gap-2">
        <span class="text-base text-[#6C757D]"
          >Number of rows/records displayed:</span
        >
        <p-dropdown
          [options]="rowsPerPageOptions"
          [ngModel]="state.rows"
          (onChange)="onLazyLoad({ first: 0, rows: $event.value })"
          styleClass="w-18 h-8 text-sm"
          appendTo="body"
        ></p-dropdown>
      </div>
    </ng-template>
  </p-table>
</div>

<p-confirmDialog></p-confirmDialog>

<!-- Notifications -->
<p-toast></p-toast>
