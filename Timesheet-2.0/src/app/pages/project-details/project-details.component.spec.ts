import { HttpClient, HttpHandler } from '@angular/common/http';
import { provideHttpClientTesting } from '@angular/common/http/testing';
import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ActivatedRoute } from '@angular/router';
import { QueryClient } from '@tanstack/angular-query-experimental';
import { of } from 'rxjs';
import { ProjectDetailsComponent } from './project-details.component';
import { DialogService } from 'primeng/dynamicdialog';

describe('ProjectDetailsComponent', () => {
  let component: ProjectDetailsComponent;
  let fixture: ComponentFixture<ProjectDetailsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ProjectDetailsComponent],
      providers: [
        HttpClient,
        HttpHandler,
        QueryClient,
        provideHttpClientTesting,
        DialogService,
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: { params: { projectId: '123' } }, // Mock params
            paramMap: of({ get: () => '123' }), // Mock observable
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ProjectDetailsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
