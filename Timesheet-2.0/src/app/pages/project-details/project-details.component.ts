import { Component, computed, signal } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { injectQuery } from '@tanstack/angular-query-experimental';
import { Message, MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { CardModule } from 'primeng/card';
import { DialogModule } from 'primeng/dialog';
import { DialogService } from 'primeng/dynamicdialog';
import { ToastModule } from 'primeng/toast';
import { ContractFormComponent } from '../../components/contract-form/contract-form.component';
import { ContractsTableComponent } from '../../components/contracts-table/contracts-table.component';
import { ProjectInfoCardComponent } from '../../components/project-info-card/project-info-card.component';
import { TimelineHistoryViewComponent } from '../../components/timeline-history-view/timeline-history-view.component';
import { ProjectHistoryResponse } from '../../services/project/project.model';
import { ProjectsService } from '../../services/project/project.service';

/**
 * Component for displaying detailed information about a project.
 *
 * This component retrieves the project ID from the route parameters and
 * uses it to fetch and display project-specific data, including contract
 * details and timeline history.
 */
@Component({
  selector: 'tms-project-details',
  standalone: true,
  imports: [
    CardModule,
    ContractsTableComponent,
    ProjectInfoCardComponent,
    ButtonModule,
    DialogModule,
    ToastModule,
  ],
  providers: [MessageService],
  templateUrl: './project-details.component.html',
  styleUrl: './project-details.component.css',
})
export class ProjectDetailsComponent {
  projectId = signal('');
  project = computed(() =>
    this.projectsService.projectQuery(this.projectId()).data()
  );
  
  constructor(
    private route: ActivatedRoute,
    private projectsService: ProjectsService,
    private messageService: MessageService,
    private dialogService: DialogService
  ) {}

  /**
   * Lifecycle hook that is called after data-bound properties of a component are initialized.
   *
   * This method retrieves the project ID from the route parameters and sets it
   * in the projectId signal if it exists.
   */
  ngOnInit() {
    const projectId = this.route.snapshot.params['projectId'];

    if (projectId) {
      this.projectId.set(projectId);
    }
  }

  openAddContractForm() {
    const dialogRef = this.dialogService.open(ContractFormComponent, {
      data: {
        project: this.project(),
      },
      header: 'Add Contract',
      dismissableMask: false,
      styleClass: 'overflow-hidden w-[90vw] md:w-[50vw]',
    });

    dialogRef.onClose.subscribe((result) => {
      if (result) {
        if (result.type === 'success') {
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: result.message,
          });
        } else if (result.type === 'error') {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: result.message,
          });
        }
      }
    });
  }
}
