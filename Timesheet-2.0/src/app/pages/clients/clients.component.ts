import { CommonModule } from '@angular/common';
import { Component, computed, viewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ConfirmationService, MenuItem, MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { ChipModule } from 'primeng/chip';
import { DialogModule } from 'primeng/dialog';
import { DialogService } from 'primeng/dynamicdialog';
import {
  FileUpload,
  FileUploadHandlerEvent,
  FileUploadModule,
} from 'primeng/fileupload';
import { InputTextModule } from 'primeng/inputtext';
import { Menu, MenuModule } from 'primeng/menu';
import { Table, TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { TooltipModule } from 'primeng/tooltip';
import { ClientFormComponent } from '../../components/client-form/client-form.component';
import { ProjectFormComponent } from '../../components/project-form/project-form.component';
import { endpoints } from '../../endpoints';
import { AuthService } from '../../services/auth/auth-service';
import { ClientResponse } from '../../services/client/client.model';
import { ClientService } from '../../services/client/client.service';
import { StringUtilsService } from '../../services/stringutils/stringutils.service';
import { SkeletonModule } from 'primeng/skeleton';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { HttpClient } from '@angular/common/http';
import { finalize } from 'rxjs/operators';

/**
 * Component for displaying and managing client data.
 *
 * This component fetches client data, displays it in a table, and provides
 * functionality for filtering and interacting with client records.
 */
@Component({
  selector: 'tms-clients',
  standalone: true,
  imports: [
    InputTextModule,
    ButtonModule,
    TableModule,
    ChipModule,
    MenuModule,
    ToastModule,
    FormsModule,
    TooltipModule,
    FileUploadModule,
    CommonModule,
    DialogModule,
    TooltipModule,
    SkeletonModule,
    ToastModule,
    ConfirmDialogModule,
  ],
  providers: [MessageService, ConfirmationService],
  templateUrl: './clients.component.html',
  styleUrl: './clients.component.css',
})
export class ClientsComponent {
  clientsTable = viewChild<Table>('clientsTable');
  clientHolidayUploader = viewChild<FileUpload>('clientHolidayUploader');
  globalSearchValue: string = '';
  skeletonRows = Array(3);
  menuItems: MenuItem[] = [];
  uploadUrl = endpoints.client.uploadHoliday;
  isUploading: boolean = false;
  selectedClientId: string | null = null;
  readonly MAX_FILE_SIZE = 10000000;

  hasError = computed(() => this.clientService.getAllClientsQuery().isError());

  /**
   * Computed property that fetches client data and maps it for display.
   *
   * This property uses the `ClientService` to fetch client data and transforms
   * it to include project counts.
   */
  clients = computed(() =>
    this.clientService
      .getAllClientsQuery()
      .data()
      ?.map((client) => ({
        ...client,
        projects: client._count?.projects,
      }))
  );

  role = computed(() => this.authService.userRole());

  constructor(
    private clientService: ClientService,
    public stringUtilsService: StringUtilsService,
    private dialogService: DialogService,
    private messageService: MessageService,
    private authService: AuthService,
    private confirmationService: ConfirmationService,
    private http: HttpClient
  ) {}

  clientTableColumns = [
    {
      field: 'name',
      header: 'Clients',
      sortable: true,
      class: '',
    },
    {
      field: 'contactName',
      header: 'Contact Person',
      sortable: true,
      class: '',
    },
    {
      field: 'contactInfo',
      header: 'Contact Info',
      sortable: false,
      class: '',
    },
    {
      field: 'address',
      header: 'Location',
      sortable: true,
      class: '',
    },
    {
      field: 'projects',
      header: 'Projects',
      sortable: false,
      class: '',
    },
    {
      field: 'actions',
      header: 'Actions',
      sortable: false,
      class: 'text-center',
    },
  ];

  /**
   * Handles input changes and filters the global data in the table
   * @param event The input event that is triggered when the user types in the input field
   */
  onInputChange(event: Event) {
    const target = event.target as HTMLInputElement;
    const table = this.clientsTable();
    this.globalSearchValue = target.value;
    if (table && target) {
      table.filterGlobal(this.globalSearchValue, 'contains');
    }
  }

  /**
   * Handles menu clicks for client actions.
   *
   * Dynamically sets menu items based on the client status and displays the menu.
   *
   * @param event The click event.
   * @param clientStatus The status of the client.
   * @param menu The menu component.
   */
  onMenuClick(event: Event, client: ClientResponse, menu: Menu) {
    // Dynamically set menu items based on the contract
    menu.model = this.getMenuItems(client);
    menu.show(event);
  }

  /**
   * Generates menu items based on the client status.
   *
   * Returns an array of menu items with actions for activating/deactivating and editing clients.
   *
   * @param clientStatus The status of the client.
   * @returns An array of menu items.
   */
  getMenuItems(client: ClientResponse): MenuItem[] {
    const menuItems: MenuItem[] = [];

    menuItems.push(
      {
        label: 'Edit',
        icon: 'pi pi-pencil',
        command: () => {
          this.editClient(client);
        },
      },
      {
        label: 'Add Project',
        icon: 'pi pi-plus',
        command: () => {
          this.openAddProjectForm(client);
        },
      },
      {
        label: 'Upload Holidays',
        icon: 'pi pi-upload',
        isUpload: true,
        command: () => {},
      }
    );

    return menuItems;
  }

  /**
   * Opens a dialog to create a new project, pre-populating client details.
   *
   * @param {ClientResponse} client - The client's information.
   */
  openAddProjectForm(client: ClientResponse) {
    const clientPrefilledData = {
      client: {
        id: client.id,
        name: client.name,
      },
      contactName: client.contactName.trim(),
      contactEmail: client.contactEmail.trim(),
      contactPhoneNumber: client.contactPhoneNumber.trim(),
    };

    const dialogRef = this.dialogService.open(ProjectFormComponent, {
      header: 'Add Project',
      dismissableMask: false,
      styleClass: 'overflow-hidden w-[90vw] md:w-[50vw]',
      data: {
        isEditMode: false,
        projectData: null,
        clientPrefilledData: clientPrefilledData,
      },
    });

    dialogRef.onClose.subscribe((result) => {
      if (result) {
        if (result.type === 'success') {
          this.messageService.add({
            severity: 'success',
            summary: result.summary,
            detail: result.message,
          });
        } else if (result.type === 'error') {
          this.messageService.add({
            severity: 'error',
            summary: result.summary,
            detail: result.message,
          });
        }
      }
    });
  }

  openAddClientForm() {
    const dialogRef = this.dialogService.open(ClientFormComponent, {
      header: 'Add Client',
      width: '700px',
      dismissableMask: false,
      styleClass: 'overflow-hidden',
      data: {
        isEditMode: false,
        projectData: null,
      },
    });

    dialogRef.onClose.subscribe((result) => {
      if (result) {
        if (result.type === 'success') {
          this.messageService.add({
            severity: 'success',
            summary: result.summary,
            detail: result.message,
          });
        } else if (result.type === 'error') {
          this.messageService.add({
            severity: 'error',
            summary: result.summary,
            detail: result.message,
          });
        }
      }
    });
  }

  /**
   * Triggered when a file is selected for upload.
   * It confirms the upload action and starts the upload process.
   *
   * @param event - The file selection event containing the selected file.
   * @param clientId - The ID of the client associated with the file.
   */
  onFileSelected(event: any, clientId: string) {
    const file = event.files?.[0];
    if (!file) return;

    this.selectedClientId = clientId;

    this.confirmationService.confirm({
      header: 'Confirm Upload',
      message: `Are you sure you want to upload "${file.name}" for this client?`,
      icon: 'pi pi-exclamation-triangle',
      accept: () => {
        // Start upload programmatically
        this.clientHolidayUploader()?.upload();
      },
      reject: () => {
        //Clear file if rejected
        this.clientHolidayUploader()?.clear();
      },
    });
  }

  /**
   * Handles the file upload process.
   * It sends the file to the server and handles success and error responses.
   *
   * @param event - The file upload event containing the selected file.
   */
  handleFileUpload(event: FileUploadHandlerEvent) {
    const file: File = event.files?.[0];
    if (!file || !this.selectedClientId) return;

    this.isUploading = true;

    const formData = new FormData();
    formData.append('file', file);
    this.http
      .post(
        this.uploadUrl.replace(':clientId', this.selectedClientId),
        formData
      )
      .pipe(
        finalize(() => {
          this.isUploading = false;
          this.clientHolidayUploader()?.clear();
        })
      )
      .subscribe({
        next: (res: any) => {
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: res?.message || 'Leaves added successfully',
          });
        },
        error: (err: any) => {
          const errorMsg = err?.error?.message || 'Upload failed.';
          this.isUploading = false;
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: errorMsg,
          });
        },
      });
  }

  /**
   * Opens a dialog to edit an existing client.
   *
   * @param {ClientResponse} client - The client's information to be edited.
   */
  editClient(client: ClientResponse) {
    const dialogRef = this.dialogService.open(ClientFormComponent, {
      header: 'Edit Client',
      dismissableMask: false,
      styleClass: 'overflow-hidden w-[90vw] md:w-[50vw]',
      data: {
        isEditMode: true,
        clientData: client,
      },
    });

    dialogRef.onClose.subscribe((result) => {
      if (result) {
        this.messageService.add({
          severity: result.type,
          summary: result.summary,
          detail: result.message,
        });
      }
    });
  }

  /**
   * Downloads the standard holidays upload template.
   */
  downloadHolidaysTemplate(): void {
    const link = document.createElement('a');
    link.href = '/standard-templates/Holidays_Upload_Template.xlsx';
    link.download = 'Holidays_Upload_Template.xlsx';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}
