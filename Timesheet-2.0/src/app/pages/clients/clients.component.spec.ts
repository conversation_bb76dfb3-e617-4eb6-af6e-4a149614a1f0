import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ClientsComponent } from './clients.component';

/**
 * Test suite for the ClientsComponent.
 *
 * This suite covers basic component creation, input handling, and UI element presence.
 */
describe('ClientsComponent', () => {
  let component: ClientsComponent;
  let fixture: ComponentFixture<ClientsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [ClientsComponent],
    }).compileComponents();

    fixture = TestBed.createComponent(ClientsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  /**
   * Test that the component is created successfully.
   */
  it('should create', () => {
    expect(component).toBeTruthy();
  });

  /**
   * Test that the global search input updates the component's globalSearchValue and filters the table.
   */
  it('should update globalSearchValue and filter table when input changes', () => {
    const inputElement = fixture.nativeElement.querySelector('input');
    inputElement.value = 'Test Client';
    inputElement.dispatchEvent(new Event('input'));

    fixture.detectChanges();

    expect(component.globalSearchValue).toBe('Test Client');
  });

  /**
   * Test that the "Add Client" button is displayed correctly.
   */
  it('should display the Add Client button', () => {
    const buttonElement = fixture.nativeElement.querySelector('p-button');
    expect(buttonElement).toBeTruthy();
    expect(buttonElement.getAttribute('label')).toBe('Add Client');
  });
});
