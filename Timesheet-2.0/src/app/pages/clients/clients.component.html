<!-- eslint-disable @angular-eslint/template/interactive-supports-focus -->
<!-- Main container for the component -->
<div class="min-h-screen flex flex-col space-y-6">
  <div class="flex items-center justify-between flex-wrap gap-2">
    <!-- Search input field -->
    <div
      class="flex items-center px-2 rounded-md border border-neutral-300 bg-white"
    >
      <i class="pi pi-search text-black mr-2"></i>
      <input
        pInputText
        type="text"
        [(ngModel)]="globalSearchValue"
        placeholder="Search Clients"
        (input)="onInputChange($event)"
        class="w-full max-w-xs border-none rounded-md focus:outline-none ring-0"
      />
    </div>
    <!-- Button to add a new client -->

    @if (role() === 'admin') {
      <p-button
        label="Add Client"
        aria-label="Add new client"
        icon="pi pi-plus text-xs"
        (onClick)="openAddClientForm()"
      ></p-button>
    }
  </div>

  <!-- Table container for displaying client data -->
  <div class="w-full bg-white overflow-hidden shadow rounded-xl">
    <p-table
      #clientsTable
      [value]="clients() ?? []"
      [globalFilterFields]="['name', 'contactName']"
      [sortOrder]="1"
      [scrollable]="true"
      scrollHeight="calc(100vh - 200px)"
      [showLoader]="false"
    >
      <!-- Table header template -->
      <ng-template pTemplate="header">
        <tr class="bg-transparent">
          @for (col of clientTableColumns; track $index) {
            <th
              [pSortableColumn]="col.field"
              class="text-sm font-bold bg-primary-50 text-nowrap"
              [ngClass]="col.class"
            >
              {{ col.header }}
              @if (col.sortable) {
                <p-sortIcon [field]="col.field"></p-sortIcon>
              }
            </th>
          }
        </tr>
      </ng-template>
      <!-- Table body template -->
      <ng-template pTemplate="body" let-client>
        <tr class="text-sm">
          <td class="text-primary-500">
            {{ client?.name || 'N/A' }}
          </td>
          <td>{{ client?.contactName || 'N/A' }}</td>
          <td class="flex flex-col">
            <span class="text-primary-500 hover:underline">{{
              client?.contactEmail || 'N/A'
            }}</span>
            <span>{{ client?.contactPhoneNumber || 'No contact number' }}</span>
          </td>
          <td>
            <span
              [pTooltip]="client?.address"
              [tooltipStyleClass]="'text-xs text-gray-700  whitespace-pre-wrap'"
              tooltipPosition="top"
              class="inline-block max-w-36 whitespace-nowrap overflow-hidden text-ellipsis"
            >
              {{ client?.address || 'N/A' }}
            </span>
          </td>
          <td class="text-center">{{ client?.projects || 'N/A' }}</td>
          <td class="text-center">
            <div class="relative inline-block space-x-3">
              <button
                pButton
                type="button"
                icon="pi pi-ellipsis-h"
                class="p-button-rounded p-button-text p-button-plain"
                [pTooltip]="'Actions'"
                tooltipPosition="left"
                (click)="onMenuClick($event, client, menu)"
                style="width: 2.5rem; height: 2.5rem"
                aria-label="menu"
              ></button>
              <p-menu
                #menu
                [model]="menuItems"
                [popup]="true"
                [styleClass]="'w-50 shadow-md border-0 text-sm'"
                appendTo="body"
              >
                <ng-template pTemplate="item" let-item>
                  @if (item.isUpload) {
                    <div
                      (keyup)="$event.stopPropagation()"
                      (click)="$event.stopPropagation()"
                    >
                      <p-fileUpload
                        #clientHolidayUploader
                        mode="advanced"
                        name="file"
                        chooseLabel="Upload Holidays"
                        chooseIcon="pi pi-upload"
                        accept=".xlsx"
                        [disabled]="isUploading"
                        [customUpload]="true"
                        [auto]="false"
                        [maxFileSize]="MAX_FILE_SIZE"
                        (onSelect)="onFileSelected($event, client.id)"
                        (uploadHandler)="handleFileUpload($event)"
                        styleClass="upload-menu-item"
                        appendTo="body"
                        [showUploadButton]="false"
                        [showCancelButton]="false"
                      />
                    </div>
                  } @else {
                    <a class="p-menuitem-link">
                      <span [class]="item.icon"></span>
                      <span class="ml-2">{{ item.label }}</span>
                    </a>
                  }
                </ng-template>
              </p-menu>
            </div>
          </td>
        </tr>
      </ng-template>

      <ng-template pTemplate="loadingbody">
        @for (_ of skeletonRows; track $index) {
          <tr>
            @for (col of clientTableColumns; track $index) {
              <td class="text-sm">
                <p-skeleton width="100%" height="1.5rem"></p-skeleton>
              </td>
            }
          </tr>
        }
      </ng-template>
      @if (hasError()) {
        <ng-template>
          <tr>
            <td colspan="8" class="text-center text-gray-500 py-4">
              Failed to load projects. Please try again later.
            </td>
          </tr>
        </ng-template>
      }
      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="8" class="text-center p-6">
            <p class="text-lg text-gray-500">No tasks found.</p>
          </td>
        </tr>
      </ng-template>
    </p-table>
  </div>
</div>
<p-toast [preventOpenDuplicates]="true"></p-toast>
<p-confirmDialog></p-confirmDialog>
