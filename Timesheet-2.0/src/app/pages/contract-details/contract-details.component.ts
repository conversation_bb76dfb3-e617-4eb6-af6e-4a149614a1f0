import { Component, computed, signal, viewChild } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { ToastModule } from 'primeng/toast';
import { ContractInfoCardComponent } from '../../components/contract-info-card/contract-info-card.component';
import { TasksTableComponent } from '../../components/tasks-table/tasks-table.component';
import { TaskStatus } from '../../services/task/task.model';
import { TaskService } from '../../services/task/task.service';
import { DialogService } from 'primeng/dynamicdialog';
import { TaskFormComponent } from '../../components/task-form/task-form.component';
import { ContractService } from '../../services/contract/contract.service';
import { ContractStatus } from '../../services/contract/contract.model';

/**
 * Component responsible for displaying contract details and its history.
 */
@Component({
  selector: 'tms-contract-details',
  standalone: true,
  imports: [
    ContractInfoCardComponent,
    TasksTableComponent,
    ButtonModule,
    ToastModule,
    ConfirmDialogModule,
  ],
  templateUrl: './contract-details.component.html',
  styleUrl: './contract-details.component.css',
  providers: [MessageService, ConfirmationService],
})
export class ContractDetailsComponent {
  contractId = signal('');
  projectId = signal('');

  tasksTableComponent = viewChild.required<TasksTableComponent>('tasksTable');

  selectedTaskIds = computed(() => this.getSelectedTaskIds());
  contractsQuery = computed(() =>
    this.contractService.contractsQuery(this.contractId())
  );

  constructor(
    private messageService: MessageService,
    private route: ActivatedRoute,
    private taskService: TaskService,
    private confirmationService: ConfirmationService,
    private contractService: ContractService,
    private dialogService: DialogService
  ) {}

  /**
   * This method retrieves the project ID and contract ID from the route parameters and sets it
   * in the signal if it exists.
   */
  ngOnInit() {
    const params = this.route.snapshot.params;
    const projectId = params['projectId'];
    const contractId = params['contractId'];

    if (projectId) {
      this.projectId.set(projectId);
    }
    if (contractId) {
      this.contractId.set(contractId);
    }
  }

  /**
   * Retrieves the IDs of selected tasks from the tasks table component.
   * Processes the currently selected tasks and returns an object
   * containing two arrays:
   * - allTaskIds: An array of IDs for all selected tasks.
   * - incompleteTaskIds: An array of IDs for tasks that are not completed or inactive.
   * @returns An object containing arrays of task IDs.
   */
  getSelectedTaskIds() {
    const selectedTasks = this.tasksTableComponent().selectedTasks();

    return selectedTasks.reduce<{
      allTaskIds: string[];
      incompleteTaskIds: string[];
    }>(
      (accumulator, task) => {
        accumulator.allTaskIds.push(task.id);
        if (
          task.taskStatus !== TaskStatus.completed &&
          task.taskStatus !== TaskStatus.inActive
        ) {
          accumulator.incompleteTaskIds.push(task.id);
        }
        return accumulator;
      },
      { allTaskIds: [], incompleteTaskIds: [] }
    );
  }

  /**
   * Prompts the user with a confirmation dialog to complete the given tasks.
   * If confirmed, it triggers a mutation to update the task statuses to "completed,"
   * refreshes the task list, and displays a success or error toast message.
   *
   * @param taskIds - An array of IDs of the tasks to be completed.
   */
  completeTasks(taskIds: string[]) {
    const updateTaskStatusMutation = this.taskService.updateTaskStatusQuery();

    this.confirmationService.confirm({
      message: `Are you sure you want to complete ${taskIds.length} task(s)?`,

      header: 'Complete Task',
      acceptButtonStyleClass: 'p-button-success',
      rejectButtonStyleClass: 'p-button-primary',
      acceptIcon: 'none',
      rejectIcon: 'none',
      acceptLabel: 'Complete',
      rejectLabel: 'Cancel',
      icon: 'pi pi-exclamation-circle text-base',
      defaultFocus: 'none',
      accept: () =>
        updateTaskStatusMutation.mutate(
          { taskIds, status: 'completed' },
          {
            onSuccess: (data) => {
              this.tasksTableComponent().selectedTasks.set([]);
              this.taskService
                .tasksQuery(this.projectId(), this.contractId())
                .refetch();
              this.messageService.add({
                severity: 'success',
                summary: 'Task(s) Completed',
                detail: data.message,
              });
            },
            onError: (error) => {
              this.messageService.add({
                severity: 'error',
                summary: 'Error',
                detail: error.message,
              });
            },
          }
        ),
    });
  }

  /**
   * Prompts the user with a confirmation dialog to delete the specified tasks.
   * If confirmed, it triggers a mutation to delete the tasks, refreshes the task list,
   * and displays a success or error message based on the operation's result.
   *
   * @param taskIds - An array of IDs of the tasks to be deleted.
   */
  deleteTasks(taskIds: string[]) {
    const deleteContractMutation = this.taskService.deleteTasksQuery();

    this.confirmationService.confirm({
      message: `Are you sure you want to delete ${taskIds.length} task(s)?`,

      header: 'Delete Task',
      acceptButtonStyleClass: 'p-button-danger',
      rejectButtonStyleClass: 'p-button-primary',
      acceptIcon: 'none',
      rejectIcon: 'none',
      acceptLabel: 'Delete',
      rejectLabel: 'Cancel',
      icon: 'pi pi-exclamation-circle text-base',
      defaultFocus: 'none',
      accept: () =>
        deleteContractMutation.mutate(taskIds, {
          onSuccess: (data) => {
            this.tasksTableComponent().selectedTasks.set([]);
            this.taskService
              .tasksQuery(this.projectId(), this.contractId())
              .refetch();
            this.messageService.add({
              severity: 'success',
              summary: 'Tasks Deleted',
              detail: data.message,
            });
          },
          onError: (error) => {
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail: error.message,
            });
          },
        }),
    });
  }

  /**
   * Opens the Add Task Form dialog.
   * This method utilizes the dialog service to present the TaskFormComponent
   * for creating a new task associated with a specific contract and project.
   */
  openAddTaskForm() {
    const dialogRef = this.dialogService.open(TaskFormComponent, {
      data: {
        contractId: this.contractId(),
        projectId: this.projectId(),
        isEditMode: false,
        customContractId: this.contractsQuery().data()?.customContractId,
      },
      header: 'Add Task',
      dismissableMask: false,
      styleClass: 'overflow-hidden w-[90vw] md:w-[50vw]',
    });

    dialogRef.onClose.subscribe((result) => {
      if (result) {
        if (result.type === 'success') {
          this.messageService.add({
            severity: 'success',
            summary: 'Success',
            detail: result.message,
          });
        } else if (result.type === 'error') {
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail: result.message,
          });
        }
      }
    });
  }

  addTask() {
    if (
      this.contractsQuery().data()?.contractStatus !== ContractStatus.active
    ) {
      this.messageService.add({
        severity: 'error',
        summary: 'Error',
        detail: 'Cannot add task: The contract is currently inactive.',
      });
    } else {
      this.openAddTaskForm();
    }
  }
}
