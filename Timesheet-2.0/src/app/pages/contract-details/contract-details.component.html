<div
  class="contract-details-container overflow-hidden overflow-y-auto h-[calc(100vh-7rem)]"
>
  <!-- Display contract information card with the provided contract ID -->
  <tms-contract-info-card [contractId]="contractId()"></tms-contract-info-card>

  <div class="w-full flex items-center justify-between mt-8 mb-3">
    <h2 class="text-2xl font-bold text-neutral-600 ms-2">Tasks</h2>
    <div class="flex gap-2">
      @if (selectedTaskIds().allTaskIds.length) {
        <p-button
          severity="danger"
          (onClick)="deleteTasks(selectedTaskIds().allTaskIds)"
          ariaLabel="delete-selected-tasks"
        >
          Delete ({{ selectedTaskIds().allTaskIds.length }})
        </p-button>
      }

      @if (selectedTaskIds().incompleteTaskIds.length) {
        <p-button
          (onClick)="completeTasks(selectedTaskIds().incompleteTaskIds)"
          ariaLabel="complete-selected-tasks"
        >
          Complete ({{ selectedTaskIds().incompleteTaskIds.length }})
        </p-button>
      }
      <p-button
        label="Add Task"
        icon="pi pi-plus"
        (onClick)="addTask()"
      ></p-button>
    </div>
  </div>
  <tms-tasks-table
    #tasksTable
    [contractId]="contractId()"
    [projectId]="projectId()"
    [contractStatus]="contractsQuery().data()?.contractStatus ?? ''"
  ></tms-tasks-table>
</div>
<p-confirmDialog [appendTo]="'body'"></p-confirmDialog>
<p-toast [preventOpenDuplicates]="true"></p-toast>
