import { ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { ContractDetailsComponent } from './contract-details.component';
import { TimelineHistoryViewComponent } from '../../components/timeline-history-view/timeline-history-view.component';
import { HttpClient, HttpHandler } from '@angular/common/http';
import { QueryClient } from '@tanstack/angular-query-experimental';
import { ActivatedRoute } from '@angular/router';
import { of } from 'rxjs';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { DialogService } from 'primeng/dynamicdialog';
import { ButtonModule } from 'primeng/button';
import { computed } from '@angular/core';

describe('ContractDetailsComponent', () => {
  let component: ContractDetailsComponent;
  let fixture: ComponentFixture<ContractDetailsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [
        ContractDetailsComponent,
        TimelineHistoryViewComponent,
        ButtonModule,
        BrowserAnimationsModule,
      ],
      providers: [
        HttpClient,
        HttpHandler,
        DialogService,
        QueryClient,
        {
          provide: ActivatedRoute,
          useValue: {
            snapshot: { params: { projectId: '123' } }, // Mock params
            paramMap: of({ get: () => '123' }), // Mock observable
          },
        },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(ContractDetailsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should display delete button when there are selected task IDs', () => {
    component.selectedTaskIds = computed(() => ({
      allTaskIds: ['task1', 'task2'],
      incompleteTaskIds: ['task3'],
    }));

    // Trigger change detection
    fixture.detectChanges();

    // Find the delete button using p-button with severity="danger"
    const deleteButton = fixture.debugElement.query(
      By.css('p-button[severity="danger"]')
    );

    // Check if button exists
    expect(deleteButton).toBeTruthy();

    // Check button text
    const buttonText = deleteButton.nativeElement.textContent.trim();
    expect(buttonText).toContain('Delete (2)');
  });

  it('should display complete button when there are incomplete task IDs', () => {
    component.selectedTaskIds = computed(() => ({
      allTaskIds: ['task1', 'task2'],
      incompleteTaskIds: ['task3'],
    }));

    // Trigger change detection
    fixture.detectChanges();

    // Find the complete button (p-button without danger severity)
    const completeButton = fixture.debugElement.query(
      By.css('p-button[ariaLabel="complete-selected-tasks"]')
    );

    // Check if button exists
    expect(completeButton).toBeTruthy();

    // Check button text
    const buttonText = completeButton.nativeElement.textContent.trim();
    expect(buttonText).toContain('Complete (1)');
  });
});
