<div class="min-h-screen flex flex-col space-y-6">
  <!-- Charts Section -->
  <div class="space-y-6">
    <!-- Date Range Selector -->
    <div class="flex items-center justify-between">
      <h2 class="text-xl font-semibold text-gray-800">
        Employee Utilization & Leave Overview
      </h2>
      <div class="flex items-center gap-4">
        <p-dropdown
          #dateRangeDropdown
          [options]="dateRangeOptions"
          [(ngModel)]="selectedDateRange"
          (onChange)="onDateRangeChange($event)"
          optionLabel="label"
          optionValue="value"
          placeholder="Select Date Range"
          styleClass="w-48 rounded-lg"
        />
        @if (selectedDateRange() === 'customRange') {
          <div class="flex items-center gap-2">
            <p-calendar
              [(ngModel)]="customStartDate"
              placeholder="Start Date"
              dateFormat="dd/mm/yy"
              styleClass="w-32 rounded-lg"
            />
            <span class="text-gray-500">to</span>
            <p-calendar
              [(ngModel)]="customEndDate"
              placeholder="End Date"
              dateFormat="dd/mm/yy"
              styleClass="w-32 rounded-lg"
            />
            <p-button
              label="Apply"
              icon="pi pi-check"
              (onClick)="applyCustomDateRange()"
              styleClass="p-button-sm"
            />
          </div>
        }
      </div>
    </div>

    <!-- Charts Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
     
      <!-- Chart 1: Organization Billability (Pie) -->
      <div class="bg-white p-6 max-h-96 rounded-xl shadow-md">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">
          Organization Billability
        </h3>
        @if (isUtilizationStatsLoading()) {
          <div class="flex items-center justify-center h-64">
            <p-skeleton
              width="200px"
              height="200px"
              shape="circle"
            ></p-skeleton>
          </div>
        } @else if (utilizationStatsError()) {
          <div class="flex items-center justify-center h-64 text-red-500">
            Failed to load organization data
          </div>
        } @else {
          <p-chart
            type="doughnut"
            [data]="orgBillabilityChartData()"
            [options]="chartOptions"
            [plugins]="[centerTextPlugin]"
            styleClass="w-full h-64"
          ></p-chart>
        }
      </div>

      <!-- Chart 2: Department Wise Billability -->
      <div class="bg-white p-6 rounded-xl max-h-96 shadow-md">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">
          Department Billability
        </h3>
        <div class="space-y-3 max-h-72 overflow-y-auto">
          @if (isUtilizationStatsLoading()) {
            <div class="flex items-center justify-center py-8">
              <p-skeleton width="100%" height="2rem"></p-skeleton>
            </div>
          } @else if (utilizationStatsError()) {
            <div class="flex items-center justify-center py-8 text-red-500">
              Failed to load department data
            </div>
          } @else if (getCurrentChartData()?.departmentWise) {
            @for (
              dept of getCurrentChartData()!.departmentWise;
              track dept.department
            ) {
              <div class="space-y-2">
                <div class="flex items-center justify-between">
                  <span class="text-sm font-medium text-gray-700">{{
                    dept.department
                  }}</span>
                  <span class="text-xs text-gray-500"
                    >{{ dept.total }} employees</span
                  >
                </div>
                <div class="flex gap-1 h-2">
                  <div
                    class="bg-[#C17EDB] rounded-l-full transition-all duration-300"
                    [style.width.%]="(dept.billable / dept.total) * 100"
                    [title]="'Billable: ' + dept.billable"
                  ></div>
                  <div
                    class="bg-[#C39032] transition-all duration-300"
                    [style.width.%]="
                      (dept.partiallyBillable / dept.total) * 100
                    "
                    [title]="'Partially Billable: ' + dept.partiallyBillable"
                  ></div>
                  <div
                    class="bg-[#D65D3C] rounded-r-full transition-all duration-300"
                    [style.width.%]="(dept.nonBillable / dept.total) * 100"
                    [title]="'Non Billable: ' + dept.nonBillable"
                  ></div>
                </div>
              </div>
            }
          } @else {
            <div class="flex items-center justify-center py-8 text-gray-500">
              No department data available
            </div>
          }
        </div>
      </div>

      <!-- Chart 3: Leave Statistics -->
      <div class="bg-white p-6 max-h-96 rounded-xl shadow-md">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">
          Leave Statistics
        </h3>
        @if (isUtilizationStatsLoading()) {
          <div class="flex items-center justify-center h-64">
            <p-skeleton
              width="200px"
              height="200px"
              shape="circle"
            ></p-skeleton>
          </div>
        } @else if (utilizationStatsError()) {
          <div class="flex items-center justify-center h-64 text-red-500">
            Failed to load leave statistics
          </div>
        } @else {
          <p-chart
            type="doughnut"
            [data]="leaveStatsChartData()"
            [options]="chartOptions"
            styleClass="w-full h-64"
          ></p-chart>
        }
      </div>
    </div>
  </div>

  <!-- Existing Filters Section -->
  <div class="space-y-4">
    <div class="flex items-center justify-between flex-wrap gap-2">
      <div
        class="flex items-center p-2 rounded-md border border-neutral-300 bg-white"
      >
        <i class="pi pi-search text-black mr-2"></i>
        <input
          pInputText
          type="text"
          [(ngModel)]="globalSearchValue"
          placeholder="Search Projects"
          (input)="onInputChange($event)"
          class="w-full max-w-xs border-none rounded-md focus:outline-none"
        />
      </div>

      <div class="flex items-center flex-grow flex-wrap gap-4">
        <p-multiSelect
          #clientMultiSelect
          [options]="clientFilterOptions()"
          [(ngModel)]="selectedClients"
          placeholder="Select Clients"
          dropdownIcon="pi pi-filter"
          [filter]="true"
          [showHeader]="true"
          display="chip"
          [style]="{ width: '200px', borderRadius: '0.5rem' }"
          (onChange)="onClientsFilterChange($event)"
          [resetFilterOnHide]="true"
        ></p-multiSelect>
      </div>
      <div class="flex items-center gap-4">
        <p-button
          label="Clear Filters"
          aria-label="Clear all filters"
          icon="pi pi-filter-slash"
          (onClick)="clearFilters()"
          outlined="true"
        ></p-button>
      </div>
    </div>
  </div>

  <!-- Existing Table Section -->
  <div class="w-full bg-white overflow-hidden shadow rounded-xl">
    <p-table
      #projectsTable
      [value]="filteredProjects() ?? []"
      [globalFilterFields]="['projectName', 'clientName', 'projectStatus']"
      [sortOrder]="1"
      [scrollable]="true"
      scrollHeight="calc(100vh - 200px)"
      [loading]="isEmployeeProjectQueryLoading()"
      [showLoader]="false"
    >
      <ng-template pTemplate="header">
        <tr class="bg-transparent">
          @for (col of columnHeadings(); track $index) {
            <th
              class="text-sm font-bold bg-primary-50 text-nowrap"
              [ngClass]="col.class"
              [pSortableColumn]="col.sortable ? col.field : undefined"
            >
              {{ col.header }}
              @if (col.sortable) {
                <p-sortIcon [field]="col.field"></p-sortIcon>
              }
            </th>
          }
        </tr>
      </ng-template>

      <ng-template pTemplate="body" let-project>
        <tr class="text-sm">
          <td
            class="text-primary-500 cursor-pointer hover:underline"
            (click)="navigateToDetails(project.id)"
          >
            <span>
              {{ project.projectName }}
            </span>
          </td>
          <td>{{ project.clientName }}</td>
          <td class="text-primary-500 cursor-pointer">
            <span
              pTooltip="{{ getManagerInfoTooltip(project) }}"
              tooltipPosition="top"
              [tooltipStyleClass]="'text-xs text-gray-700 whitespace-pre-wrap'"
            >
              {{ getProjectManagerName(project) }}
            </span>
          </td>
          <td>
            <span
              class="block max-w-xs truncate text-ellipsis whitespace-nowrap overflow-hidden"
              pTooltip="{{ project.technologies }}"
              tooltipPosition="top"
              [tooltipStyleClass]="'text-xs text-gray-700 whitespace-pre-wrap'"
            >
              {{ project.technologies }}
            </span>
          </td>
          <td>
            <p-chip
              [label]="project.projectStatus.toUpperCase()"
              [styleClass]="
                getStatusChipStyle(project.projectStatus.toUpperCase())
              "
            >
            </p-chip>
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="loadingbody">
        @for (_ of skeletonRows; track $index) {
          <tr>
            @for (col of columnHeadings(); track $index) {
              <td class="text-sm">
                <p-skeleton width="100%" height="1.5rem"></p-skeleton>
              </td>
            }
          </tr>
        }
      </ng-template>

      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="8" class="text-center text-gray-500 py-4">
            No records found.
          </td>
        </tr>
      </ng-template>
      @if (hasError()) {
        <ng-template>
          <tr>
            <td colspan="8" class="text-center text-gray-500 py-4">
              Failed to load projects. Please try again later.
            </td>
          </tr>
        </ng-template>
      }
    </p-table>
  </div>
</div>
