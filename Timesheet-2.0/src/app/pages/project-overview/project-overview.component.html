<div class="min-h-screen flex flex-col space-y-6">
  <div class="space-y-4">
    <div class="flex items-center justify-between flex-wrap gap-2">
      <div
        class="flex items-center p-2 rounded-md border border-neutral-300 bg-white"
      >
        <i class="pi pi-search text-black mr-2"></i>
        <input
          pInputText
          type="text"
          [(ngModel)]="globalSearchValue"
          placeholder="Search Projects"
          (input)="onInputChange($event)"
          class="w-full max-w-xs border-none rounded-md focus:outline-none"
        />
      </div>

      <div class="flex items-center flex-grow flex-wrap gap-4">
        <p-multiSelect
          #clientMultiSelect
          [options]="clientFilterOptions()"
          [(ngModel)]="selectedClients"
          placeholder="Select Clients"
          dropdownIcon="pi pi-filter"
          [filter]="true"
          [showHeader]="true"
          display="chip"
          [style]="{ width: '200px', borderRadius: '0.5rem' }"
          (onChange)="onClientsFilterChange($event)"
          [resetFilterOnHide]="true"
        ></p-multiSelect>
      </div>
      <div class="flex items-center gap-4">
        <p-button
          label="Clear Filters"
          aria-label="Clear all filters"
          icon="pi pi-filter-slash"
          (onClick)="clearFilters()"
          outlined="true"
        ></p-button>
      </div>
    </div>
  </div>

  <div class="w-full bg-white overflow-hidden shadow rounded-xl">
    <p-table
      #projectsTable
      [value]="filteredProjects() ?? []"
      [globalFilterFields]="['projectName', 'clientName', 'projectStatus']"
      [sortOrder]="1"
      [scrollable]="true"
      scrollHeight="calc(100vh - 200px)"
      [loading]="isEmployeeProjectQueryLoading()"
      [showLoader]="false"
    >
      <ng-template pTemplate="header">
        <tr class="bg-transparent">
          @for (col of columnHeadings(); track $index) {
            <th
              [pSortableColumn]="col.field"
              class="text-sm font-bold bg-primary-50 text-nowrap"
              [ngClass]="col.class"
            >
              {{ col.header }}
              @if (col.sortable) {
                <p-sortIcon [field]="col.field"></p-sortIcon>
              }
            </th>
          }
        </tr>
      </ng-template>

      <ng-template pTemplate="body" let-project>
        <tr class="text-sm">
          <td
            class="text-primary-500 cursor-pointer hover:underline"
            (click)="navigateToDetails(project.id)"
          >
            <span>
              {{ project.projectName }}
            </span>
          </td>
          <td>{{ project.clientName }}</td>
          <td class="text-primary-500 cursor-pointer">
            <span
              pTooltip="{{ getManagerInfoTooltip(project) }}"
              tooltipPosition="top"
              [tooltipStyleClass]="'text-xs text-gray-700 whitespace-pre-wrap'"
            >
              {{ getProjectManagerName(project) }}
            </span>
          </td>
          <td>
            <span
              class="block max-w-xs truncate text-ellipsis whitespace-nowrap overflow-hidden"
              pTooltip="{{ project.technologies }}"
              tooltipPosition="top"
              [tooltipStyleClass]="'text-xs text-gray-700 whitespace-pre-wrap'"
            >
              {{ project.technologies }}
            </span>
          </td>
          <td>
            <p-chip
              [label]="project.projectStatus.toUpperCase()"
              [styleClass]="
                getStatusChipStyle(project.projectStatus.toUpperCase())
              "
            >
            </p-chip>
          </td>
        </tr>
      </ng-template>
      <ng-template pTemplate="loadingbody">
        @for (_ of skeletonRows; track $index) {
          <tr>
            @for (col of columnHeadings(); track $index) {
              <td class="text-sm">
                <p-skeleton width="100%" height="1.5rem"></p-skeleton>
              </td>
            }
          </tr>
        }
      </ng-template>

      <ng-template pTemplate="emptymessage">
        <tr>
          <td colspan="8" class="text-center text-gray-500 py-4">
            No records found.
          </td>
        </tr>
      </ng-template>
      @if (hasError()) {
        <ng-template>
          <tr>
            <td colspan="8" class="text-center text-gray-500 py-4">
              Failed to load projects. Please try again later.
            </td>
          </tr>
        </ng-template>
      }
    </p-table>
  </div>
</div>
