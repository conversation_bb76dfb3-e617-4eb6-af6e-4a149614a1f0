import { CommonModule } from '@angular/common';
import {
  Component,
  computed,
  viewChild,
  ViewEncapsulation,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { ChipModule } from 'primeng/chip';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import {
  MultiSelect,
  MultiSelectChangeEvent,
  MultiSelectModule,
} from 'primeng/multiselect';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { SkeletonModule } from 'primeng/skeleton';
import { Table, TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { TooltipModule } from 'primeng/tooltip';
import { AuthService } from '../../services/auth/auth-service';
import { ClientService } from '../../services/client/client.service';
import { ProjectByResourceId } from '../../services/project/project.model';
import { ProjectsService } from '../../services/project/project.service';
import { StringUtilsService } from '../../services/stringutils/stringutils.service';
import { getStatusChipStyle } from '../../utils/chip.utils';
import { TECHNOLOGY_LIST } from '../../constants/constant';

@Component({
  selector: 'tms-project-overview',
  standalone: true,
  imports: [
    FormsModule,
    MultiSelectModule,
    TableModule,
    CommonModule,
    SkeletonModule,
    ChipModule,
    OverlayPanelModule,
    ConfirmDialogModule,
    DialogModule,
    TooltipModule,
    ButtonModule,
    ToastModule,
  ],
  providers: [MessageService, ConfirmationService],
  templateUrl: './project-overview.component.html',
  styleUrls: [
    './project-overview.component.css',
    '/src/app/components/tasks-table/tasks-table.component.css',
  ],
})
export class ProjectOverviewComponent {
  projectsTable = viewChild<Table>('projectsTable');
  clientMultiSelect = viewChild<MultiSelect>('clientMultiSelect');
  skeletonRows = Array(3);
  getStatusChipStyle = getStatusChipStyle;

  isEmployeeProjectQueryLoading = computed(() =>
    this.myProjectService.employeeProjectsQuery().isLoading()
  );

  projects = computed(() => {
    return this.myProjectService
      .employeeProjectsQuery()
      .data()
      ?.map((project) => {
        return {
          ...project,
          projectManagerName: this.getProjectManagerName(project),
        };
      });
  });

  hasError = computed(() =>
    this.myProjectService.employeeProjectsQuery().isError()
  );

  role = computed(() => this.authService.userRole());

  columnHeadings = computed(() => {
    const baseColumns = [
      { field: 'projectName', header: 'Project', sortable: true, class: '' },
      { field: 'clientName', header: 'Client', sortable: true, class: '' },
      {
        field: 'projectManagerName',
        header: 'Project Manager',
        sortable: false,
        class: '',
      },
      {
        field: 'technologies',
        header: 'Technology',
        sortable: false,
        class: '',
      },
      {
        field: 'projectStatus',
        header: 'Status',
        sortable: false,
        class: 'align-status',
      },
    ];
    return baseColumns;
  });

  /**
   * Query to fetch list of clients.
   */
  clientsQuery = computed(() => this.clientService.getClientsQuery().data());

  clientFilterOptions = computed(() => {
    const clients = this.clientService.ClientQuery.data();
    if (!clients) return [];
    return clients?.map((client) => client.name);
  });

  selectedClients: string[] = [];

  globalSearchValue: string = '';

  constructor(
    private myProjectService: ProjectsService,
    private clientService: ClientService,
    private authService: AuthService,
    public stringUtilsService: StringUtilsService,
    private router: Router
  ) {}

  ngOnInit() {
    this.clientsQuery();
  }

  /**
   * Handles input changes and filters the global data in the table
   * @param event The input event that is triggered when the user types in the input field
   */
  onInputChange(event: Event) {
    const target = event.target as HTMLInputElement;
    const table = this.projectsTable();
    this.globalSearchValue = target.value;
    if (table && target) {
      table.filterGlobal(this.globalSearchValue, 'contains');
    }
  }

  getManagerInfoTooltip(project: ProjectByResourceId): string {
    return (
      project.projectManager
        ?.map(
          (manager) =>
            `${manager.name}\n${manager.email}\n${manager.phoneNumber}`
        )
        .join('\n\n') ?? 'N/A'
    ); // Separate multiple managers with a blank line for readability
  }

  getProjectManagerName(project: ProjectByResourceId): string {
    return project?.projectManager?.length
      ? project.projectManager.map((manager) => manager.name).join(', ')
      : 'N/A';
  }

  navigateToDetails(projectId: string) {
    this.router.navigate(['/dashboard/projects', projectId]);
  }

  /**
   * Handles changes in selected clients filter.
   */
  onClientsFilterChange(event: MultiSelectChangeEvent) {
    this.selectedClients = event.value;
  }

  /**
   * Filters projects based on selected clients and project status.
   */
  filteredProjects = () => {
    const filteredProjectsByClient =
      this.selectedClients.length > 0
        ? this.projects()?.filter((project) =>
            this.selectedClients.includes(project.clientName)
          )
        : this.projects();

    return filteredProjectsByClient;
  };

  /**
   * Clears all filters and resets the global search value.
   */
  clearFilters() {
    this.selectedClients = [];
    const table = this.projectsTable();
    if (table) {
      table.filterGlobal('', 'contains');
    }
    this.globalSearchValue = '';
    this.clientMultiSelect()?.resetFilter();
  }
}
