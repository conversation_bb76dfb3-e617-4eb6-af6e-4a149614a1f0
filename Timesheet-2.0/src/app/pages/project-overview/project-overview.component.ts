import { CommonModule } from '@angular/common';
import {
  Component,
  computed,
  viewChild,
  ViewEncapsulation,
  signal,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { ConfirmationService, MessageService } from 'primeng/api';
import { ButtonModule } from 'primeng/button';
import { ChipModule } from 'primeng/chip';
import { ChartModule } from 'primeng/chart';
import { ConfirmDialogModule } from 'primeng/confirmdialog';
import { DialogModule } from 'primeng/dialog';
import {
  MultiSelect,
  MultiSelectChangeEvent,
  MultiSelectModule,
} from 'primeng/multiselect';
import { OverlayPanelModule } from 'primeng/overlaypanel';
import { SkeletonModule } from 'primeng/skeleton';
import { Table, TableModule } from 'primeng/table';
import { ToastModule } from 'primeng/toast';
import { TooltipModule } from 'primeng/tooltip';
import { Dropdown, DropdownModule } from 'primeng/dropdown';
import { Calendar, CalendarModule } from 'primeng/calendar';
import { AuthService } from '../../services/auth/auth-service';
import { ClientService } from '../../services/client/client.service';
import {
  dateRangeOptions,
  ProjectByResourceId,
} from '../../services/project/project.model';
import {
  ProjectsService,
  UtilizationStatsResponse,
} from '../../services/project/project.service';
import { StringUtilsService } from '../../services/stringutils/stringutils.service';
import { getStatusChipStyle } from '../../utils/chip.utils';
import { TECHNOLOGY_LIST } from '../../constants/constant';

@Component({
  selector: 'tms-project-overview',
  standalone: true,
  imports: [
    FormsModule,
    MultiSelectModule,
    TableModule,
    CommonModule,
    SkeletonModule,
    ChipModule,
    OverlayPanelModule,
    ConfirmDialogModule,
    DialogModule,
    TooltipModule,
    ButtonModule,
    ToastModule,
    DropdownModule,
    CalendarModule,
    ChartModule,
  ],
  providers: [MessageService, ConfirmationService],
  templateUrl: './project-overview.component.html',
  styleUrls: [
    './project-overview.component.css',
    '/src/app/components/tasks-table/tasks-table.component.css',
  ],
})
export class ProjectOverviewComponent {
  projectsTable = viewChild<Table>('projectsTable');
  clientMultiSelect = viewChild<MultiSelect>('clientMultiSelect');
  dateRangeDropdown = viewChild<Dropdown>('dateRangeDropdown');
  customStartDateCalendar = viewChild<Calendar>('customStartDate');
  customEndDateCalendar = viewChild<Calendar>('customEndDate');
  skeletonRows = Array(3);
  getStatusChipStyle = getStatusChipStyle;

  // Chart-related properties
  readonly dateRangeOptions = dateRangeOptions;
  readonly selectedDateRange = signal<string>('past1Week');
  readonly customStartDate = signal<Date | undefined>(undefined);
  readonly customEndDate = signal<Date | undefined>(undefined);

  // Utilization stats query
  utilizationStatsQuery = computed(() => {
    const timePeriod = this.selectedDateRange();
    const startDate = this.customStartDate();
    const endDate = this.customEndDate();

    return this.myProjectService.utilizationStatsQuery(
      timePeriod,
      startDate,
      endDate
    );
  });

  // Computed properties for chart data
  utilizationStats = computed(() => this.utilizationStatsQuery().data());
  isUtilizationStatsLoading = computed(() =>
    this.utilizationStatsQuery().isLoading()
  );
  utilizationStatsError = computed(() =>
    this.utilizationStatsQuery().isError()
  );

  isEmployeeProjectQueryLoading = computed(() =>
    this.myProjectService.employeeProjectsQuery().isLoading()
  );

  projects = computed(() => {
    return this.myProjectService
      .employeeProjectsQuery()
      .data()
      ?.map((project) => {
        return {
          ...project,
          projectManagerName: this.getProjectManagerName(project),
        };
      });
  });

  hasError = computed(() =>
    this.myProjectService.employeeProjectsQuery().isError()
  );

  role = computed(() => this.authService.userRole());

  columnHeadings = computed(() => {
    const baseColumns = [
      { field: 'projectName', header: 'Project', sortable: true, class: '' },
      { field: 'clientName', header: 'Client', sortable: true, class: '' },
      {
        field: 'projectManagerName',
        header: 'Project Manager',
        sortable: false,
        class: '',
      },
      {
        field: 'technologies',
        header: 'Technology',
        sortable: false,
        class: '',
      },
      {
        field: 'projectStatus',
        header: 'Status',
        sortable: false,
        class: 'align-status',
      },
    ];
    return baseColumns;
  });

  /**
   * Query to fetch list of clients.
   */
  clientsQuery = computed(() => this.clientService.getClientsQuery().data());

  clientFilterOptions = computed(() => {
    const clients = this.clientService.ClientQuery.data();
    if (!clients) return [];
    return clients?.map((client) => client.name);
  });

  selectedClients: string[] = [];

  globalSearchValue: string = '';

  constructor(
    private myProjectService: ProjectsService,
    private clientService: ClientService,
    private authService: AuthService,
    public stringUtilsService: StringUtilsService,
    private router: Router
  ) {}

  ngOnInit() {
    this.clientsQuery();
  }

  /**
   * Gets the current chart data based on selected date range
   */
  getCurrentChartData(): UtilizationStatsResponse | null {
    return this.utilizationStats() || null;
  }

  /**
   * Handles date range change from dropdown
   */
  onDateRangeChange(event: { value: string }) {
    this.selectedDateRange.set(event.value);
  }

  /**
   * Applies custom date range when custom dates are selected
   */
  applyCustomDateRange() {
    if (this.customStartDate() && this.customEndDate()) {
      // The query will automatically refetch with the new custom dates
      // since it's reactive to the customStartDate and customEndDate signals
      console.log('Custom date range applied:', {
        startDate: this.customStartDate(),
        endDate: this.customEndDate(),
      });
    }
  }

  /**
   * Handles input changes and filters the global data in the table
   * @param event The input event that is triggered when the user types in the input field
   */
  onInputChange(event: Event) {
    const target = event.target as HTMLInputElement;
    const table = this.projectsTable();
    this.globalSearchValue = target.value;
    if (table && target) {
      table.filterGlobal(this.globalSearchValue, 'contains');
    }
  }

  getManagerInfoTooltip(project: ProjectByResourceId): string {
    return (
      project.projectManager
        ?.map(
          (manager) =>
            `${manager.name}\n${manager.email}\n${manager.phoneNumber}`
        )
        .join('\n\n') ?? 'N/A'
    ); // Separate multiple managers with a blank line for readability
  }

  getProjectManagerName(project: ProjectByResourceId): string {
    return project?.projectManager?.length
      ? project.projectManager.map((manager) => manager.name).join(', ')
      : 'N/A';
  }

  navigateToDetails(projectId: string) {
    this.router.navigate(['/dashboard/projects', projectId]);
  }

  /**
   * Handles changes in selected clients filter.
   */
  onClientsFilterChange(event: MultiSelectChangeEvent) {
    this.selectedClients = event.value;
  }

  /**
   * Filters projects based on selected clients and project status.
   */
  filteredProjects = () => {
    const filteredProjectsByClient =
      this.selectedClients.length > 0
        ? this.projects()?.filter((project) =>
            this.selectedClients.includes(project.clientName)
          )
        : this.projects();

    return filteredProjectsByClient;
  };

  /**
   * Clears all filters and resets the global search value.
   */
  clearFilters() {
    this.selectedClients = [];
    const table = this.projectsTable();
    if (table) {
      table.filterGlobal('', 'contains');
    }
    this.globalSearchValue = '';
    this.clientMultiSelect()?.resetFilter();

    // Reset chart date range to default
    this.selectedDateRange.set('past1Week');
    this.customStartDate.set(undefined);
    this.customEndDate.set(undefined);

    // Reset calendar components
    this.dateRangeDropdown()?.resetFilter();
  }
  /**
   * Pie chart data for Organization Billability
   */
  orgBillabilityChartData = computed(() => {
    const chartData = this.getCurrentChartData();
    if (!chartData) {
      return {
        labels: ['Billable', 'Partially Billable', 'Non Billable'],
        datasets: [
          {
            data: [0, 0, 0],
            backgroundColor: ['#C17EDB', '#C39032', '#D65D3C'],
            hoverBackgroundColor: ['#BC76D7', '#BD892D', '#D35534'],
          },
        ],
      };
    }

    const org = chartData.orgLevel;
    return {
      labels: ['Billable', 'Partially Billable', 'Non Billable'],
      datasets: [
        {
          data: [org.billable, org.partiallyBillable, org.nonBillable],
          backgroundColor: ['#C17EDB', '#C39032', '#D65D3C'],
          hoverBackgroundColor: ['#BC76D7', '#BD892D', '#D35534'],
        },
      ],
    };
  });

  /**
   * Pie chart data for Leave Stats
   */
  leaveStatsChartData = computed(() => {
    const chartData = this.getCurrentChartData();
    if (!chartData) {
      return {
        labels: ['Full Day', 'Half Day'],
        datasets: [
          {
            data: [0, 0],
            backgroundColor: ['#5A9DF7', '#9C72F7'],
            hoverBackgroundColor: ['#3B82F6', '#8B5CF6'],
          },
        ],
      };
    }

    const leave = chartData.leaveStats;
    return {
      labels: ['Full Day', 'Half Day'],
      datasets: [
        {
          data: [leave.fullDay, leave.halfDay],
          backgroundColor: ['#5A9DF7', '#9C72F7'],
          hoverBackgroundColor: ['#3B82F6', '#8B5CF6'],
        },
      ],
    };
  });

  centerTextPlugin = {
  id: 'centerText',
  afterDraw: (chart: any) => {
    const { ctx, chartArea: { width, height } } = chart;
    ctx.save();

    const total = chart.data.datasets[0].data
      .reduce((sum: number, val: number) => sum + val, 0);

    const centerX = width / 2;
    const centerY = height / 2;

    // First line: label
    ctx.font = '12px sans-serif';
    ctx.fillStyle = '#6B7280'; // Tailwind gray-500
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('Total', centerX, centerY - 10);

    // Second line: value
    ctx.font = '12px sans-serif';
    ctx.fillStyle = '#374151'; // Tailwind gray-700
    ctx.fillText(total, centerX, centerY + 10);

    ctx.restore();
  }
};


  /**
   * Shared chart options
   */
  chartOptions: any = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom',
        labels: {
          color: '#374151', // Tailwind gray-700
          font: { size: 12 },
        },
      },
    },
  };
}
