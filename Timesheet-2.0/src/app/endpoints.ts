export const endpoints = {
  worklog: {
    employeeReport: '/api/v2/worklog/employeeReport',
    addWorklog: '/api/v2/worklog/daily-log',
    updateWorklog: '/api/v2/worklog/daily-log/:id',
    list: '/api/v2/worklog/list',
    delete: '/api/v1/worklog',
    getWorkLogsByManagerId: '/api/v1/worklog/resource/:resourceId',
    approve: '/api/v2/worklog/status/approved',
    reject: '/api/v2/worklog/status/rejected',
    filteredWorklog: '/api/v2/worklog',
  },
  project: {
    getAllProjects: '/api/v2/project',
    projectById: '/api/v2/project/:id',
    projectsByResourceId: '/api/v2/project/resource/:id',
    projectsList: '/api/v2/project/list/projects',
    projectsContractHistory: '/api/v1/project/:projectId/history',
    createProject: '/api/v1/project',
    updateProject: '/api/v2/project/:projectId',
  },
  task: {
    getTasksByProjectId: '/api/v2/task/list/:projectId',
    getTasksByContractId: '/api/v2/task',
    updateStatus: '/api/v2/task/status',
    delete: '/api/v1/task',
    create: '/api/v2/task',
    update: '/api/v2/task/:taskId',
  },
  profile: {
    resourceDetail: '/api/v1/resource',
  },
  resource: {
    list: '/api/v2/resource/list/resources',
    resourcesByManagerId: '/api/v1/resource/manager/:managerId/resources',
    workLogReport: '/api/v1/resource/worklog/report',
    reportDownload: '/api/v2/resource/report/download',
  },
  client: {
    create: '/api/v1/client',
    list: '/api/v1/client/list',
    getAllClients: '/api/v1/client',
    uploadHoliday: '/api/v1/holiday/upload/:clientId',
    updateClient: '/api/v1/client/:clientId',
    report: '/api/v1/client/report/:clientId/:month/:year/:projectStatus',
  },
  contract: {
    getContracts: '/api/v2/contract',
    contractById: '/api/v2/contract/:id',
    addComments: '/api/v1/contract/:contractId/comments',
    deleteComment: '/api/v1/contract/comment/:commentId',
    editComment: '/api/v1/contract/comment/:commentId',
    addContract: '/api/v2/contract',
    updateContract: '/api/v2/contract/:contractId',
    complete: '/api/v2/contract/:id/complete',
    renew: '/api/v2/contract/renew',
    reactivate: '/api/v2/contract/:id/reactivate',
    deactivate: '/api/v2/contract/:id/deactivate',
    getContractHistory: '/api/v1/contract/:contractId/history',
    getResourceByContract: '/api/v1/contract/:contractId/resources',
    report: '/api/v2/contract/report/:contractId/:month/:year/:mode',
  },
  leaves: {
    upload: '/api/v1/leaves/upload',
    delete: '/api/v1/leaves/:id',
    all: '/api/v1/leaves',
    list: '/api/v1/leaves/list',
  },
  department: {
    all: '/api/v1/department',
  },
};
