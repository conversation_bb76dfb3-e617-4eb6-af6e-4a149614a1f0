export function generateMockWorklogs(numberOfEmployees: number = 40): any[] {
  const projects = [
    {
      id: 'c8c80c3e-0c55-4cfa-8d88-faf8f81363e9',
      name: 'Recruitment App',
      billable: false,
    },
    {
      id: 'b7b70b2d-0a44-3cfa-7d77-eae7e7a735d8',
      name: 'E-commerce Platform',
      billable: true,
    },
    {
      id: 'a6a60a1c-9b33-2beb-6c66-dad6d6b624c7',
      name: 'CRM System',
      billable: true,
    },
    {
      id: 'd5d50d0b-8c22-1ada-5b55-cbc5c5a513b6',
      name: 'Mobile App',
      billable: true,
    },
  ];

  const tasks = [
    {
      id: '7721fe77-751f-49fb-8223-48ed37bd788d',
      name: 'Backend development',
    },
    {
      id: '6610ed66-640e-38ea-7112-37dc26ac677c',
      name: 'Frontend development',
    },
    { id: '5509dc55-539d-27d9-6001-26cb15ab566b', name: 'Database design' },
    { id: '4408cb44-428c-16c8-5990-15ba04ba455a', name: 'UI/UX design' },
  ];

  const statuses = ['submitted', 'approved', 'rejected', 'revised'];
  const firstNames = [
    '<PERSON>',
    'Jane',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    '<PERSON>',
    'Lisa',
    '<PERSON>',
    'Emily',
  ];
  const lastNames = [
    'Smith',
    'Johnson',
    'Williams',
    'Brown',
    'Jones',
    'Garcia',
    'Miller',
    'Davis',
    'Rodriguez',
    'Martinez',
  ];

  const generateUUID = () =>
    'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {
      const r = (Math.random() * 16) | 0;
      const v = c === 'x' ? r : (r & 0x3) | 0x8;
      return v.toString(16);
    });

  let worklogs: any[] = [];

  // Generate a fixed set of employees with unique IDs
  const employees = Array.from({ length: numberOfEmployees }, () => ({
    id: generateUUID(),
    name: `${firstNames[Math.floor(Math.random() * firstNames.length)]} ${lastNames[Math.floor(Math.random() * lastNames.length)]}`,
  }));

  // Create worklogs for each employee for each day in January
  for (let day = 1; day <= 30; day++) {
    // Only 30 days as requested
    for (const employee of employees) {
      const project = projects[Math.floor(Math.random() * projects.length)];
      const task = tasks[Math.floor(Math.random() * tasks.length)];

      const workDate = new Date(2025, 0, day);
      const startDate = new Date(workDate);
      startDate.setHours(9, 0, 0, 0); // Work starts at 9 AM
      const endDate = new Date(startDate);
      endDate.setHours(17, 0, 0, 0); // Work ends at 5 PM (8-hour shift)

      worklogs.push({
        id: generateUUID(),
        workDate: workDate.toISOString(),
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString(),
        clientId: generateUUID(),
        projectId: project.id,
        employeeId: employee.id,
        managerId: generateUUID(),
        taskId: task.id,
        description: 'Daily work log entry',
        minutes: 480,
        isOnLeave: Math.random() < 0.1, // 10% chance of leave
        isOnFirstHalfLeave: Math.random() < 0.05,
        isOnSecondHalfLeave: Math.random() < 0.05,
        isWeekOff: Math.random() < 0.15, // 15% chance of a weekend off
        isCompanyOff: Math.random() < 0.03, // 3% chance of company holiday
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        worklogTimerStatus: 'stopped',
        categoryId: null,
        subCategoryId: null,
        deleted: false,
        employeeResource: {
          name: employee.name,
        },
        project: {
          projectName: project.name,
          billable: project.billable,
        },
        workLogStatus: {
          status: statuses[Math.floor(Math.random() * statuses.length)],
        },
        category: null,
        subCategory: null,
        task: {
          taskName: task.name,
          contract: {
            id: generateUUID(),
            departmentId: null,
            startDate: new Date(2025, 0, 20, 18, 30).toISOString(),
            endDate: new Date(2025, 2, 26, 18, 30).toISOString(),
            contractStatus: 'active',
            projectId: project.id,
            deleted: false,
            clientId: generateUUID(),
            customContractId: `${project.name.substring(0, 2).toUpperCase()}-${String(Math.floor(Math.random() * 999)).padStart(3, '0')}`,
            projectManagerId: null,
            renewedFrom: null,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            contractTypeSelection: 'timeAndMaterial',
            totalEstimatedEffort: null,
          },
        },
      });
    }
  }

  return worklogs;
}
