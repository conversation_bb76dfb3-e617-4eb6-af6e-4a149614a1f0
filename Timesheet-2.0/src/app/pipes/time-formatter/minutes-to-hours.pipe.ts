import { Pipe, PipeTransform } from '@angular/core';

/**
 * MinutesToHoursPipe
 *
 * This pipe transforms a number of minutes into a string representation
 * of hours and minutes. It is useful for displaying time durations in a
 * more human-readable format.
 */
@Pipe({
  name: 'minutesToHours',
  standalone: true,
})
export class MinutesToHoursPipe implements PipeTransform {
  transform(minutes: number): string {
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes === 0
      ? `${hours} hours`
      : `${hours} hours ${remainingMinutes} min`;
  }
}
