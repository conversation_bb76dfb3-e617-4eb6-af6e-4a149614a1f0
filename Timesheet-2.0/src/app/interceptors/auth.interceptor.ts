import { HttpInterceptorFn } from '@angular/common/http';
import { environment } from '../../environments/environment';

export const authInterceptor: HttpInterceptorFn = (req, next) => {
  const isLocalhost =
    window.location?.hostname === 'localhost' ||
    window.location?.hostname === '127.0.0.1';

  const BASE_URL = isLocalhost ? environment.apiUrl : window.location.origin;

  // Remove leading slash from req.url if it exists
  const path = req.url.replace(/^\/+/, '');

  // Ensure BASE_URL doesn't end with a slash
  const baseUrl = BASE_URL.replace(/\/+$/, '');

  const modifiedReq = req.clone({
    url: `${baseUrl}/${path}`,
    withCredentials: true,
  });

  return next(modifiedReq);
};
