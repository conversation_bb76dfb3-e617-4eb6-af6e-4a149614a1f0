import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import {
  ResourceByID,
  ResourceByIdResponse,
  validateResourceById,
} from './profile.model';
import { lastValueFrom } from 'rxjs';
import { AuthService } from '../auth/auth-service';
import { injectQuery } from '@tanstack/angular-query-experimental';
import { endpoints } from '../../endpoints';

/**
 * ResourceService
 *
 * This service is responsible for fetching resource details related to a user.
 * It interacts with an API to retrieve user information and manages the authentication state.
 */
@Injectable({
  providedIn: 'root',
})
export class ResourceService {
  constructor(
    private http: HttpClient,
    private authService: AuthService
  ) {}

  /**
   * Asynchronous method to fetch resource details by user ID.
   * It constructs an API URL using the user ID and makes a GET request.
   */
  async getResourceByID(): Promise<ResourceByID> {
    const url = `${endpoints.profile.resourceDetail}/${this.authService.userId()}`;

    try {
      const response = await lastValueFrom(
        this.http.get<ResourceByIdResponse>(url, { withCredentials: true })
      );

      const validatedResponse = validateResourceById(response.data);
      return validatedResponse; // Return the validated response
    } catch (error: any) {
      if (error instanceof HttpErrorResponse) {
        console.error(`Failed to fetch resource: ${error.message}`);
        throw new Error(`Failed to fetch resource: ${error.message}`);
      } else {
        console.error(`An unexpected error occurred: ${error.message}`);
        throw new Error(`An unexpected error occurred: ${error.message}`);
      }
    }
  }

  /**
   * Query to fetch resource details based on user authentication state.
   * It is enabled only if the user is authenticated.
   */
  ResourceDetailsQuery = injectQuery(() => ({
    queryKey: ['resourceDetails', this.authService.userId()],
    enabled: !!this.authService.isAuthenticatedQuery.data(),
    queryFn: () => this.getResourceByID(),
    onError: (error: any) => {
      console.error('Error fetching resource details:', error);
    },
  }));
}
