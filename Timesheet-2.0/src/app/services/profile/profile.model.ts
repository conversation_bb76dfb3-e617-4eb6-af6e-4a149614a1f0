import { z } from 'zod';
import { fromZodError } from 'zod-validation-error';

interface UserProfile {
  name: string;
  email: string;
  role: 'admin';
}

const ResourceDetailsSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string(),
  phoneNumber: z.string().optional().nullable(),
  location: z.string(),
  jobTitle: z.string().nullable(),
  department: z.string(),
  employeeNo: z.string(),
  profilePicurl: z.string().optional().nullable(),
  reportingTo: z.string().optional().nullable(),
});

export type ResourceByID = z.infer<typeof ResourceDetailsSchema>;

export function validateResourceById(data: ResourceByID): ResourceByID {
  const result = ResourceDetailsSchema.safeParse(data);
  if (result.success) {
    return data;
  } else {
    const validationError = fromZodError(result.error as z.ZodError);
    throw validationError;
  }
}

export type ResourceByIdResponse = {
  data: ResourceByID;
  message: string;
  statusCode: number;
};
