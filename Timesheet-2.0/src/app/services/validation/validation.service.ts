import { Injectable } from '@angular/core';
import { z, ZodSchema } from 'zod';
import { fromZodError } from 'zod-validation-error';

/**
 * ValidationService is an Angular service responsible for validating
 * data against defined schemas using the Zod validation library.
 */
@Injectable({
  providedIn: 'root',
})
export class ValidationService {
  /**
   * Validates the provided data against the specified Zod schema.
   *
   * @param data - The data to be validated.
   * @param schema - The Zod schema against which the data will be validated.
   * @returns T - The validated data if it conforms to the schema.
   * @throws ValidationError - Throws a structured validation error if validation fails.
   */
  validate<T>(data: T, schema: ZodSchema): T {
    const result = schema.safeParse(data);
    if (result.success) {
      return data;
    } else {
      const validationError = fromZodError(result.error as z.ZodError);
      throw validationError;
    }
  }
}
