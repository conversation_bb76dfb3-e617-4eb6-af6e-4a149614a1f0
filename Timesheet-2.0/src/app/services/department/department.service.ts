import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Injectable, Injector, runInInjectionContext } from '@angular/core';
import { injectQuery } from '@tanstack/angular-query-experimental';
import { lastValueFrom } from 'rxjs';
import { endpoints } from '../../endpoints';
import { AuthService } from '../auth/auth-service';
import { ApiErrorResponse } from '../common.model';
import {
  Department,
  DepartmentResponse,
  validateAllDepartmentResponse,
} from './department.model';

@Injectable({
  providedIn: 'root',
})
export class DepartmentService {
  constructor(
    private http: HttpClient,
    private authService: AuthService,
    private injector: Injector
  ) {}

  /**
   * Query object for getting all departments
   * @returns Angular Query instance
   */
  listAllDepartmentsQuery() {
    return runInInjectionContext(this.injector, () =>
      injectQuery(() => ({
        queryKey: ['listAllDepartments'],
        queryFn: () => this.listAllDepartments(),
      }))
    );
  }

  /**
   * Fetch all departments
   * @returns Promise resolving to Department[]
   */
  async listAllDepartments(): Promise<Department[]> {
    try {
      const response = await lastValueFrom(
        this.http.get<DepartmentResponse>(endpoints.department.all)
      );
      const validated = validateAllDepartmentResponse(response);

      return validated;
    } catch (error) {
      if (error instanceof HttpErrorResponse) {
        const errorMessage =
          error.error?.message || 'Unexpected error occurred.';
        throw new Error(`Error ${error.status}: ${errorMessage}`);
      }
      throw new Error((error as Error).message);
    }
  }
}
