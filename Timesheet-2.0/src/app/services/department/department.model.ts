import { z } from 'zod';
import { fromZodError } from 'zod-validation-error';

export const ResourceSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string(),
  role: z.string(),
  phoneNumber: z.union([z.string(), z.null()]),
  departmentId: z.union([z.string(), z.null()]).optional(),
  designation: z.union([z.string(), z.null()]),
  managerId: z.union([z.string(), z.null()]).optional().nullish(),
  location: z.string(),
  kekaId: z.string(),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
  deleted: z.boolean().optional(),
  profilePicUrl: z.union([z.string(), z.null()]),
  department: z.object({ departmentName: z.string() }).optional().nullish(),
  totalHoursSpent: z.number().optional(),
  billableHours: z.number().optional(),
  stackedBarChartData: z
    .array(
      z.object({
        employeeBillAmountPerDay: z.array(
          z.object({
            employeeId: z.string(),
            employeeName: z.string(),
            currentBillAmount: z.number(),
            billAmountPerDay: z.array(
              z.object({
                workDate: z.string(),
                amount: z.number(),
              })
            ),
          })
        ),
        projectTotalBillAmountPerDay: z.object({
          currentBillAmount: z.number(),
          billAmountPerDay: z.array(
            z.object({
              workDate: z.string(),
              amount: z.string(),
            })
          ),
        }),
      })
    )
    .nullable()
    .optional(),
  resourceBillingRate: z
    .array(
      z.object({
        id: z.string(),
        rate: z.number(),
        interval: z.string(),
        currency: z.string(),
        projectId: z.string(),
        resourceId: z.string(),
      })
    )
    .nullable()
    .optional(),
});

const NewDepartmentSchema = z.object({
  id: z.string(),
  departmentName: z.string(),
  deleted: z.boolean(),
  resource: z.array(ResourceSchema),
});

const DepartmentSchema = z.object({
  id: z.string(),
  departmentName: z.string(),
  deleted: z.boolean(),
  resource: z.array(z.object({ profilePicUrl: z.string().nullish() })),
});

const DepartmentResponseSchema = z.array(DepartmentSchema);

export const DepartmentListApiResponseSchema = z.object({
  statusCode: z.number(),
  message: z.string(),
  data: z.array(DepartmentSchema),
});

export type Department = z.infer<typeof DepartmentSchema>;
export type DepartmentResponse = z.infer<typeof DepartmentResponseSchema>;
export type NewDepartmentResponse = z.infer<typeof NewDepartmentSchema>;
export type DepartmentListApiResponse = z.infer<
  typeof DepartmentListApiResponseSchema
>;

export function validateAllDepartmentResponse(data: unknown): Department[] {
  const result = DepartmentListApiResponseSchema.safeParse(data);
  if (result.success) {
    return result.data.data;
  } else {
    const validationError = fromZodError(result.error);
    throw validationError;
  }
}
