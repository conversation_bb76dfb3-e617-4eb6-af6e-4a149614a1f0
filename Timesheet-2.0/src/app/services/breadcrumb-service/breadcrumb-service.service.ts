import { Injectable } from '@angular/core';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { BehaviorSubject, Observable } from 'rxjs';
import { filter } from 'rxjs/operators';
import { TitleCasePipe } from '@angular/common';
import { DASHBOARD, EXCLUDED_BREADCRUMB_ROUTES } from '../../settings';

/**
 * Represents a single breadcrumb item in the navigation hierarchy
 */
interface Breadcrumb {
  /** Optional icon to display next to the breadcrumb */
  icon?: string;
  /** Display text for the breadcrumb */
  label: string;
  /** Route path for navigation */
  route: string;
  /** Indicates if this is the last breadcrumb in the chain */
  isLast?: boolean;
}

/**
 * Service to manage breadcrumb navigation throughout the application
 * Automatically generates and updates breadcrumbs based on the current route
 */
@Injectable({
  providedIn: 'root',
})
export class BreadcrumbService {
  private readonly breadcrumbSubject = new BehaviorSubject<Breadcrumb[]>([]);

  // Expose breadcrumbs as an observable for components to subscribe to
  readonly breadcrumbs$: Observable<Breadcrumb[]> =
    this.breadcrumbSubject.asObservable();

  private readonly titleCasePipe = new TitleCasePipe();

  constructor(private readonly router: Router) {}

  /**
   * Initializes the breadcrumb service and starts listening to route changes
   * @param route The activated route to start tracking from
   */
  initialize(route: ActivatedRoute): void {
    // Listen for route changes
    this.router.events
      .pipe(
        filter(
          (event): event is NavigationEnd => event instanceof NavigationEnd
        )
      )
      .subscribe(() => {
        const newBreadcrumbs = this.generateBreadcrumbs(route.root);
        this.breadcrumbSubject.next(newBreadcrumbs);
      });

    // Initial breadcrumb generation
    const initialBreadcrumbs = this.generateBreadcrumbs(route.root);
    this.breadcrumbSubject.next(initialBreadcrumbs);
  }

  /**
   * Recursively generates breadcrumbs based on the current route hierarchy
   * @param route Current route node being processed
   * @param url Accumulated URL for the current breadcrumb chain
   * @param breadcrumbs Array of breadcrumbs being built
   * @returns New array of breadcrumbs
   */
  private generateBreadcrumbs(
    route: ActivatedRoute,
    url: string = '',
    breadcrumbs: Breadcrumb[] = []
  ): Breadcrumb[] {
    // Base case: no more child routes
    if (route.children.length === 0) {
      return this.processLeafRoute(breadcrumbs);
    }

    // Process child routes and accumulate results
    return route.children.reduce(
      (acc, child) => this.processChildRoute(child, url, acc),
      breadcrumbs
    );
  }

  /**
   * Processes a leaf route and returns updated breadcrumbs
   * @param breadcrumbs Current breadcrumb chain
   * @returns New array of processed breadcrumbs
   */
  private processLeafRoute(breadcrumbs: Breadcrumb[]): Breadcrumb[] {
    // Mark the last breadcrumb
    const markedBreadcrumbs = this.markLastBreadcrumb(breadcrumbs);

    // Check for excluded routes
    const finalUrl = this.buildFinalUrl(markedBreadcrumbs);
    if (this.isExcludedRoute(finalUrl)) {
      return [];
    }

    return markedBreadcrumbs;
  }

  /**
   * Processes a child route and returns updated breadcrumbs
   * @param child Child route to process
   * @param parentUrl URL accumulated from parent routes
   * @param breadcrumbs Current breadcrumb chain
   * @returns New array of breadcrumbs
   */
  private processChildRoute(
    child: ActivatedRoute,
    parentUrl: string,
    breadcrumbs: Breadcrumb[]
  ): Breadcrumb[] {
    const routeURL = this.getRouteUrl(child);
    const breadcrumbLabel = this.getBreadcrumbLabel(child, routeURL);
    const fullUrl = `${parentUrl}/${routeURL}`;

    if (routeURL && breadcrumbLabel.toLocaleLowerCase() !== DASHBOARD) {
      const newBreadcrumb = this.createBreadcrumb(breadcrumbLabel, fullUrl);
      const newBreadcrumbs = [...breadcrumbs, newBreadcrumb];
      return this.generateBreadcrumbs(child, fullUrl, newBreadcrumbs);
    }

    return this.generateBreadcrumbs(child, fullUrl, breadcrumbs);
  }

  /**
   * Extracts the URL from a route
   */
  private getRouteUrl(route: ActivatedRoute): string {
    return route.snapshot?.url.map((segment) => segment.path).join('/');
  }

  /**
   * Gets the breadcrumb label from route data or URL
   */
  private getBreadcrumbLabel(
    route: ActivatedRoute,
    fallbackUrl: string
  ): string {
    return route.snapshot?.data['breadcrumb'] || fallbackUrl;
  }

  /**
   * Creates a new breadcrumb object
   */
  private createBreadcrumb(label: string, route: string): Breadcrumb {
    return {
      label: this.titleCasePipe.transform(label),
      route,
    };
  }

  /**
   * Marks the last breadcrumb in the chain without mutating the input
   */
  private markLastBreadcrumb(breadcrumbs: Breadcrumb[]): Breadcrumb[] {
    return breadcrumbs.map((breadcrumb, index) => ({
      ...breadcrumb,
      isLast: index === breadcrumbs.length - 1,
    }));
  }

  /**
   * Builds the final URL from breadcrumbs
   */
  private buildFinalUrl(breadcrumbs: Breadcrumb[]): string {
    return breadcrumbs.map((b) => b.route).join('/');
  }

  /**
   * Checks if the current route should be excluded
   */
  private isExcludedRoute(url: string): boolean {
    return EXCLUDED_BREADCRUMB_ROUTES.some((excludedRoute) =>
      url.includes(excludedRoute)
    );
  }
}
