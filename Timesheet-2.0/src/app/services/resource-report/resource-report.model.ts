import { z } from 'zod';
import { fromZodError } from 'zod-validation-error';

const WorkLogSchema = z.object({
  workDate: z.string(),
  minutes: z.number(),
  isOnLeave: z.boolean(),
  isWeekOff: z.boolean(),
  isCompanyOff: z.boolean(),
  attendance: z.string(),
  billingStatus: z.string(),
  isPresent: z.boolean(),
  isBillable: z.boolean(),
});

const TaskDetailsSchema = z.object({
  minutes: z.number(),
  projectId: z.string(),
  projectName: z.string(),
  taskId: z.string(),
  taskName: z.string(),
  workDate: z.string(),
});

const ResourceListSchema = z.array(
  z.object({
    resourceId: z.string(),
    resourceName: z.string(),
    kekaId: z.string(),
    isDeleted: z.boolean(),
    department: z.string(),
    totalMinutes: z.number(),
    totalDays: z.number(),
    billableStatus: z.enum(['both', 'billable', 'not-billable']),
    resourcesWorklog: z.array(WorkLogSchema).nullish(),
    taskDetails: z.array(TaskDetailsSchema).nullish(),
    leaveDetails: z
      .object({
        date: z.string(),
        leaveStatus: z.number(),
      })
      .array(),
  })
);

export type ResourceReport = z.infer<typeof ResourceListSchema>;
export type WorklogData = z.infer<typeof WorkLogSchema>;

export function validateResourceReportResponse(data: {
  data: ResourceReport;
  totalCount: number;
}): { data: ResourceReport; totalCount: number } {
  const result = z
    .object({
      data: ResourceListSchema,
      totalCount: z.number(),
    })
    .safeParse(data);
  if (result.success) {
    return result.data;
  } else {
    const validationError = fromZodError(result.error as z.ZodError);
    throw validationError;
  }
}
