import {
  HttpClient,
  HttpErrorResponse,
  HttpParams,
} from '@angular/common/http';
import { Injectable, Injector, runInInjectionContext } from '@angular/core';
import { injectQuery } from '@tanstack/angular-query-experimental';
import { lastValueFrom } from 'rxjs';
import { endpoints } from '../../endpoints';
import { ApiResponse } from '../common.model';
import { ValidationService } from '../validation/validation.service';
import {
  ResourceReport,
  validateResourceReportResponse,
} from './resource-report.model';
import { z } from 'zod';

export interface IResourceReport {
  month: number;
  year: number;
  queryString?: string;
  currentPage?: number | null;
  rowPerPage?: number | null;
  sortBy?: string | null;
  sortOrder?: string | null;
  department?: string[] | null;
  billingStatus?: string | null;
  resourceStatus?: string | null;
  mode?: 'actualHours' | 'attendance';
}

@Injectable({
  providedIn: 'root',
})
export class ResourceReportService {
  constructor(
    private http: HttpClient,
    private validationService: ValidationService,
    private injector: Injector
  ) {}

  private buildQueryParams(params: IResourceReport): HttpParams {
    let queryParams = new HttpParams()
      .set('month', params.month.toString())
      .set('year', params.year.toString());

    // Handle department IDs as multiple parameters
    if (params.department && params.department.length > 0) {
      params.department.forEach((deptId) => {
        queryParams = queryParams.append('departmentIds', deptId);
      });
    }

    const optionalParams = [
      { key: 'page', value: params.currentPage?.toString() },
      { key: 'pageSize', value: params.rowPerPage?.toString() },
      { key: 'billableStatus', value: params.billingStatus },
      { key: 'sortBy', value: params.sortBy },
      { key: 'sortOrder', value: params.sortOrder },
      { key: 'resourceStatus', value: params.resourceStatus },
      { key: 'mode', value: params.mode },
    ];

    optionalParams.forEach(({ key, value }) => {
      if (value) queryParams = queryParams.set(key, value);
    });

    return queryParams;
  }

  getResourceOverviewQuery(params: IResourceReport) {
    return runInInjectionContext(this.injector, () =>
      injectQuery(() => ({
        queryKey: ['resourceOverview', params],
        queryFn: () => this.getResourceOverview(params),
      }))
    );
  }

  async getResourceOverview(
    params: IResourceReport
  ): Promise<{ data: ResourceReport; totalCount: number }> {
    try {
      const queryParams = this.buildQueryParams(params);

      const url = `${endpoints.resource.workLogReport}${
        params.queryString ? `${params.queryString}` : ''
      }`;

      const response = await lastValueFrom(
        this.http.get<{ data: ResourceReport; count: number }>(url, {
          params: queryParams,
        })
      );

      const validated = validateResourceReportResponse({
        data: response.data ?? [],
        totalCount: response.count ?? 0,
      });

      return validated;
    } catch (error) {
      console.error('Failed to fetch resource report:', error);
      throw new Error(
        'Failed to fetch resource report. Please try again later.'
      );
    }
  }

  async fetchResourceReportDownloadUrl(
    params: IResourceReport
  ): Promise<{ downloadUrl: string }> {
    const queryParams = this.buildQueryParams(params);

    const url = `${endpoints.resource.reportDownload}?${queryParams.toString()}${
      params.queryString ? `&${params.queryString}` : ''
    }`;

    const response = await lastValueFrom(this.http.get<{ data: string }>(url));

    return z.object({ downloadUrl: z.string() }).parse(response.data);
  }
}
