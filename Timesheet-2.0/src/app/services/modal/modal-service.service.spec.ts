import { TestBed } from '@angular/core/testing';
import { ModalService } from './modal-service.service';
import { DialogService, DynamicDialogRef } from 'primeng/dynamicdialog';
import { of } from 'rxjs';
import { Component } from '@angular/core';

@Component({
  standalone: true,
  template: `<p>
    Lorem ipsum dolor sit amet consectetur adipisicing elit. Quia, soluta!
  </p>`,
})
class MockComponent {}

describe('ModalService', () => {
  let service: ModalService;
  let dialogServiceSpy: jasmine.SpyObj<DialogService>;
  let mockDialogRef: jasmine.SpyObj<DynamicDialogRef>;

  beforeEach(() => {
    dialogServiceSpy = jasmine.createSpyObj('DialogService', ['open']);
    mockDialogRef = jasmine.createSpyObj('DynamicDialogRef', ['close'], {
      get onClose() {
        return of(null);
      },
    });

    TestBed.configureTestingModule({
      providers: [
        ModalService,
        { provide: DialogService, useValue: dialogServiceSpy },
      ],
    });

    service = TestBed.inject(ModalService);
    dialogServiceSpy.open.and.returnValue(mockDialogRef);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  it('should open a modal with correct parameters', () => {
    const mockComponent = MockComponent;
    const header = 'Test Header';

    const ref = service.openModal(mockComponent, header);

    expect(dialogServiceSpy.open).toHaveBeenCalledWith(
      MockComponent,
      jasmine.objectContaining({
        header: header,
        modal: true,
        dismissableMask: true,
      })
    );

    expect(ref).toBeDefined();
  });

  it('should return the modal reference', () => {
    const mockComponent = {};
    const header = 'Test Header';

    const ref = service.openModal(mockComponent, header);

    expect(dialogServiceSpy.open).toHaveBeenCalled();
    expect(ref).toBe(mockDialogRef);
  });

  it('should handle modal close', (done) => {
    mockDialogRef.onClose.subscribe(() => {
      done();
    });

    service.openModal({}, 'Test Header');
    mockDialogRef.close();
  });
});
