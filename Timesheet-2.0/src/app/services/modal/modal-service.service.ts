import { Injectable } from '@angular/core';
import {
  DialogService,
  DynamicDialogConfig,
  DynamicDialogRef,
} from 'primeng/dynamicdialog';

/**
 * Service to manage dynamic modal dialogs in the application.
 * This service provides methods to open modals with specified components,
 * headers, and configurations.
 */
@Injectable({
  providedIn: 'root',
})
export class ModalService {
  constructor(private dialogService: DialogService) {}

  /**
   * Opens a modal dialog with the specified component and header.
   * @returns A reference to the opened dialog
   */
  openModal(
    component: any,
    header: string,
    config?: DynamicDialogConfig
  ): DynamicDialogRef {
    const ref: DynamicDialogRef = this.dialogService.open(component, {
      header: header,
      modal: true,
      closeOnEscape: true,
      dismissableMask: true,
      styleClass: 'dynamic-modal',
      ...config,
    });
    return ref;
  }
}
