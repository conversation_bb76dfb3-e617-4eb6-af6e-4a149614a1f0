import { z } from 'zod';
import { fromZodError } from 'zod-validation-error';
import { HttpStatus } from '../worklog/worklog.model';

export const ClientDetailSchema = z.object({
  id: z.string(),
  name: z.string(),
  contactName: z.string(),
  contactPhoneNumber: z.string(),
  contactEmail: z.string(),
  country: z.string(),
  address: z.string(),
  zipcode: z.string(),
  currency: z.string(),
  status: z.boolean(),
  taxName: z.union([z.string(), z.null()]),
  taxPercentage: z.union([z.number(), z.null()]),
  taxNumber: z.union([z.string(), z.null()]),
  createdAt: z.string(),
  updatedAt: z.string(),
  deleted: z.boolean(),
  _count: z
    .object({
      workLogs: z.number(),
      projects: z.number(),
      Contract: z.number(),
      ClientPermissions: z.number(),
    })
    .optional(),
  projects: z
    .array(
      z.object({
        projectName: z.string(),
        projectStatus: z.string(),
        clientId: z.string(),
      })
    )
    .optional(),
});

export const ClientSchema = z.object({
  id: z.string().uuid(),
  name: z.string(),
  contactName: z.string(),
  contactPhoneNumber: z.string(),
  contactEmail: z.string().email(),
  country: z.string().optional().nullable(),
  address: z.string().optional().nullable(),
  zipcode: z.string().optional().nullable(),
  currency: z.string().optional().nullable(),
  status: z.boolean(),
  taxName: z.string().optional().nullable(),
  taxPercentage: z.number().min(0).max(100).optional().nullable(),
  taxNumber: z.string({ message: 'Invalid tax number' }).optional().nullable(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
  deleted: z.boolean(),
});

export const CreateClientSchema = z.object({
  name: z.string(),
  contactName: z.string(),
  contactPhoneNumber: z.string(),
  contactEmail: z.string().email(),
  country: z.string().optional().nullable(),
  address: z.string().optional().nullable(),
  zipcode: z.string().optional().nullable(),
  currency: z.string().optional().nullable(),
  status: z.boolean(),
});

export const UpdateClientSchema = CreateClientSchema.extend({
  clientId: z.string().uuid(),
});

export type Client = z.infer<typeof ClientSchema>;
export type ClientResponse = z.infer<typeof ClientDetailSchema>;
export type CreateClient = z.infer<typeof CreateClientSchema>;
export type UpdateClient = z.infer<typeof UpdateClientSchema>;

export function validateCreateClientData(data: CreateClient): CreateClient {
  const result = CreateClientSchema.safeParse(data);
  if (result.success) {
    return data;
  } else {
    const validationError = fromZodError(result.error as z.ZodError);
    throw validationError;
  }
}

export function validateClientResponse(data: ClientResponse): ClientResponse {
  const result = ClientDetailSchema.safeParse(data);
  if (result.success) {
    return data;
  } else {
    const validationError = fromZodError(result.error as z.ZodError);
    throw validationError;
  }
}

export function validateClientList(data: Client[]) {
  const result = z.array(ClientSchema).safeParse(data);
  if (result.success) {
    return data;
  } else {
    const validationError = fromZodError(result.error as z.ZodError);
    throw validationError;
  }
}

export interface ClientListResponse {
  statusCode: HttpStatus;
  data: ClientResponse[];
  count: number;
  message: string;
}

// Client Report Response Schema
export const ClientReportResourceWorklogSchema = z.object({
  workDate: z.string(),
  attendance: z.string(),
  minutes: z.number(),
  isOnLeave: z.boolean(),
  isWeekOff: z.boolean(),
  isCompanyOff: z.boolean(),
  isPresent: z.boolean(),
});

export const ClientReportResourceSchema = z.object({
  resourceId: z.string(),
  resourceName: z.string(),
  designation: z.string(),
  isDeleted: z.boolean(),
  totalLoggedMinutes: z.number(),
  totalBillableMinutes: z.number(),
  totalBillableDays: z.number(),
  resourceWorklog: z.array(ClientReportResourceWorklogSchema),
  leaveDetails: z.array(z.any()), // Adjust if leaveDetails structure is known
});

export const ClientReportContractSchema = z.object({
  id: z.string(),
  contractId: z.string(),
  startDate: z.string(),
  numberOfResource: z.number(),
  contractStatus: z.string(),
  resourceList: z.array(ClientReportResourceSchema),
  contractManagers: z.array(z.string()),
});

export const ClientReportProjectSchema = z.object({
  projectId: z.string(),
  projectName: z.string(),
  projectStatus: z.string(),
  numberOfContracts: z.number(),
  startDate: z.string(),
  contracts: z.array(ClientReportContractSchema),
  projectManagers: z.array(z.string()),
});

export const ClientReportDataSchema = z.object({
  clientId: z.string(),
  clientName: z.string(),
  projects: z.array(ClientReportProjectSchema),
});

export type ClientReportResourceWorklog = z.infer<
  typeof ClientReportResourceWorklogSchema
>;
export type ClientReportResource = z.infer<typeof ClientReportResourceSchema>;
export type ClientReportContract = z.infer<typeof ClientReportContractSchema>;
export type ClientReportProject = z.infer<typeof ClientReportProjectSchema>;
export type ClientReportData = z.infer<typeof ClientReportDataSchema>;
export type ClientReportResponse = z.infer<typeof ClientReportDataSchema>;

export function validateClientReportResponse(data: ClientReportResponse) {
  const result = ClientReportDataSchema.safeParse(data);
  if (result.success) {
    return result.data;
  } else {
    const validationError = fromZodError(result.error as z.ZodError);
    throw validationError;
  }
}
