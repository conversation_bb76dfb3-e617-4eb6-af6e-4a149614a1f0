import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Injectable, Injector, runInInjectionContext } from '@angular/core';
import { injectQuery } from '@tanstack/angular-query-experimental';
import { lastValueFrom } from 'rxjs';
import { endpoints } from '../../endpoints';
import { AuthService } from '../auth/auth-service';
import {
  ApiErrorResponse,
  ApiResponse,
  UpdateServiceResponse,
} from '../common.model';
import { ValidationService } from '../validation/validation.service';
import {
  Client,
  ClientResponse,
  CreateClient,
  UpdateClient,
  UpdateClientSchema,
  validateClientList,
  validateCreateClientData,
  ClientReportData,
  ClientReportResponse,
  validateClientReportResponse,
} from './client.model';

/**
 * ClientService
 *
 * This service is responsible for managing client-related operations,
 * including fetching a list of clients from the API. It uses <PERSON><PERSON>'s
 * HttpClient for making HTTP requests and integrates with an authentication
 * service to ensure that requests are made by authenticated users..
 */
@Injectable({
  providedIn: 'root',
})
export class ClientService {
  constructor(
    private authService: AuthService,
    private http: HttpClient,
    private validationService: ValidationService,
    private injector: Injector
  ) {}

  /**
   * ClientQuery
   *
   * A query to fetch the list of clients associated with the authenticated user.
   */
  ClientQuery = injectQuery(() => {
    return {
      queryKey: ['Client', this.authService.userId()],
      enabled: !!this.authService.isAuthenticatedQuery.data(),
      queryFn: () => {
        return this.getClientsList();
      },
    };
  });

  /**
   * Creates a query to fetch all clients using React Query.
   *
   * This function sets up a query with a unique key based on the user ID and
   * uses the `getAllClients` method as the query function.
   *
   * @returns A query object that can be used to fetch and manage client data.
   */
  getAllClientsQuery() {
    return runInInjectionContext(this.injector, () =>
      injectQuery(() => ({
        queryKey: ['getAllClients', this.authService.userId()],
        queryFn: () => this.getAllClients(),
      }))
    );
  }

  /**
   * It fetches a list of clients using TanStack Query.
   *
   * It gets the client list from `this.getClientsList()`, transforms each client
   * to include only the `id` and `name`, and automatically refetches the data
   */
  getClientsQuery() {
    return runInInjectionContext(this.injector, () =>
      injectQuery(() => ({
        queryKey: ['clientDetails', this.authService.userId()],
        queryFn: async () => {
          const clients = await this.getClientsList();
          return (
            clients.map((client) => ({
              id: client.id,
              name: client.name,
            })) || []
          );
        },
        onError: (error: Error) => {
          console.error('Error fetching resource details:', error);
        },
      }))
    );
  }

  /**
   * Fetches the list of clients from the API.
   */
  async getClientsList() {
    try {
      const resourceId = this.authService.userId();

      if (!resourceId) {
        throw new Error('Resource id not found.');
      }

      const response = await lastValueFrom(
        this.http.get<{ data: Client[] }>(`${endpoints.client.list}`)
      );
      return validateClientList(response.data);
    } catch (error) {
      console.error('Failed to fetch clients:', error);
      throw new Error('Failed to fetch clients. Please try again later.');
    }
  }

  /**
   * Retrieves a list of all clients from the server.
   *
   * This function sends a GET request to the server to fetch all client data.
   * It handles potential HTTP errors and throws informative error messages.
   *
   * @returns A promise resolving to an array of client data.
   */
  async getAllClients() {
    try {
      const response = await lastValueFrom(
        this.http.get<{ data: ClientResponse[] }>(
          `${endpoints.client.getAllClients}`
        )
      );
      return response.data;
    } catch (error) {
      if (error instanceof HttpErrorResponse) {
        const errorMessage =
          error.error?.message || 'An unexpected error occurred.';
        if (error.status >= 400 && error.status < 500) {
          throw new Error(`Failed to get all clients: ${errorMessage}`);
        } else if (error.status >= 500) {
          throw new Error(
            `An unexpected server error occurred: ${errorMessage}`
          );
        }
      }
      throw new Error((error as Error).message);
    }
  }

  async addClient(clientData: CreateClient): Promise<UpdateServiceResponse> {
    try {
      const validatedContractData = validateCreateClientData(clientData);
      const response = await lastValueFrom(
        this.http.post<ApiResponse<Client>>(
          `${endpoints.client.create}`,
          validatedContractData
        )
      );

      if (response.statusCode === 201) {
        return {
          status: 'success',
          message: `Client "${response.data.name}" added successfully.`,
        };
      } else {
        throw new Error(response.message);
      }
    } catch (error) {
      console.error('Failed to add client:', error);
      throw (error as ApiErrorResponse).error;
    }
  }

  /**
   * `updateClient` updates an existing client with the provided data.
   *
   * It validates the `clientData` using `this.validationService` against the `UpdateClientSchema`.
   * If the validation is successful, it sends a PUT request to the server to update the client.
   * It handles both successful updates and errors, including HTTP errors.
   */

  async updateClient(clientData: UpdateClient): Promise<string> {
    try {
      if (!clientData.clientId) {
        throw new Error('Client id not found');
      }
      const validatedData = this.validationService.validate(
        clientData,
        UpdateClientSchema
      );

      const response = await lastValueFrom(
        this.http.put<ApiResponse<Client>>(
          `${endpoints.client.updateClient.replace(':clientId', clientData.clientId)}`,
          validatedData
        )
      );

      if (response.statusCode === 200) {
        return 'success';
      } else {
        throw new Error(response.message);
      }
    } catch (error: any) {
      if (error instanceof HttpErrorResponse) {
        const errorMessage =
          error.error?.message || 'An unexpected error occurred.';
        if (error.status >= 400 && error.status < 500) {
          throw new Error(`Failed to update client: ${errorMessage}`);
        } else if (error.status >= 500) {
          throw new Error(
            'An unexpected server error occurred. Please try again later.'
          );
        }
      }
      throw new Error((error as Error).message);
    }
  }

  /**
   * Fetches the client report for a given client, month, year, and projectStatus.
   * @param clientId The client UUID
   * @param month The month (number)
   * @param year The year (number)
   * @param projectStatus The project status (e.g., 'all')
   */
  async getClientReport(
    clientId: string,
    month: number,
    year: number,
    projectStatus: string
  ): Promise<ClientReportResponse> {
    try {
      const url = endpoints.client.report
        .replace(':clientId', clientId)
        .replace(':month', String(month))
        .replace(':year', String(year))
        .replace(':projectStatus', projectStatus);
      const response = await lastValueFrom(
        this.http.get<ApiResponse<ClientReportResponse>>(url)
      );
      return validateClientReportResponse(response.data);
    } catch (error) {
      console.error('Failed to fetch client report:', error);
      throw new Error('Failed to fetch client report. Please try again later.');
    }
  }
}
