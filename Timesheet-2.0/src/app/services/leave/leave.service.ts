import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Injectable, Injector, runInInjectionContext } from '@angular/core';
import { injectQuery } from '@tanstack/angular-query-experimental';
import { lastValueFrom } from 'rxjs';
import { endpoints } from '../../endpoints';
import { AuthService } from '../auth/auth-service';
import { ValidationService } from '../validation/validation.service';
import { ApiErrorResponse } from '../common.model';
import {
  Leaves,
  DeleteLeave,
  validateDeleteLeaveResponse,
  validateListAllLeavesResponse,
} from './leave.model';

@Injectable({
  providedIn: 'root',
})
export class LeaveService {
  constructor(
    private authService: AuthService,
    private http: HttpClient,
    private injector: Injector
  ) {}

  /**
   * Get a query for fetching all leaves
   * @returns Query object for fetching all leaves
   */
  listAllLeavesQuery() {
    return runInInjectionContext(this.injector, () =>
      injectQuery(() => ({
        queryKey: [
          'listAllLeaves',
          this.authService.userId(),
          this.authService.userRole(),
        ],
        queryFn: () => this.listAllLeaves(),
      }))
    );
  }

  /**
   * Fetch all leaves for the authenticated user
   * @returns Promise containing an array of leaves and total count
   */
  async listAllLeaves(): Promise<{ leaves: Leaves[]; totalCount: number }> {
    try {
      const resourceId = this.authService.userId();

      const queryParams = new URLSearchParams({
        resourceId,
      });

      const response = await lastValueFrom(
        this.http.get<{ data: Leaves[]; count: number }>(
          `${endpoints.leaves.list}?${queryParams}`
        )
      );

      const extracted: [Leaves[], number] = [response.data, response.count];
      const responseValid = validateListAllLeavesResponse(extracted);

      return { leaves: responseValid[0], totalCount: responseValid[1] };
    } catch (error) {
      if (error instanceof HttpErrorResponse) {
        const errorMessage =
          error.error?.message || 'Unexpected error occurred.';
        throw new Error(`Error ${error.status}: ${errorMessage}`);
      }
      throw new Error((error as Error).message);
    }
  }

  /**
   * Delete a leave request
   */
  async deleteLeave(leaveId: string): Promise<DeleteLeave> {
    try {
      if (!leaveId) {
        throw new Error('Leave ID is required.');
      }

      const response = await lastValueFrom(
        this.http.request<DeleteLeave>(
          'delete',
          endpoints.leaves.delete.replace(':id', leaveId),
          {
            body: { id: leaveId },
          }
        )
      );
      return validateDeleteLeaveResponse(response);
    } catch (error) {
      if (error instanceof HttpErrorResponse) {
        const errorMessage =
          error.error?.message ||
          'Unexpected error occurred while deleting leave.';
        throw new Error(`Error ${error.status}: ${errorMessage}`);
      }
      throw (error as ApiErrorResponse).error;
    }
  }
}
