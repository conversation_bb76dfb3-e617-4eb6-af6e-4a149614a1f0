import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { lastValueFrom } from 'rxjs';
import { endpoints } from '../../endpoints';
import { AuthService } from '../auth/auth-service';
import { ApiErrorResponse } from '../common.model';
import {
  Leaves,
  DeleteLeave,
  validateDeleteLeaveResponse,
  validateListAllLeavesResponse,
  LeavesQueryParams,
  LeavesApiResponse,
} from './leave.model';

@Injectable({
  providedIn: 'root',
})
export class LeaveService {
  constructor(
    private authService: AuthService,
    private http: HttpClient
  ) {}

  /**
   * Fetch all leaves for the authenticated user with pagination
   * @param params - Pagination parameters
   * @returns Promise containing the API response with leaves, count, and pagination info
   */
  async listAllLeaves(params: LeavesQueryParams): Promise<LeavesApiResponse> {
    try {
      const queryParams = new URLSearchParams({
        resourceId: params.resourceId,
        page: params.page.toString(),
        limit: params.limit.toString(),
      });

      const response = await lastValueFrom(
        this.http.get<LeavesApiResponse>(
          `${endpoints.leaves.list}?${queryParams}`
        )
      );

      return validateListAllLeavesResponse(response);
    } catch (error) {
      if (error instanceof HttpErrorResponse) {
        const errorMessage =
          error.error?.message || 'Unexpected error occurred.';
        throw new Error(`Error ${error.status}: ${errorMessage}`);
      }
      throw new Error((error as Error).message);
    }
  }

  /**
   * Delete a leave request
   */
  async deleteLeave(leaveId: string): Promise<DeleteLeave> {
    try {
      if (!leaveId) {
        throw new Error('Leave ID is required.');
      }

      const response = await lastValueFrom(
        this.http.request<DeleteLeave>(
          'delete',
          endpoints.leaves.delete.replace(':id', leaveId),
          {
            body: { id: leaveId },
          }
        )
      );
      return validateDeleteLeaveResponse(response);
    } catch (error) {
      if (error instanceof HttpErrorResponse) {
        const errorMessage =
          error.error?.message ||
          'Unexpected error occurred while deleting leave.';
        throw new Error(`Error ${error.status}: ${errorMessage}`);
      }
      throw (error as ApiErrorResponse).error;
    }
  }
}
