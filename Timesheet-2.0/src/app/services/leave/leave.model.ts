import { z } from 'zod';
import { fromZodError } from 'zod-validation-error';

const LeavesSchema = z.object({
  id: z.string(),
  fromDate: z
    .string()
    .datetime()
    .transform((val) => new Date(Date.parse(val))),
  toDate: z
    .string()
    .datetime()
    .transform((val) => new Date(Date.parse(val))),
  resourceId: z.string(),
  typeOfLeaveForFrom: z.string(),
  typeOfLeaveForTo: z.string(),
  createdAt: z
    .string()
    .datetime()
    .transform((val) => new Date(Date.parse(val))),
  updatedAt: z
    .string()
    .datetime()
    .transform((val) => new Date(Date.parse(val))),
  resource: z
    .object({
      id: z.string().optional(),
      name: z.string().optional(),
      email: z.string().nullable().optional(),
      kekaId: z.string().nullable().optional(),
    })
    .optional(),
  approver: z.string().nullable().optional(),
});

export const DeleteLeaveSchema = z.object({
  statusCode: z.number(),
  data: z.boolean(),
  message: z.string(),
});

export type Leaves = z.infer<typeof LeavesSchema>;
export type DeleteLeave = z.infer<typeof DeleteLeaveSchema>;

export interface LeaveFormData {
  fromDate: Date;
  toDate: Date;
  typeOfLeaveForFrom: string;
  typeOfLeaveForTo: string;
}

/**
 * Validates the response from the API for listing all leaves.
 *  It checks if the data matches the expected schema and returns the validated data.
 * If the data is invalid, it throws a validation error.
 * @param data - The response data from the API, expected to be a tuple of [Leaves[], number].
 * @return A tuple containing an array of Leaves and a number representing the total count.
 */
export function validateListAllLeavesResponse(
  data: [Leaves[], number]
): [Leaves[], number] {
  const result = z.tuple([z.array(LeavesSchema), z.number()]).safeParse(data);
  if (result.success) {
    return result.data;
  } else if (!data[0]) {
    return [[], data[1]];
  } else {
    const validationError = fromZodError(result.error as z.ZodError);
    throw validationError;
  }
}

/**
 * Validates the response from the API for deleting a leave request.
 * It checks if the data matches the expected schema and returns the validated data.
 * If the data is invalid, it throws a validation error.
 * @param data - The response data from the API, expected to match DeleteLeaveSchema.
 * @return The validated DeleteLeave object.
 */
export function validateDeleteLeaveResponse(data: DeleteLeave): DeleteLeave {
  const result = DeleteLeaveSchema.safeParse(data);
  if (result.success) {
    return data;
  } else {
    const validationError = fromZodError(result.error as z.ZodError);
    throw validationError;
  }
}
