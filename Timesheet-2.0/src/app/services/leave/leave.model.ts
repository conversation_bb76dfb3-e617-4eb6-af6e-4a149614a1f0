import { z } from 'zod';
import { fromZodError } from 'zod-validation-error';

const LeavesSchema = z.object({
  id: z.string(),
  fromDate: z
    .string()
    .datetime()
    .transform((val) => new Date(Date.parse(val))),
  toDate: z
    .string()
    .datetime()
    .transform((val) => new Date(Date.parse(val))),
  resourceId: z.string(),
  typeOfLeaveForFrom: z.string(),
  typeOfLeaveForTo: z.string(),
  createdAt: z
    .string()
    .datetime()
    .transform((val) => new Date(Date.parse(val))),
  updatedAt: z
    .string()
    .datetime()
    .transform((val) => new Date(Date.parse(val))),
  resource: z
    .object({
      id: z.string().optional(),
      name: z.string().optional(),
      email: z.string().nullable().optional(),
      kekaId: z.string().nullable().optional(),
    })
    .optional(),
  approver: z.string().nullable().optional(),
});

export const DeleteLeaveSchema = z.object({
  statusCode: z.number(),
  data: z.boolean(),
  message: z.string(),
});

// New pagination schema for the API response
export const PaginationSchema = z.object({
  page: z.number(),
  limit: z.number(),
  totalPages: z.number(),
  totalCount: z.number(),
});

// New API response schema
export const LeavesApiResponseSchema = z.object({
  statusCode: z.number(),
  data: z.array(LeavesSchema),
  count: z.number(),
  pagination: PaginationSchema,
  message: z.string(),
});

export type Leaves = z.infer<typeof LeavesSchema>;
export type DeleteLeave = z.infer<typeof DeleteLeaveSchema>;
export type Pagination = z.infer<typeof PaginationSchema>;
export type LeavesApiResponse = z.infer<typeof LeavesApiResponseSchema>;

// Pagination parameters interface
export interface LeavesQueryParams {
  resourceId: string;
  page: number;
  limit: number;
}

export interface LeaveFormData {
  fromDate: Date;
  toDate: Date;
  typeOfLeaveForFrom: string;
  typeOfLeaveForTo: string;
}

/**
 * Validates the response from the API for listing all leaves.
 * It checks if the data matches the expected schema and returns the validated data.
 * If the data is invalid, it throws a validation error.
 * @param data - The response data from the API, expected to match LeavesApiResponseSchema.
 * @return The validated LeavesApiResponse object.
 */
export function validateListAllLeavesResponse(data: any): LeavesApiResponse {
  const result = LeavesApiResponseSchema.safeParse(data);
  if (result.success) {
    return result.data;
  } else {
    const validationError = fromZodError(result.error as z.ZodError);
    throw validationError;
  }
}

/**
 * Validates the response from the API for deleting a leave request.
 * It checks if the data matches the expected schema and returns the validated data.
 * If the data is invalid, it throws a validation error.
 * @param data - The response data from the API, expected to match DeleteLeaveSchema.
 * @return The validated DeleteLeave object.
 */
export function validateDeleteLeaveResponse(data: DeleteLeave): DeleteLeave {
  const result = DeleteLeaveSchema.safeParse(data);
  if (result.success) {
    return data;
  } else {
    const validationError = fromZodError(result.error as z.ZodError);
    throw validationError;
  }
}
