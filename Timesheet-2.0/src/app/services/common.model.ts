export type ValidatorTypes =
  | 'min'
  | 'max'
  | 'pattern'
  | 'minlength'
  | 'maxlength'
  | 'email'
  | 'required';

export interface ApiResponse<T> {
  statusCode: number;
  data: T;
  message: string;
}

export interface ApiErrorResponse {
  statusCode: number;
  message: string;
  error: string;
}

export interface UpdateServiceResponse {
  status: string;
  message: string;
}

export interface Option {
  label: string;
  value: string;
}

export type ErrorMessage<T> = {
  [K in keyof T]?: { [key in ValidatorTypes]?: string };
};
