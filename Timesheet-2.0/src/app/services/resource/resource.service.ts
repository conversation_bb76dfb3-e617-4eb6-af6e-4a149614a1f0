import { HttpClient } from '@angular/common/http';
import { Injectable, Injector, runInInjectionContext } from '@angular/core';
import { lastValueFrom } from 'rxjs';
import { endpoints } from '../../endpoints';
import { Resource, validateResourcesList } from './resource.model';
import { injectQuery } from '@tanstack/angular-query-experimental';
import { AuthService } from '../auth/auth-service';

@Injectable({
  providedIn: 'root',
})
export class ResourceService {
  constructor(
    private http: HttpClient,
    private authService: AuthService,
    private injector: Injector
  ) {}

  /**
   * Query to fetch list of resources.
   */
  resourcesQuery = injectQuery(() => ({
    queryKey: ['resourcesDetails', this.authService.userId()],
    queryFn: async () => {
      const resources = await this.getResourcesList();
      return resources.map((resource) => ({
        id: resource.id,
        name: resource.name,
        profilePicUrl: resource.profilePicUrl,
        kekaId: resource.kekaId.toUpperCase(),
        designation: resource.designation,
      }));
    },
    onError: (error: Error) => {
      console.error('Error fetching resource details:', error);
    },
  }));

  /**
   * It fetches a list of resources using TanStack Query.
   *
   * It retrieves the resource list from `this.getResourcesList()`, transforms each resource
   * to include only the `id`, `name`, and `profilePicUrl`, and automatically refetches the data
   */
  getResourcesQuery() {
    return runInInjectionContext(this.injector, () =>
      injectQuery(() => ({
        queryKey: ['resourcesDetails', this.authService.userId()],
        queryFn: async () => {
          const resources = await this.getResourcesList();
          return resources.map((resource) => ({
            id: resource.id,
            name: resource.name,
            profilePicUrl: resource.profilePicUrl,
          }));
        },
        onError: (error: Error) => {
          console.error('Error fetching resource details:', error);
        },
      }))
    );
  }

  async getResourcesList() {
    try {
      const response = await lastValueFrom(
        this.http.get<{ data: Resource[] }>(`${endpoints.resource.list}`)
      );
      return validateResourcesList(response.data);
    } catch (error) {
      console.error('Failed to fetch resources:', error);
      throw new Error('Failed to fetch resources. Please try again later.');
    }
  }
}
