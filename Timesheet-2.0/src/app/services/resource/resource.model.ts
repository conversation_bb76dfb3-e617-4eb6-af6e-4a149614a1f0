import { z } from 'zod';
import { fromZodError } from 'zod-validation-error';

export const ResourceContractBillingRateSchema = z.object({
  id: z.string().uuid(),
  rate: z.number(),
  interval: z.enum([
    'monthly',
    'weekly',
    'daily',
    'hourly',
    'quarterly',
    'yearly',
  ]),
  currency: z.string(),
  contractId: z.string().uuid(),
  resourceId: z.string().uuid(),
});

export const departmentSchema = z.object({
  id: z.string().uuid(),
  departmentName: z.string(),
});
const DepartmentSchema = z.object({
  id: z.string(),
  departmentName: z.string(),
});

const ResourceSchema = z.object({
  id: z.string().uuid(),
  name: z.string(),
  email: z.string().email(),
  department: DepartmentSchema,
  role: z.string(),
  designation: z.string().nullable(),
  phoneNumber: z.string().nullable(),
  managerId: z.string().uuid().optional().nullable(),
  location: z.string().optional().nullable(),
  kekaId: z.string(),
  createdAt: z.string().datetime().optional(),
  updatedAt: z.string().datetime().optional(),
  profilePicUrl: z.string().optional().nullable(),
  currency: z.string().optional().nullable(),
  expectedHours: z.number().optional().nullable(),
  expectedHoursFrequency: z.enum(['weekly', 'daily']).optional().nullable(),
  resourceContractor: z.boolean().optional().nullable(),
  deleted: z.boolean().optional(),
  resourceContractBillingRate: z
    .array(ResourceContractBillingRateSchema)
    .optional()
    .nullable(),
});

export type Resource = z.infer<typeof ResourceSchema>;

export function validateResourcesList(data: Resource[]): Resource[] {
  const result = z.array(ResourceSchema).safeParse(data);
  if (result.success) {
    return data;
  } else {
    const validationError = fromZodError(result.error as z.ZodError);
    throw validationError;
  }
}
