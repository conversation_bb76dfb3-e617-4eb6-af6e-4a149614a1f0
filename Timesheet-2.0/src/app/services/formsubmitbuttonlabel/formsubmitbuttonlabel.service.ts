import { Injectable } from '@angular/core';

/**
 * ButtonLabelService is an Angular service responsible for managing
 * the labels and icons of buttons used in forms throughout the application.
 */
@Injectable({
  providedIn: 'root',
})
export class FormSubmitButtonLabelService {
  /**
   * Returns the appropriate label for the form submit button
   * based on whether the form is currently being submitted
   * and whether it is in edit mode.
   */
  getFormSubmitButtonLabel(isSubmitting: boolean, isEditMode: boolean): string {
    if (isSubmitting) {
      return isEditMode ? 'Saving...' : 'Adding...';
    }
    return isEditMode ? 'Save' : 'Add';
  }

  /**
   * Returns the appropriate icon class for the form submit button
   * based on whether the form is currently being submitted.
   */
  getFormSubmitButtonIcon(isSubmitting: boolean): string {
    return isSubmitting ? 'pi pi-spin pi-spinner' : '';
  }
}
