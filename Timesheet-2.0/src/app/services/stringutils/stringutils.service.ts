import { Injectable } from '@angular/core';
import { ContractHistoryResponse } from '../contract/contract.model';
import { ProjectHistoryResponse, TimelineData } from '../project/project.model';

@Injectable({
  providedIn: 'root',
})
export class StringUtilsService {
  /**
   * Utility service to capitalize the first letter of a string.
   * If the input is undefined or empty, it returns an empty string.
   */
  capitalizeFirstLetter = (value: string | undefined): string => {
    if (!value) return 'N/A';
    return value.charAt(0).toUpperCase() + value.slice(1);
  };

  /**
   * Utility service to capitalize the first three characters of an employee number.
   * If the input is undefined or empty, it returns an empty string.
   */
  capitalizeEmployeeNumber = (employeeNo: string | undefined): string => {
    if (!employeeNo) return '';
    return employeeNo.replace(/^(.{3})/, (match) => match.toUpperCase());
  };

  /**
   * Utility service to get the value before the hyphen in a given string.
   * If the input is undefined or empty, it returns an empty string.
   */
  getValueBeforeHyphen(input: string | undefined): string {
    return input ? input.split('-')[0]?.trim() : '';
  }

  /**
   * Converts an array of project history records into a structured format
   * suitable for displaying in a timeline.
   */
  convertProjectHistoryToTimelineData(
    projectContractHistory: ProjectHistoryResponse[]
  ) {
    const timelineData: TimelineData[] = [];

    projectContractHistory.forEach((project) => {
      let subtitleParts: string[] = [];

      if (project.projectManagerName)
        subtitleParts.push(project.projectManagerName);

      if (project.resourceList && project.resourceList.length > 0) {
        const resourceNames = project.resourceList.map(
          (resource) => resource.resourceName
        );
        subtitleParts.push(...resourceNames);
      }

      if (project.addedResourceList && project.addedResourceList.length > 0) {
        const resourceNames = project.addedResourceList.map(
          (resource) => resource.resourceName
        );
        subtitleParts.push(...resourceNames);
        subtitleParts.push('(added)');
      }

      if (
        project.removedResourceList &&
        project.removedResourceList.length > 0
      ) {
        const resourceNames = project.removedResourceList.map(
          (resource) => resource.resourceName
        );
        subtitleParts.push(...resourceNames);
        subtitleParts.push('(removed)');
      }

      if (project.unassignedResourceName)
        subtitleParts.push(project.unassignedResourceName);

      const formattedSubtitle = subtitleParts
        .join(', ')
        .replace(/, \(removed\)$/, '(removed)')
        .replace(/, \(added\)$/, '(added)');

      timelineData.push({
        id: project.projectId,
        date: project.date,
        title: project.action,
        author: project.personName,
        subTitle: formattedSubtitle,
      });
    });
    return timelineData;
  }

  /**
   * Converts an array of contract history records into a structured format
   * suitable for displaying in a timeline.
   */
  convertContractHistoryToTimelineData(
    contractHistory: ContractHistoryResponse[] = []
  ): TimelineData[] {
    return contractHistory.map((contract) => {
      let subtitleParts: (string | undefined)[] = [];

      // Extract names from contractManagers
      if (contract.contractManagers?.length) {
        contract.contractManagers.forEach((manager) => {
          if (manager?.contractManagerName)
            subtitleParts.push(manager.contractManagerName);
          if (manager?.resourceName) subtitleParts.push(manager.resourceName);
        });
      }

      // Extract names from resources
      if (contract.resources?.length) {
        contract.resources
          .map((resource) => resource?.resourceName)
          .filter(Boolean)
          .forEach((name) => subtitleParts.push(name));
      }

      // Build the timeline entry
      return {
        id: contract.id,
        date: contract.date,
        title: contract.action,
        author: contract.assigneeResource?.name || 'Unknown',
        subTitle: subtitleParts.join(', '),
      };
    });
  }

  /**
   * Returns the CSS styles for a status tag based on the provided status.
   *
   * This function checks the value of the status parameter and returns a string
   * containing the appropriate CSS classes for styling the tag.
   *
   * @param status The status of the project, which can be 'active', 'completed', or undefined.
   * @returns A string containing the CSS classes for the corresponding status.
   */
  getStatusTagStyles(status: string | undefined): string {
    return status === 'active'
      ? 'bg-green-200 text-green-700'
      : status === 'completed'
        ? 'bg-primary-200 text-primary-700'
        : 'bg-red-200 text-red-700';
  }

  /**
   * Returns an object containing the status text and CSS styles for a client status tag.
   *
   * This function checks the value of the status parameter and returns an object with
   * the appropriate status text and CSS classes for styling the tag.
   *
   * @param status A boolean indicating whether the client is active or inactive.
   * @returns An object containing the status text and CSS classes for the corresponding status.
   */
  getClientStatusInfo(status: boolean) {
    const baseClass =
      'px-5 flex items-center justify-center text-xs font-bold rounded-md';
    return status
      ? { status: 'Active', style: `${baseClass} bg-green-100 text-green-600` }
      : { status: 'InActive', style: `${baseClass} bg-gray-100 text-gray-600` };
  }
}
