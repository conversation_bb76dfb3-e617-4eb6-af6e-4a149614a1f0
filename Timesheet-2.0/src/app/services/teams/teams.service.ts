import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Injectable, Injector, runInInjectionContext } from '@angular/core';
import { injectQuery } from '@tanstack/angular-query-experimental';
import { lastValueFrom } from 'rxjs';
import { endpoints } from '../../endpoints';
import { AuthService } from '../auth/auth-service';
import { ApiErrorResponse, ApiResponse } from '../common.model';
import {
  Resource,
  TeamsApiResponse,
  TeamsQueryParams,
  validateTeamsApiResponse,
} from './teams.model';

/**
 * TeamsService
 *
 * This service is responsible for managing teams/employees-related operations,
 * including fetching a list of employees from the API with filtering capabilities.
 * It uses Angular's HttpClient for making HTTP requests and integrates with
 * an authentication service to ensure that requests are made by authenticated users.
 */
@Injectable({
  providedIn: 'root',
})
export class TeamsService {
  constructor(
    private authService: AuthService,
    private http: HttpClient,
    private injector: Injector
  ) {}

  /**
   * Creates a query to fetch teams/employees data with filters using React Query.
   *
   * @param params - Query parameters for filtering and pagination
   * @returns A query object that can be used to fetch and manage filtered teams data.
   */
  getTeamsQuery(params: TeamsQueryParams) {
    return runInInjectionContext(this.injector, () =>
      injectQuery(() => ({
        queryKey: ['getTeamsWithFilters', this.authService.userId(), params],
        queryFn: () => this.getTeamsList(params),
      }))
    );
  }

  /**
   * Fetches the list of teams/employees from the API with filters.
   */
  async getTeamsList(params: TeamsQueryParams): Promise<TeamsApiResponse> {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();
      queryParams.append('pageNumber', params.pageNumber.toString());
      queryParams.append('countPerPage', params.countPerPage.toString());

      if (params.department) {
        queryParams.append('department', params.department);
      }
      if (params.search) {
        queryParams.append('search', params.search);
      }
      if (params.location) {
        queryParams.append('location', params.location);
      }
      if (params.role) {
        queryParams.append('role', params.role);
      }

      const url = `${endpoints.teams.list}?${queryParams.toString()}`;
      const response = await lastValueFrom(
        this.http.get<TeamsApiResponse>(url)
      );

      return validateTeamsApiResponse(response);
    } catch (error) {
      if (error instanceof HttpErrorResponse) {
        const errorMessage =
          error.error?.message || 'An unexpected error occurred.';
        if (error.status >= 400 && error.status < 500) {
          throw new Error(`Failed to get teams with filters: ${errorMessage}`);
        } else if (error.status >= 500) {
          throw new Error(
            `An unexpected server error occurred: ${errorMessage}`
          );
        }
      }
      throw new Error((error as Error).message);
    }
  }

  /**
   * Updates an employee's status (activate/deactivate).
   */
  async updateEmployeeStatus(
    employeeId: string,
    status: boolean
  ): Promise<string> {
    try {
      const response = await lastValueFrom(
        this.http.put<{ statusCode: number; message: string }>(
          `${endpoints.teams.updateStatus.replace(':employeeId', employeeId)}`,
          { deleted: !status }
        )
      );

      if (response.statusCode === 200) {
        return 'success';
      } else {
        throw new Error(response.message);
      }
    } catch (error: unknown) {
      if (error instanceof HttpErrorResponse) {
        const errorMessage =
          error.error?.message || 'An unexpected error occurred.';
        if (error.status >= 400 && error.status < 500) {
          throw new Error(`Failed to update employee status: ${errorMessage}`);
        } else if (error.status >= 500) {
          throw new Error(
            'An unexpected server error occurred. Please try again later.'
          );
        }
      }
      throw new Error((error as Error).message);
    }
  }

  /**
   * Bulk upload employees from file.
   */
  async bulkUploadEmployees(file: File): Promise<{ message: string }> {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await lastValueFrom(
        this.http.post<{ statusCode: number; message: string }>(
          endpoints.teams.bulkUpload,
          formData
        )
      );

      if (response.statusCode === 200 || response.statusCode === 201) {
        return { message: response.message };
      } else {
        throw new Error(response.message);
      }
    } catch (error: unknown) {
      if (error instanceof HttpErrorResponse) {
        const errorMessage =
          error.error?.message || 'An unexpected error occurred.';
        throw new Error(`Bulk upload failed: ${errorMessage}`);
      }
      throw new Error((error as Error).message);
    }
  }
}
