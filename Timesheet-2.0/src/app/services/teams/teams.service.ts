import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { lastValueFrom } from 'rxjs';
import { endpoints } from '../../endpoints';
import { AuthService } from '../auth/auth-service';
import { ApiErrorResponse, ApiResponse } from '../common.model';
import {
  Resource,
  ResourceList,
  ResourceListApiResponse,
  TeamsApiResponse,
  TeamsQueryParams,
  validateResourceListApiResponse,
  validateTeamsApiResponse,
  AddEmployee,
  UpdateEmployee,
} from './teams.model';

/**
 * TeamsService
 *
 * This service is responsible for managing teams/employees-related operations,
 * including fetching a list of employees from the API with filtering capabilities.
 * It uses Angular's HttpClient for making HTTP requests and integrates with
 * an authentication service to ensure that requests are made by authenticated users.
 */
@Injectable({
  providedIn: 'root',
})
export class TeamsService {
  constructor(
    private authService: AuthService,
    private http: HttpClient
  ) {}

  /**
   * Fetches the list of teams/employees from the API with filters.
   */
  async getTeamsList(params: TeamsQueryParams): Promise<TeamsApiResponse> {
    try {
      // Build query parameters
      const queryParams = new URLSearchParams();
      queryParams.append('pageNumber', params.pageNumber.toString());
      queryParams.append('countPerPage', params.countPerPage.toString());

      if (params.department) {
        queryParams.append('department', params.department);
      }
      if (params.search) {
        queryParams.append('search', params.search);
      }
      if (params.location) {
        queryParams.append('location', params.location);
      }
      if (params.role) {
        queryParams.append('role', params.role);
      }

      const url = `${endpoints.teams.list}?${queryParams.toString()}`;
      const response = await lastValueFrom(
        this.http.get<TeamsApiResponse>(url)
      );

      return validateTeamsApiResponse(response);
    } catch (error) {
      if (error instanceof HttpErrorResponse) {
        const errorMessage =
          error.error?.message || 'An unexpected error occurred.';
        if (error.status >= 400 && error.status < 500) {
          throw new Error(`Failed to get teams with filters: ${errorMessage}`);
        } else if (error.status >= 500) {
          throw new Error(
            `An unexpected server error occurred: ${errorMessage}`
          );
        }
      }
      throw new Error((error as Error).message);
    }
  }

  /**
   * Updates an employee's status (activate/deactivate).
   */
  async updateEmployeeStatus(
    employeeId: string,
    status: boolean
  ): Promise<string> {
    try {
      const response = await lastValueFrom(
        this.http.put<{ statusCode: number; message: string }>(
          `${endpoints.teams.updateStatus.replace(':employeeId', employeeId)}`,
          { deleted: !status }
        )
      );

      if (response.statusCode === 200) {
        return 'success';
      } else {
        throw new Error(response.message);
      }
    } catch (error: unknown) {
      if (error instanceof HttpErrorResponse) {
        const errorMessage =
          error.error?.message || 'An unexpected error occurred.';
        if (error.status >= 400 && error.status < 500) {
          throw new Error(`Failed to update employee status: ${errorMessage}`);
        } else if (error.status >= 500) {
          throw new Error(
            'An unexpected server error occurred. Please try again later.'
          );
        }
      }
      throw new Error((error as Error).message);
    }
  }

  /**
   * Bulk upload employees from file.
   */
  async bulkUploadEmployees(file: File): Promise<{ message: string }> {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await lastValueFrom(
        this.http.post<{ statusCode: number; message: string }>(
          endpoints.teams.bulkUpload,
          formData
        )
      );

      if (response.statusCode === 200 || response.statusCode === 201) {
        return { message: response.message };
      } else {
        throw new Error(response.message);
      }
    } catch (error: unknown) {
      if (error instanceof HttpErrorResponse) {
        const errorMessage =
          error.error?.message || 'An unexpected error occurred.';
        throw new Error(`Bulk upload failed: ${errorMessage}`);
      }
      throw new Error((error as Error).message);
    }
  }

  /**
   * Get employees for reporting dropdown (managers, admins, etc.)
   * or get a single employee by ID for edit form
   */
  async getEmployeesForReporting(resourceId?: string): Promise<ResourceList[]> {
    try {
      let url: string;

      if (resourceId) {
        // Get single employee by ID for edit form
        url = `${endpoints.teams.resourceList}?resourceId=${resourceId}`;
      } else {
        // Get all employees for reporting dropdown
        const queryParams = new URLSearchParams();
        queryParams.append('role', 'admin');
        queryParams.append('role', 'manager');
        queryParams.append('role', 'finance');
        queryParams.append('role', 'employee');
        url = `${endpoints.teams.resourceList}?${queryParams.toString()}`;
      }

      const response = await lastValueFrom(
        this.http.get<ResourceListApiResponse>(url)
      );
      const validatedResponse = validateResourceListApiResponse(response);
      return validatedResponse.data || [];
    } catch (error) {
      if (error instanceof HttpErrorResponse) {
        const errorMessage =
          error.error?.message || 'An unexpected error occurred.';
        throw new Error(`Failed to get employees: ${errorMessage}`);
      }
      throw new Error((error as Error).message);
    }
  }

  /**
   * Add a new employee.
   */
  async addEmployee(
    employeeData: AddEmployee
  ): Promise<{ statusCode: number; message: string }> {
    try {
      const response = await lastValueFrom(
        this.http.post<{ statusCode: number; message: string }>(
          endpoints.teams.add,
          employeeData
        )
      );

      if (response.statusCode === 200 || response.statusCode === 201) {
        return response;
      } else {
        throw new Error(response.message);
      }
    } catch (error: unknown) {
      if (error instanceof HttpErrorResponse) {
        const errorMessage =
          error.error?.message || 'An unexpected error occurred.';
        throw new Error(`Failed to add employee: ${errorMessage}`);
      }
      throw new Error((error as Error).message);
    }
  }

  /**
   * Get a single employee by ID for edit form
   */
  async getEmployeeById(employeeId: string): Promise<ResourceList> {
    try {
      const employees = await this.getEmployeesForReporting(employeeId);
      if (employees.length === 0) {
        throw new Error('Employee not found');
      }
      return employees[0];
    } catch (error) {
      if (error instanceof HttpErrorResponse) {
        const errorMessage =
          error.error?.message || 'An unexpected error occurred.';
        throw new Error(`Failed to get employee: ${errorMessage}`);
      }
      throw new Error((error as Error).message);
    }
  }

  /**
   * Update an existing employee.
   */
  async updateEmployee(
    employeeData: UpdateEmployee
  ): Promise<{ statusCode: number; message: string }> {
    try {
      const response = await lastValueFrom(
        this.http.put<{ statusCode: number; message: string }>(
          endpoints.teams.update.replace(
            ':employeeId',
            employeeData.employeeId
          ),
          employeeData
        )
      );

      if (response.statusCode === 200 || response.statusCode === 201) {
        return response;
      } else {
        throw new Error(response.message);
      }
    } catch (error: unknown) {
      if (error instanceof HttpErrorResponse) {
        const errorMessage =
          error.error?.message || 'An unexpected error occurred.';
        throw new Error(`Failed to update employee: ${errorMessage}`);
      }
      throw new Error((error as Error).message);
    }
  }

  /**
   * Deactivate an employee.
   */
  async deactivateEmployee(
    employeeId: string
  ): Promise<{ statusCode: number; message: string }> {
    try {
      const response = await lastValueFrom(
        this.http.delete<{ statusCode: number; message: string }>(
          `${endpoints.teams.list}/${employeeId}`
        )
      );

      if (response.statusCode === 200 || response.statusCode === 204) {
        return response;
      } else {
        throw new Error(response.message);
      }
    } catch (error: unknown) {
      if (error instanceof HttpErrorResponse) {
        const errorMessage =
          error.error?.message || 'An unexpected error occurred.';
        throw new Error(`Failed to deactivate employee: ${errorMessage}`);
      }
      throw new Error((error as Error).message);
    }
  }

  /**
   * Reactivate an employee.
   */
  async reactivateEmployee(
    employeeId: string
  ): Promise<{ statusCode: number; message: string }> {
    try {
      const response = await lastValueFrom(
        this.http.put<{ statusCode: number; message: string }>(
          `${endpoints.teams.reactivate}/${employeeId}/reactivate`,
          {}
        )
      );

      if (response.statusCode === 200 || response.statusCode === 204) {
        return response;
      } else {
        throw new Error(response.message);
      }
    } catch (error: unknown) {
      if (error instanceof HttpErrorResponse) {
        const errorMessage =
          error.error?.message || 'An unexpected error occurred.';
        throw new Error(`Failed to reactivate employee: ${errorMessage}`);
      }
      throw new Error((error as Error).message);
    }
  }
}
