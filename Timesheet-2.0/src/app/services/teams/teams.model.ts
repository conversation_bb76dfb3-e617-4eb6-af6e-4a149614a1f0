import { z } from 'zod';
import { fromZodError } from 'zod-validation-error';

// Department schema for nested department object
const DepartmentSchema = z.object({
  departmentName: z.string(),
});

// Resource/Employee schema based on API response
export const ResourceSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string(),
  role: z.string(),
  canAccessAssetManagement: z.boolean(),
  phoneNumber: z.string().nullable().optional(),
  departmentId: z.string(),
  designation: z.string().nullable().optional(),
  managerId: z.string().nullable().optional(),
  reportingTo: z.string().nullable().optional(),
  location: z.string(),
  kekaId: z.string().nullable().optional(),
  createdAt: z.string(),
  updatedAt: z.string(),
  profilePicUrl: z.string().nullable().optional(),
  currency: z.string().nullable().optional(),
  expectedHours: z.number().nullable().optional(),
  expectedHoursFrequency: z.string().nullable().optional(),
  frequency: z.string().nullable().optional(),
  resourceContractor: z.boolean(),
  deleted: z.boolean(),
  department: DepartmentSchema.optional(),
});

export const ResourceListSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string().email(),
  role: z.string(),
  canAccessAssetManagement: z.boolean(),
  phoneNumber: z.string().nullable().optional(),
  department: z
    .object({
      id: z.string(),
      departmentName: z.string(),
    })
    .nullable()
    .optional(),
  designation: z.string().nullable().optional(),
  managerId: z.string().nullable().optional(),
  location: z.string().nullable().optional(),
  kekaId: z.string().nullable().optional(),
  createdAt: z.string().datetime().nullable().optional(),
  updatedAt: z.string().datetime().nullable().optional(),
  profilePicUrl: z.string().url().nullable().optional(),
  currency: z.string().nullable().optional(),
  expectedHours: z.number().nullable().optional(),
  expectedHoursFrequency: z.string().nullable().optional(),
  resourceContractor: z.boolean().nullable().optional(),
  deleted: z.boolean().nullable().optional(),
});

// Add Employee schema for creating new employee
export const AddEmployeeSchema = z.object({
  name: z.string(),
  email: z.string().email(),
  phoneNumber: z.string(),
  kekaId: z.string(),
  location: z.string(),
  department: z.string(),
  designation: z.string(),
  managerId: z.string(),
  currency: z.string(),
  expectedHours: z.number(),
  expectedHoursFrequency: z.string(),
  role: z.string(),
  resourceContractor: z.boolean(),
  canAccessAssetManagement: z.boolean(),
});

// Update Employee schema for editing existing employee
export const UpdateEmployeeSchema = z.object({
  employeeId: z.string(),
  name: z.string().optional(),
  email: z.string().email().optional(),
  phoneNumber: z.string().optional(),
  kekaId: z.string().optional(),
  location: z.string().optional(),
  department: z.string().optional(),
  designation: z.string().optional(),
  managerId: z.string().optional(),
  currency: z.string().optional(),
  expectedHours: z.number().optional(),
  expectedHoursFrequency: z.string().optional(),
  role: z.string().optional(),
  resourceContractor: z.boolean().optional(),
  canAccessAssetManagement: z.boolean().optional(),
});

// API response schema for the full response structure
export const TeamsApiResponseSchema = z.object({
  statusCode: z.number(),
  data: z.array(ResourceSchema),
  count: z.number(),
  message: z.string(),
});

export const ResourceListApiResponseSchema = z.object({
  statusCode: z.number(),
  data: z.array(ResourceListSchema),
  message: z.string(),
});

// Legacy schema for backward compatibility (just the array)
export const TeamsListResponseSchema = z.array(ResourceSchema);

// Query parameters interface for filtering
export interface TeamsQueryParams {
  pageNumber: number;
  countPerPage: number;
  department?: string;
  search?: string;
  location?: string;
  role?: string;
}

// Types
export type Resource = z.infer<typeof ResourceSchema>;
export type ResourceList = z.infer<typeof ResourceListSchema>;
export type TeamsApiResponse = z.infer<typeof TeamsApiResponseSchema>;
export type TeamsListResponse = z.infer<typeof TeamsListResponseSchema>;
export type AddEmployee = z.infer<typeof AddEmployeeSchema>;
export type UpdateEmployee = z.infer<typeof UpdateEmployeeSchema>;

export type ResourceListApiResponse = z.infer<
  typeof ResourceListApiResponseSchema
>;
export type Department = z.infer<typeof DepartmentSchema>;

// Validation functions
export function validateTeamsApiResponse(data: any): TeamsApiResponse {
  const result = TeamsApiResponseSchema.safeParse(data);
  if (result.success) {
    return result.data;
  } else {
    const validationError = fromZodError(result.error);
    throw validationError;
  }
}
export function validateResourceListApiResponse(
  data: any
): ResourceListApiResponse {
  const result = ResourceListApiResponseSchema.safeParse(data);
  if (result.success) {
    return result.data;
  } else {
    const validationError = fromZodError(result.error);
    throw validationError;
  }
}
