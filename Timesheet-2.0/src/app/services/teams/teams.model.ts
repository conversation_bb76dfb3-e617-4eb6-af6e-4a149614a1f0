import { z } from 'zod';
import { fromZodError } from 'zod-validation-error';

// Department schema for nested department object
const DepartmentSchema = z.object({
  departmentName: z.string(),
});

// Resource/Employee schema based on API response
export const ResourceSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string(),
  role: z.string(),
  canAccessAssetManagement: z.boolean(),
  phoneNumber: z.string().nullable().optional(),
  departmentId: z.string(),
  designation: z.string(),
  managerId: z.string().nullable().optional(),
  location: z.string(),
  kekaId: z.string(),
  createdAt: z.string(),
  updatedAt: z.string(),
  profilePicUrl: z.string().nullable().optional(),
  currency: z.string().nullable().optional(),
  expectedHours: z.number(),
  expectedHoursFrequency: z.string(),
  resourceContractor: z.boolean(),
  deleted: z.boolean(),
  department: DepartmentSchema.optional(),
});

// API response schema for the full response structure
export const TeamsApiResponseSchema = z.object({
  statusCode: z.number(),
  data: z.array(ResourceSchema),
  count: z.number(),
  message: z.string(),
});

// Legacy schema for backward compatibility (just the array)
export const TeamsListResponseSchema = z.array(ResourceSchema);

// Query parameters interface for filtering
export interface TeamsQueryParams {
  pageNumber: number;
  countPerPage: number;
  department?: string;
  search?: string;
  location?: string;
  role?: string;
}

// Types
export type Resource = z.infer<typeof ResourceSchema>;
export type TeamsApiResponse = z.infer<typeof TeamsApiResponseSchema>;
export type TeamsListResponse = z.infer<typeof TeamsListResponseSchema>;
export type Department = z.infer<typeof DepartmentSchema>;

// Validation functions
export function validateTeamsApiResponse(data: any): TeamsApiResponse {
  const result = TeamsApiResponseSchema.safeParse(data);
  if (result.success) {
    return result.data;
  } else {
    const validationError = fromZodError(result.error);
    throw validationError;
  }
}

// Legacy validation function for backward compatibility
export function validateTeamsListResponse(data: Resource[]): TeamsListResponse {
  const result = TeamsListResponseSchema.safeParse(data);
  if (result.success) {
    return result.data;
  } else {
    const validationError = fromZodError(result.error);
    throw validationError;
  }
}
