import { z } from 'zod';
import { fromZodError } from 'zod-validation-error';

export const TaskStatus = {
  active: 'active',
  inActive: 'inActive',
  completed: 'completed',
} as const;

export const TaskAction = {
  Complete: 'complete',
  Delete: 'delete',
  Activate: 'activate',
  Deactivate: 'deactivate',
} as const;

export type TaskStatusType = (typeof TaskStatus)[keyof typeof TaskStatus];
export type TaskActionType = (typeof TaskAction)[keyof typeof TaskAction];

export type UpdateTaskStatusVariables = {
  taskIds: string[];
  status: TaskStatusType;
};

const TaskSchema = z.object({
  id: z.string(),
  taskName: z.string(),
  description: z.string(),
  createdAt: z.string(),
  updatedAt: z.string(),
  taskStatus: z.string().optional(),
  deleted: z.boolean(),
  projectId: z.string(),
  estimationTime: z.number().nullable(),
  frequency: z.string(),
  contractResource: z
    .array(
      z.union([
        z.object({
          contractResource: z
            .object({
              profilePicUrl: z.string().nullable(),
              name: z.string(),
            })
            .nullable(),
          id: z.string(),
        }),
        z.null(),
      ])
    )
    .optional(),
  contract: z
    .object({
      id: z.string().optional(),
      customContractId: z.string().optional(),
      projectId: z.string().optional(),
    })
    .nullable(),
  project: z
    .object({
      contracts: z.array(
        z.object({
          id: z.string().optional(),
          customContractId: z.string().optional(),
        })
      ),
    })
    .optional(),

  _count: z.object({ contractResources: z.number() }).optional(),
  count: z.number().optional(),
});

export const TaskByProjectIdSchema = z.object({
  id: z.string().uuid(),
  taskName: z.string(),
  taskStatus: z.enum(['active', 'inActive', 'completed']),
  contractId: z.string().uuid(),
});

const ContractResourceSchema = z.object({
  id: z.string().uuid(),
  profilePicUrl: z.string().url().optional().nullable(),
  name: z.string(),
  email: z.string().email(),
  phoneNumber: z.string().optional().nullable(),
  resourceId: z.string().uuid(),
  isActive: z.boolean(),
  assignedBy: z.string().optional().nullable(),
  expectedHours: z.number().optional().nullable(),
  typeOfResource: z.string().optional().nullable(),
});

const ContractSchema = z.object({
  id: z.string().uuid(),
  customContractId: z.string(),
});

export const TasksByContractIdSchema = z.object({
  id: z.string().uuid(),
  taskName: z.string(),
  description: z.string().optional().nullable(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
  taskStatus: z.enum(['active', 'inActive', 'completed']),
  estimationTime: z.number().optional().nullable(),
  frequency: z.enum(['hours', 'days']),
  deleted: z.boolean(),
  projectId: z.string().uuid(),
  contractResource: z.array(ContractResourceSchema),
  contract: ContractSchema,
  _count: z.object({
    contractResources: z.number(),
  }),
});

export const CreateTaskSchema = z.object({
  taskName: z.string(),
  projectId: z.string().uuid(),
  customContractId: z.string().nullable(),
  contractResources: z.array(z.string().uuid()),
  estimatedTime: z.number().nullable().optional(),
  frequency: z.enum(['days', 'hours']),
  description: z.string().nullable().optional(),
});

export const UpdateTaskSchema = CreateTaskSchema.extend({
  taskId: z.string().uuid(),
});

export interface UpdateTaskStatusResponse {
  updatedRecordCount: number;
}
export interface DeleteTasksResponse {
  deletedRecordCount: number;
}

export type Task = z.infer<typeof TaskSchema>;
export type TaskByProjectId = z.infer<typeof TaskByProjectIdSchema>;
export type TasksByContractId = z.infer<typeof TasksByContractIdSchema>;
export type CreateTask = z.infer<typeof CreateTaskSchema>;
export type UpdateTask = z.infer<typeof UpdateTaskSchema>;
