import {
  HttpClient,
  HttpErrorResponse,
  HttpStatusCode,
} from '@angular/common/http';
import { Injectable, Injector, runInInjectionContext } from '@angular/core';
import {
  injectMutation,
  injectQuery,
} from '@tanstack/angular-query-experimental';
import { lastValueFrom } from 'rxjs';
import { z } from 'zod';
import { endpoints } from '../../endpoints';
import { AuthService } from '../auth/auth-service';
import { ApiResponse, UpdateServiceResponse } from '../common.model';
import { ValidationService } from '../validation/validation.service';
import {
  CreateTask,
  CreateTaskSchema,
  DeleteTasksResponse,
  Task,
  TaskByProjectId,
  TaskByProjectIdSchema,
  TasksByContractId,
  TasksByContractIdSchema,
  TaskStatus,
  TaskStatusType,
  UpdateTask,
  UpdateTaskSchema,
  UpdateTaskStatusResponse,
  UpdateTaskStatusVariables,
} from './task.model';

@Injectable({
  providedIn: 'root',
})
export class TaskService {
  resourceId: string;

  constructor(
    private authService: AuthService,
    private http: HttpClient,
    private injector: Injector,
    private validationService: ValidationService
  ) {
    this.resourceId = this.authService.userId();
  }

  /**
   * Creates a query to fetch all the tasks for a given project.
   * @param {string} projectId - The ID of the project.
   * @returns {QueryObserver} - A query object to fetch and manage tasks.
   */
  tasksByProjectQuery(projectId: string) {
    return runInInjectionContext(this.injector, () =>
      injectQuery(() => ({
        queryKey: ['tasks', projectId, this.resourceId],
        queryFn: () => this.getTasksByProjectId(projectId),
        enabled: !!projectId,
      }))
    );
  }

  /**
   * Creates a query to fetch tasks for a given project and contract.
   * @param {string} projectId - The ID of the project.
   * @param {string} contractId - The ID of the contract.
   * @returns {QueryObserver} - A query object to fetch and manage tasks.
   */
  tasksQuery(projectId: string, contractId: string) {
    return runInInjectionContext(this.injector, () =>
      injectQuery(() => ({
        queryKey: ['tasks', contractId, this.resourceId],
        queryFn: () => this.getTasksByContractId(contractId, projectId),
      }))
    );
  }

  /**
   * Creates a mutation for update status for a set of tasks.
   * @returns {MutationObserver} - A mutation object to manage task status update.
   */
  updateTaskStatusQuery() {
    return runInInjectionContext(this.injector, () =>
      injectMutation(() => ({
        mutationKey: ['update task status', this.resourceId],
        mutationFn: (variables: UpdateTaskStatusVariables) =>
          this.updateTaskStatus(variables.taskIds, variables.status),
      }))
    );
  }

  /**
   * Creates a mutation for deleting a set of tasks.
   * @returns {MutationObserver} - A mutation object to manage task deletion.
   */
  deleteTasksQuery() {
    return runInInjectionContext(this.injector, () =>
      injectMutation(() => ({
        mutationKey: ['delete task', this.resourceId],
        mutationFn: (taskIds: string[]) => this.deleteTasks(taskIds),
      }))
    );
  }

  /**
   * Fetches tasks associated with a specific project from the server.
   * Validates the fetched task data before returning it.
   *
   * @param {string} projectId - The ID of the project for which tasks need to be fetched.
   * @returns {Promise<TaskByProjectId[]>} - A promise that resolves to an array of tasks for the specified project.
   */
  async getTasksByProjectId(projectId: string): Promise<TaskByProjectId[]> {
    try {
      if (!this.resourceId) {
        throw new Error('Resource id not found.');
      }

      const response = await lastValueFrom(
        this.http.get<{ data: TaskByProjectId[] }>(
          `${endpoints.task.getTasksByProjectId.replace(':projectId', projectId)}?resourceId=${this.resourceId}&taskStatus=${TaskStatus.active}`
        )
      );

      const validatedData = this.validationService.validate(
        response.data,
        z.array(TaskByProjectIdSchema)
      );
      return validatedData;
    } catch (error: unknown) {
      console.error('Failed to fetch tasks:', (error as Error).message);
      throw new Error('Failed to fetch tasks.');
    }
  }

  /**
   * Fetches tasks associated with a specific contract and project.
   *
   * @param {string} contractId - The ID of the contract whose tasks need to be fetched.
   * @param {string} projectId - The ID of the project associated with the contract.
   * @returns {Promise<TasksByContractId[]>} - A promise that resolves to an array of validated tasks.
   */
  async getTasksByContractId(
    contractId: string,
    projectId: string
  ): Promise<TasksByContractId[]> {
    try {
      if (!this.resourceId) {
        throw new Error('Resource id not found.');
      }

      const response = await lastValueFrom(
        this.http.get<{ data: TasksByContractId[] }>(
          `${endpoints.task.getTasksByContractId}?projectId=${projectId}&contractId=${contractId}`
        )
      );

      return this.validationService.validate(
        response.data,
        z.array(TasksByContractIdSchema)
      );
    } catch (error) {
      console.error(
        'Failed to fetch contract tasks: ',
        (error as Error).message
      );
      throw new Error('Failed to fetch contract tasks');
    }
  }

  /**
   * Updates tasks status to active, inActive or completed.
   *
   * @param {string[]} taskIds - An array of task IDs to update the status.
   * @param {TaskStatusType} status - Task status to which tasks will be updated to.
   * @returns {Promise<UpdateServiceResponse>} - A promise that resolves to a success message if the operation is successful.
   * @throws {Error} - Throws an error if no task IDs are provided or if the API call fails.
   */
  async updateTaskStatus(
    taskIds: string[],
    status: TaskStatusType
  ): Promise<UpdateServiceResponse> {
    try {
      if (!taskIds.length) {
        throw new Error('No task IDs provided.');
      }

      const response = await lastValueFrom(
        this.http.patch<ApiResponse<UpdateTaskStatusResponse>>(
          endpoints.task.updateStatus,
          {
            taskIds,
            status,
          }
        )
      );

      if (
        response.statusCode === HttpStatusCode.Ok &&
        response.data?.updatedRecordCount === taskIds.length
      ) {
        return {
          status: 'success',
          message: `Successfully marked ${taskIds.length} ${taskIds.length > 1 ? 'tasks' : 'task'} as ${status}.`,
        };
      } else {
        throw new Error('Failed to update task status. Please try again.');
      }
    } catch (error: unknown) {
      console.error('Error completing tasks:', error);
      throw new Error('Failed to complete tasks. Please try again.');
    }
  }

  /**
   * Deletes specified tasks from the system.
   *
   * @param {string[]} taskIds - An array of task IDs to be deleted.
   * @returns {Promise<UpdateServiceResponse>} - A promise that resolves to a success message if the operation is successful.
   * @throws {Error} - Throws an error if no task IDs are provided or if the API call fails.
   */
  async deleteTasks(taskIds: string[]): Promise<UpdateServiceResponse> {
    try {
      if (!taskIds.length) {
        throw new Error('No task IDs provided.');
      }

      const response = await lastValueFrom(
        this.http.delete<ApiResponse<DeleteTasksResponse>>(
          endpoints.task.delete,
          {
            body: { taskIds },
          }
        )
      );

      if (
        response.statusCode === HttpStatusCode.NoContent &&
        response.data?.deletedRecordCount === taskIds.length
      ) {
        return {
          status: 'success',
          message: `Successfully deleted ${taskIds.length} ${taskIds.length > 1 ? 'tasks' : 'task'}`,
        };
      } else {
        throw new Error('Failed to delete task. Please try again.');
      }
    } catch (error: unknown) {
      console.error('Error deleting task:', error);
      throw new Error('Failed to deleting tasks. Please try again.');
    }
  }

  /**
   * createTask is an asynchronous method that handles the creation of a new task.
   *
   * @param data - The task data to be created, adhering to the CreateTask interface.
   * @returns Promise<string> - A promise that resolves to 'success' if the task is created successfully.
   * @throws Error - Throws an error if validation fails or if there are issues with the HTTP request.
   */
  async createTask(data: CreateTask) {
    try {
      const validatedData = this.validationService.validate(
        data,
        CreateTaskSchema
      );

      const response = await lastValueFrom(
        this.http.post<ApiResponse<Task>>(
          `${endpoints.task.create}`,
          validatedData
        )
      );

      if (response.statusCode === 201) {
        return 'success';
      } else {
        throw new Error(response.message);
      }
    } catch (error) {
      // Handle HTTP errors
      if (error instanceof HttpErrorResponse) {
        const errorMessage =
          error.error?.message || 'An unexpected error occurred.';
        if (error.status >= 400 && error.status < 500) {
          throw new Error(`Failed to create task:\n ${errorMessage}`);
        } else if (error.status >= 500) {
          throw new Error(
            'An unexpected server error occurred. Please try again later.'
          );
        }
      }
      console.error('Unexpected error:', (error as Error).message);
      throw new Error((error as Error).message);
    }
  }

  /**
   * updateTask is an asynchronous method that handles updating of a task.
   *
   * @param data - The task data to be updated, adhering to the UpdateTask interface.
   * @returns Promise<string> - A promise that resolves to 'success' if the task is updated successfully.
   * @throws Error - Throws an error if validation fails or if there are issues with the HTTP request.
   */
  async updateTask(data: UpdateTask): Promise<string> {
    try {
      if (!data.taskId) {
        throw new Error('Task Id is not found');
      }
      const validatedData = this.validationService.validate(
        data,
        UpdateTaskSchema
      );

      const response = await lastValueFrom(
        this.http.put<ApiResponse<Task>>(
          `${endpoints.task.update.replace(':taskId', data.taskId)}`,
          validatedData
        )
      );

      if (response.statusCode === 200) {
        return 'success';
      } else {
        console.error('Error message', response.message);
        throw new Error(response.message);
      }
    } catch (error) {
      if (error instanceof HttpErrorResponse) {
        const errorMessage =
          error.error?.message || 'An unexpected error occurred.';
        if (error.status >= 400 && error.status < 500) {
          console.error('Client error:', errorMessage);
          throw new Error(`Failed to update task: ${errorMessage}`);
        } else if (error.status >= 500) {
          console.error('Server error:', error.message);
          throw new Error(
            'An unexpected server error occurred. Please try again later.'
          );
        }
      }
      console.error('Unexpected error:', (error as Error).message);
      throw new Error((error as Error).message);
    }
  }
}
