import { z } from 'zod';
import { fromZodError } from 'zod-validation-error';
import { ProjectSchema } from '../project/project.model';

export const ContractStatus = {
  active: 'active',
  inActive: 'inActive',
  completed: 'completed',
} as const;

export type ContractActionType =
  | 'complete'
  | 'renew'
  | 'activate'
  | 'deactivate';

const CreateContractSchema = z.object({
  customContractId: z.string(),
  departmentId: z.string().optional(),
  startDate: z.string(),
  endDate: z.string(),
  projectId: z.string(),
  assignedBy: z.string(),
  resourceIds: z.array(z.string()).optional(),
  comments: z.array(z.string()).optional(),
  contractManagerIds: z.array(z.string()),
  isBillable: z.boolean(),
  billingSetting: z.string().nullable().optional(),
  amount: z.number().nullable().optional(),
  contractTypeSelection: z.string(),
  totalEstimatedEffort: z.number().nullable().optional(),
  contractStatus: z.string().optional(),
});

const UpdateContractSchema = z.object({
  customContractId: z.string().optional(),
  departmentId: z.string().optional(),
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  projectId: z.string().optional(),
  assignedBy: z.string().optional(),
  resourceIds: z.array(z.string()).optional(),
  comments: z.array(z.string()).optional(),
  contractManagerIds: z.array(z.string()).optional(),
  isBillable: z.boolean().optional(),
  billingSetting: z.string().nullable().optional(),
  amount: z.number().nullable().optional(),
  contractTypeSelection: z.string().optional(),
  totalEstimatedEffort: z.number().nullable().optional(),
  contractStatus: z.string().optional(),
});

const ContractSchema = z.object({
  id: z.string(),
  departmentId: z.string().nullable().optional(),
  startDate: z.string(),
  endDate: z.string(),
  contractType: z.string().optional(),
  contractStatus: z.string(),
  project: ProjectSchema,
  projectId: z.string(),
  contractResource: z
    .array(
      z.object({
        id: z.string(),
        resourceId: z.string(),
        contractId: z.string(),
        isActive: z.boolean(),
      })
    )
    .optional()
    .nullable(),
  deleted: z.boolean().optional().nullish(),
  clientId: z.string().nullable().optional(),
  customContractId: z.string().nullable().optional(),
  projectManagerId: z.string().nullable().optional(),
  createdAt: z.string().nullable().optional(),
  updatedAt: z.string().nullable().optional(),
});

const ContractBudgetDetailSchema = z.object({
  id: z.string(),
  contractId: z.string(),
  isBillable: z.boolean(),
  amount: z.number().nullable().optional(),
  billingSettings: z.string().nullable().optional(),
});

const ContractManagerSchema = z.object({
  id: z.string().optional(),
  name: z.string().optional(),
  email: z.string().optional(),
  kekaId: z.string().optional(),
  profilePicUrl: z.string().nullable().optional(),
});

const ContractsById = z.object({
  id: z.string(),
  startDate: z.string(),
  endDate: z.string(),
  totalEstimatedEffort: z.number().nullable().optional(),
  contractTypeSelection: z
    .enum(['fixed', 'timeAndMaterial'])
    .nullable()
    .optional(),
  contractType: z
    .union([
      z.string(),
      z.object({
        id: z.string(),
        departmentName: z.string(),
        deleted: z.boolean(),
      }),
    ])
    .optional(),
  contractStatus: z.string(),
  contractId: z.string().nullable(),
  renewedFrom: z.string().nullable(),
  total: z.number(),
  project: z.object({
    id: z.string(),
    projectName: z.string(),
    contactName: z.string().nullable(),
    contactEmail: z.string().nullable(),
  }),
  contractBudgetDetail: z
    .array(ContractBudgetDetailSchema)
    .optional()
    .nullable(),
  contractManagers: z.array(ContractManagerSchema).optional(),
  contractResources: z.array(z.string()),
  totalLoggedMinutes: z.number().optional(),
});

export const ContractHistorySchema = z.object({
  id: z.string(),
  date: z.string(),
  action: z.string(),
  assigneeResource: z.object({
    id: z.string(),
    name: z.string(),
  }),
  contract: z
    .object({
      id: z.string(),
      customContractId: z.string(),
    })
    .optional(),
  project: z
    .object({
      id: z.string(),
      projectName: z.string(),
    })
    .optional(),
  contractManagers: z
    .array(
      z.object({
        contractManagerId: z.string().optional(),
        contractManagerName: z.string().optional(),
        resourceId: z.string().optional(),
        resourceName: z.string().optional(),
      })
    )
    .optional(),
  resources: z.array(
    z
      .object({
        resourceId: z.string(),
        resourceName: z.string(),
      })
      .optional()
  ),
  updatedFields: z
    .array(
      z.object({
        to: z.string(),
        from: z.string(),
        field: z.string(),
      })
    )
    .optional(),
});
const ContractByIdSchema = z.object({
  id: z.string(),
  startDate: z.string(),
  endDate: z.string(),
  customContractId: z.string().nullable(),
  renewedFrom: z.string().nullable().optional(),
  contractType: z
    .union([
      z.string(),
      z.object({
        id: z.string(),
        departmentName: z.string(),
        deleted: z.boolean(),
      }),
    ])
    .optional(),
  contractStatus: z.string(),
  project: z.object({
    id: z.string().nullable().optional(),
    projectName: z.string(),
    contactName: z.string().nullable(),
    contactEmail: z.string().nullable(),
    contactPhoneNumber: z.string().nullable(),
    contractResource: z.array(
      z.object({
        contractResource: z.object({
          id: z.string(),
          name: z.string(),
          role: z.string(),
          phoneNumber: z.string(),
          email: z.string(),
          profilePicUrl: z.string().nullable(),
        }),
      })
    ),
  }),
  contractResource: z.array(
    z.object({
      projectId: z.string(),
      contractId: z.string().nullable(),
      isActive: z.boolean(),
      contractResource: z.object({
        id: z.string(),
        name: z.string(),
        designation: z.string().nullable(),
        profilePicUrl: z.string().nullable().optional(),
        kekaId: z.string(),
        resourceContractBillingRate: z
          .array(
            z.object({
              id: z.string(),
              rate: z.number(),
              interval: z.string(),
              currency: z.string(),
              contractId: z.string(),
              resourceId: z.string(),
            })
          )
          .optional()
          .nullish(),
      }),
    })
  ),
  contractComments: z.array(
    z.object({
      id: z.string(),
      comment: z.string(),
      updatedAt: z.string(),
      createdBy: z.object({
        name: z.string(),
      }),
    })
  ),
  total: z.number().optional(),
  contractManagers: z
    .array(
      z.object({
        id: z.string(),
        name: z.string(),
        email: z.string(),
        kekaId: z.string(),
        profilePicUrl: z.string().nullable(),
      })
    )
    .optional(),
  contractBudgetDetail: z
    .array(
      z.object({
        id: z.string().nullable().optional(),
        isBillable: z.boolean().optional(),
        billingSettings: z.string().nullish(),
        amount: z.number().nullish(),
        contractId: z.string().nullable().optional(),
      })
    )
    .nullish()
    .optional(),
});

const CommentsSchema = z.object({
  id: z.string(),
  comment: z.string(),
  createdAt: z.string(),
  updatedAt: z.string(),
  createdById: z.string(),
  deleted: z.boolean(),
  contractId: z.string(),
  contract: z
    .object({
      id: z.string(),
      customContractId: z.string(),
    })
    .optional(),
  project: z
    .object({
      id: z.string(),
      projectName: z.string(),
    })
    .optional(),
  contractManagers: z
    .array(
      z.object({
        contractManagerId: z.string().optional(),
        contractManagerName: z.string().optional(),
        resourceId: z.string().optional(),
        resourceName: z.string().optional(),
      })
    )
    .optional(),
  resources: z
    .array(
      z
        .object({
          resourceId: z.string(),
          resourceName: z.string(),
        })
        .optional()
    )
    .optional(),
  updatedFields: z
    .array(
      z.object({
        to: z.string(),
        from: z.string(),
        field: z.string(),
      })
    )
    .optional(),
});

const ResourceByContractIdSchema = z.object({
  id: z.string(),
  startDate: z.string(),
  endDate: z.string(),
  contractStatus: z.string(),
  contractResource: z.array(
    z.object({
      id: z.string(),
      contractId: z.string(),
      projectId: z.string(),
      project: z.object({
        projectName: z.string(),
      }),
      contractResource: z.object({
        id: z.string(),
        name: z.string(),
        email: z.string(),
        role: z.string(),
        profilePicUrl: z.string().nullable(),
        resourceBillingRate: z.array(
          z.object({
            id: z.string(),
            rate: z.number(),
            interval: z.string(),
            projectId: z.string(),
          })
        ),
      }),
    })
  ),
});

export interface ContractCompleteResponse {
  id: string;
  contractId: string;
  contractStatus: ContractStatus;
}

export interface ContractRenewalResponse {
  contractId: string;
  startDate: string;
  endDate: string;
  renewedFrom: string;
  contactPerson: {
    name: string;
    email: string;
  };
  resources: number;
  contractStatus: ContractStatus;
}

export interface ActivateContractPayload {
  contractId: string;
  endDate: string;
}

export interface ContractActivationResponse extends ContractCompleteResponse {}
export interface ContractDeactivationResponse
  extends ContractCompleteResponse {}

export interface ContractReportDownloadResponse {
  userEmail: string;
  downloadUrl: string;
}

export type Contract = z.infer<typeof ContractSchema>;
export type CreateContract = z.infer<typeof CreateContractSchema>;
export type UpdateContract = z.infer<typeof UpdateContractSchema>;
export type ContractStatus = 'active' | 'inActive' | 'completed';
export type ContractsById = z.infer<typeof ContractsById>;

export type ContractBudgetDetail = z.infer<typeof ContractBudgetDetailSchema>;
export type ContractHistoryResponse = z.infer<typeof ContractHistorySchema>;

export type ContractById = z.infer<typeof ContractByIdSchema>;

export type Comments = z.infer<typeof CommentsSchema>;

export type ResourceByContractId = z.infer<typeof ResourceByContractIdSchema>;
export type ContractManager = z.infer<typeof ContractManagerSchema>;

export function validateContractDetailsByProjectId(
  data: ContractsById[]
): ContractsById[] {
  const result = z.array(ContractsById).safeParse(data);
  if (result.success) {
    return data;
  } else {
    const validationError = fromZodError(result.error as z.ZodError);
    throw validationError;
  }
}

export function validateContractHistoryById(
  data: ContractHistoryResponse[]
): ContractHistoryResponse[] {
  const projectSchema = z.array(ContractHistorySchema);
  const result = projectSchema.safeParse(data);
  if (result.success) {
    return data;
  } else {
    const validationError = fromZodError(result.error as z.ZodError);
    throw validationError;
  }
}
export function validateContractId(data: ContractById): ContractById {
  const result = ContractByIdSchema.safeParse(data);
  if (result.success) {
    return data;
  } else {
    const validationError = fromZodError(result.error as z.ZodError);
    throw validationError;
  }
}

export function validateAllComments(data: Comments[]): Comments[] {
  const commentSchema = z.array(CommentsSchema);
  const result = commentSchema.safeParse(data);
  if (result.success) {
    return data;
  } else {
    const validationError = fromZodError(result.error as z.ZodError);
    throw validationError;
  }
}

export function validateCreateContractData(
  data: CreateContract
): CreateContract {
  const result = CreateContractSchema.safeParse(data);
  if (result.success) {
    return data;
  } else {
    // The temp var is intentional for debug purpose.
    const validationError = fromZodError(result.error as z.ZodError);
    throw validationError;
  }
}

export function validateAllResourceByContractId(
  data: ResourceByContractId[]
): ResourceByContractId[] {
  const contractSchema = z.array(ResourceByContractIdSchema);
  const result = contractSchema.safeParse(data);
  if (result.success) {
    return data;
  } else {
    const validationError = fromZodError(result.error as z.ZodError);
    throw validationError;
  }
}

export function validateUpdateContract(data: UpdateContract): UpdateContract {
  const result = UpdateContractSchema.safeParse(data);
  if (result.success) {
    return data;
  } else {
    const validationError = fromZodError(result.error as z.ZodError);
    throw validationError;
  }
}
