import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Injectable, Injector, runInInjectionContext } from '@angular/core';
import {
  injectMutation,
  injectQuery,
  mutationOptions,
} from '@tanstack/angular-query-experimental';
import { lastValueFrom } from 'rxjs';
import { endpoints } from '../../endpoints';
import { AuthService } from '../auth/auth-service';
import {
  ApiErrorResponse,
  ApiResponse,
  UpdateServiceResponse,
} from '../common.model';
import {
  Comments,
  Contract,
  ContractActivationResponse,
  ContractById,
  ContractCompleteResponse,
  ContractDeactivationResponse,
  ContractHistoryResponse,
  ContractRenewalResponse,
  ContractsById,
  CreateContract,
  validateAllComments,
  validateContractDetailsByProjectId,
  validateContractHistoryById,
  validateContractId,
  validateCreateContractData,
  ResourceByContractId,
  validateAllResourceByContractId,
  validateUpdateContract,
  UpdateContract,
  ActivateContractPayload,
  ContractReportDownloadResponse,
} from './contract.model';

@Injectable({
  providedIn: 'root',
})
export class ContractService {
  constructor(
    private http: HttpClient,
    private authService: AuthService,
    private injector: Injector
  ) {}

  contractsQuery(contractId: string) {
    return runInInjectionContext(this.injector, () =>
      injectQuery(() => ({
        queryKey: ['contracts', contractId, this.authService.userId()],
        queryFn: () => this.getContractById(contractId),
        enabled: !!contractId && !!this.authService.userId(),
      }))
    );
  }

  /**
   * Generates query to fetch contract for a specified project.
   * - queryKey: Uniquely identifies the query based on the given project ID.
   * - queryFn: Executes the `fetchContractsByProjectId` method with the given project ID.
   * - enabled: Query is only enabled when project ID and user ID are present.
   */
  contractsByProjectIdQuery(projectId: string) {
    return runInInjectionContext(this.injector, () =>
      injectQuery(() => ({
        queryKey: ['contracts', projectId, this.authService.userId()],
        queryFn: () => this.fetchContractsByProjectId(projectId),
      }))
    );
  }

  /**
   * Generates mutation options for renewing a contract.
   * - mutationKey: Uniquely identifies the mutation based on the given contract ID.
   * - mutationFn: Executes the `renewContract` method with the given contract ID.
   */
  renewContractQueryOptions(contractId: string) {
    return mutationOptions({
      mutationKey: ['renew contract', contractId],
      mutationFn: (contractId: string) => this.renewContract(contractId),
    });
  }

  /**
   * Generates mutation options for completing a contract.
   * - mutationKey: Uniquely identifies the mutation based on the given contract ID.
   * - mutationFn: Executes the `completeContract` method with the given contract ID.
   */
  completeContractQueryOptions(contractId: string) {
    return mutationOptions({
      mutationKey: ['complete contract', contractId],
      mutationFn: (contractId: string) => this.completeContract(contractId),
    });
  }

  /**
   * Generates mutation options for reactivating a contract.
   * - mutationKey: Uniquely identifies the mutation based on the given contract payload.
   * - mutationFn: Executes the `reactivate` method with the given contract payload.
   */
  activateContractQueryOptions(
    activatecontractPayload: ActivateContractPayload
  ) {
    return mutationOptions({
      mutationKey: ['reactivate contract', activatecontractPayload.contractId],
      mutationFn: (activatecontractPayload: ActivateContractPayload) =>
        this.reactivate(activatecontractPayload),
    });
  }

  /**
   * Generates mutation options for deactivating a contract.
   * - mutationKey: Uniquely identifies the mutation based on the given contract ID.
   * - mutationFn: Executes the `deactivate` method with the given contract ID.
   */
  deactivateContractQueryOptions(contractId: string) {
    return mutationOptions({
      mutationKey: ['deactivate contract', contractId],
      mutationFn: (contractId: string) => this.deactivate(contractId),
    });
  }

  /**
   * Generates mutation options for deleting comments in  a contract.
   * - mutationFn: Executes the `delete` method with the given comment ID.
   */
  deleteCommentsQuery() {
    return runInInjectionContext(this.injector, () =>
      injectMutation(() => ({
        mutationKey: ['deleteComment', this.authService.userId()],
        mutationFn: (commentId: string) => this.deleteComment(commentId),
      }))
    );
  }

  /**
   * Fetches history of contract based on the contract Id
   */
  contractsHistoryQuery(contractId: string) {
    return runInInjectionContext(this.injector, () =>
      injectQuery(() => ({
        queryKey: ['contractKey', contractId],
        enabled: !!contractId,
        queryFn: () => this.getContractHistoryById(contractId),
      }))
    );
  }

  /**
   * Fetches contract details by contract ID.
   */
  async getContractById(contractId: string): Promise<ContractById> {
    try {
      if (!contractId) {
        throw new Error('Contract id not found');
      }
      const response = await lastValueFrom(
        this.http.get<{ data: ContractById }>(
          `${endpoints.contract.contractById.replace(':id', contractId)}`
        )
      );
      return validateContractId(response.data);
    } catch (error) {
      throw new Error('Failed to fetch contracts.Please try again later.');
    }
  }

  /**
   * Adds comments to a specified contract.
   */
  async addCommentsToContract(
    contractId: string,
    comments: string[]
  ): Promise<Comments[]> {
    try {
      if (!contractId) {
        throw new Error('Contract id not found');
      }

      const response = await lastValueFrom(
        this.http.post<{ data: Comments[] }>(
          `${endpoints.contract.addComments.replace(':contractId', contractId)}`,
          { comments: comments }
        )
      );
      return validateAllComments(response.data);
    } catch (error: any) {
      console.error('Failed to update the comments', error.message);
      throw new Error('Failed to update the comments');
    }
  }

  async fetchContractsByProjectId(projectId: string): Promise<ContractsById[]> {
    try {
      if (!projectId) {
        throw new Error('Project id not found.');
      }
      const response = await lastValueFrom(
        this.http.get<{ data: ContractsById[] }>(
          `${endpoints.contract.getContracts}?projectId=${projectId}`
        )
      );

      return validateContractDetailsByProjectId(response.data);
    } catch (error) {
      console.error('Failed to fetch contracts', error);
      throw new Error('Failed to fetch contracts. Please try again later.');
    }
  }

  async completeContract(
    contractId: string
  ): Promise<{ status: string; message: string }> {
    try {
      const response = await lastValueFrom(
        this.http.put<ApiResponse<ContractCompleteResponse>>(
          `${endpoints.contract.complete.replace(':id', contractId)}`,
          {}
        )
      );

      // Check if the response is successful and contains the expected data
      if (
        response.statusCode === 200 &&
        response.data?.contractStatus === 'completed'
      ) {
        return {
          status: 'success',
          message: `Successfully marked contract ${response.data.contractId} as complete.`,
        };
      } else {
        throw new Error(`Failed to update contract status. Please try again.`);
      }
    } catch (error) {
      console.error('Failed to mark contract as complete: ', error);
      return { status: 'error', message: (error as Error).message };
    }
  }

  async renewContract(
    contractId: string
  ): Promise<{ status: string; message: string }> {
    try {
      const response = await lastValueFrom(
        this.http.post<ApiResponse<ContractRenewalResponse>>(
          `${endpoints.contract.renew}`,
          { renewedFrom: contractId }
        )
      );

      // Check if the response is successful and contains the expected data
      if (response.statusCode === 200) {
        return {
          status: 'success',
          message: `Successfully renewed contract ${response.data.contractId}`,
        };
      } else {
        throw new Error('Failed to renew contract. Please try again.');
      }
    } catch (error) {
      console.error('Failed to renew contract:', error);
      throw new Error('Failed to renew contract. Please try again.');
    }
  }

  /**
   * Reactivates a contract asynchronously.
   *
   * @param activatecontractPayload Payload containing contract ID and end date.
   * @returns A promise resolving with an object containing status and message.
   */
  async reactivate(
    activateContractPayload: ActivateContractPayload
  ): Promise<{ status: string; message: string }> {
    try {
      const url = `${endpoints.contract.reactivate.replace(':id', activateContractPayload.contractId)}`;
      const response = await lastValueFrom(
        this.http.put<ApiResponse<ContractActivationResponse>>(url, {
          endDate: activateContractPayload.endDate,
        })
      );

      if (response.statusCode === 200) {
        return {
          status: 'success',
          message: `Successfully activated contract ${response.data.contractId}`,
        };
      } else {
        throw new Error(`Failed to update contract status. Please try again.`);
      }
    } catch (error) {
      console.error('Failed to activate contracts:', error);
      throw (error as ApiErrorResponse).error;
    }
  }

  async deactivate(contractId: string): Promise<UpdateServiceResponse> {
    try {
      const url = `${endpoints.contract.deactivate.replace(':id', contractId)}`;
      const response = await lastValueFrom(
        this.http.put<ApiResponse<ContractDeactivationResponse>>(url, {
          endDate: new Date().toISOString(),
        })
      );
      if (response.statusCode === 200) {
        return {
          status: 'success',
          message: `Contract (${response.data.contractId}) deactivated successfully.`,
        };
      } else {
        throw new Error(`Failed to update contract status. Please try again.`);
      }
    } catch (error) {
      console.error('Failed to activate contracts:', error);
      throw (error as ApiErrorResponse).error;
    }
  }

  /**
   * Fetches the history of a contract by its ID.
   */
  async getContractHistoryById(
    contractId: string
  ): Promise<ContractHistoryResponse[]> {
    try {
      const response = await lastValueFrom(
        this.http.get<{ data: ContractHistoryResponse[] }>(
          `${endpoints.contract.getContractHistory.replace(':contractId', contractId)}`
        )
      );
      return validateContractHistoryById(response.data);
    } catch (error: any) {
      throw new Error('Failed to fetch the contract History', error.message);
    }
  }

  async addContract(
    contractData: CreateContract
  ): Promise<UpdateServiceResponse> {
    try {
      const validatedContractData = validateCreateContractData(contractData);
      const response = await lastValueFrom(
        this.http.post<ApiResponse<Contract>>(
          `${endpoints.contract.addContract}`,
          validatedContractData
        )
      );

      if (response.statusCode === 201) {
        return {
          status: 'success',
          message: `Contract (${response.data.customContractId}) added successfully.`,
        };
      } else {
        throw new Error(response.message);
      }
    } catch (error) {
      console.error('Failed to add contract:', error);
      throw (error as ApiErrorResponse).error;
    }
  }

  /**
   * getResourcesByContractId is an asynchronous method that retrieves resources
   * associated with a specific contract ID
   *
   * @param contractId - The ID of the contract for which resources are to be retrieved.
   * @returns Promise<ResourceByContractId[]> - A promise that resolves to an array of resources
   *          associated with the specified contract ID.
   */
  async getResourcesByContractId(
    contractId: string
  ): Promise<ResourceByContractId[]> {
    try {
      if (!contractId) {
        throw new Error('Contract id not found');
      }
      const response = await lastValueFrom(
        this.http.get<{ data: ResourceByContractId[] }>(
          `${endpoints.contract.getResourceByContract.replace(':contractId', contractId)}`
        )
      );
      return validateAllResourceByContractId(response.data);
    } catch (error: any) {
      console.error(`Failed to get the resources ${error.message}`);
      throw new Error(`Failed to get the resources ${error.message}`);
    }
  }

  async updateContract(
    contractData: UpdateContract,
    contractId: string
  ): Promise<UpdateServiceResponse> {
    try {
      const validatedContractData = validateUpdateContract(contractData);
      const response = await lastValueFrom(
        this.http.put<ApiResponse<Contract>>(
          `${endpoints.contract.updateContract.replace(':contractId', contractId)}`,
          validatedContractData
        )
      );

      if (response.statusCode === 200) {
        return {
          status: 'success',
          message: `Contract (${response.data.customContractId}) updated successfully.`,
        };
      } else {
        throw new Error(response.message);
      }
    } catch (error) {
      console.error('Failed to add contract:', error);
      throw (error as ApiErrorResponse).error;
    }
  }

  /**
   * @description Deletes a comment by its ID.  Handles HTTP errors and provides specific error messages based on the status code.
   * @param {string} commentId - The ID of the comment to delete.
   * @returns {Promise<void>} - A promise that resolves when the comment is successfully deleted.
   * @throws {Error} - Throws an error if the deletion fails, with a message indicating the reason.
   */
  async deleteComment(commentId: string) {
    try {
      await lastValueFrom(
        this.http.delete(
          `${endpoints.contract.deleteComment.replace(':commentId', commentId)}`
        )
      );
    } catch (error: any) {
      if (error instanceof HttpErrorResponse) {
        const errorMessage =
          error.error?.message || 'An unexpected error occurred.';
        if (error.status >= 400 && error.status < 500) {
          throw new Error(`Failed to delete Comment: ${errorMessage}`);
        } else if (error.status >= 500) {
          throw new Error(
            `An unexpected server error occurred: ${errorMessage}`
          );
        }
      }
      throw new Error((error as Error).message);
    }
  }

  /**
   * @description Edits an existing comment by its ID. Handles HTTP errors and provides specific error messages based on the status code.
   * @param {string} commentId - The ID of the comment to edit.
   * @param {string} comment - The new comment text.
   * @returns {Promise<void>} - A promise that resolves when the comment is successfully edited.
   * @throws {Error} - Throws an error if the edit fails, with a message indicating the reason.
   */
  async editComment(commentId: string, comment: string) {
    try {
      await lastValueFrom(
        this.http.put(
          `${endpoints.contract.editComment.replace(':commentId', commentId)}`,
          { comment }
        )
      );
    } catch (error: any) {
      if (error instanceof HttpErrorResponse) {
        const errorMessage =
          error.error?.message || 'An unexpected error occurred.';
        if (error.status >= 400 && error.status < 500) {
          throw new Error(`Failed to edit Comment: ${errorMessage}`);
        } else if (error.status >= 500) {
          throw new Error(
            `An unexpected server error occurred: ${errorMessage}`
          );
        }
      }
      throw new Error((error as Error).message);
    }
  }

  /**
   * Fetches the contract report download URL for a given contract, month, year, and mode.
   * @param contractId The contract UUID
   * @param month The month (number)
   * @param year The year (number)
   * @param mode The mode (e.g., 'cappedHours')
   * @param kekaId Whether to include kekaId (boolean)
   */
  async getContractReportDownloadUrl(
    contractId: string,
    month: number,
    year: number,
    mode: string,
    kekaId: boolean
  ): Promise<ApiResponse<ContractReportDownloadResponse>> {
    try {
      const url = endpoints.contract.report
        .replace(':contractId', contractId)
        .replace(':month', String(month))
        .replace(':year', String(year))
        .replace(':mode', mode) + `?kekaId=${kekaId}`;
      const response = await lastValueFrom(
        this.http.get<ApiResponse<ContractReportDownloadResponse>>(url)
      );
      return response;
    } catch (error) {
      throw new Error('Failed to fetch contract report download URL. Please try again later.');
    }
  }
}
