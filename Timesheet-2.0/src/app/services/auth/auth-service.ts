import { HttpClient } from '@angular/common/http';
import { computed, Injectable } from '@angular/core';
import { catchError, map, Observable, of } from 'rxjs';
import { environment } from '../../../environments/environment';
import { injectQuery } from '@tanstack/angular-query-experimental';

/**
 * AuthService
 *
 * A service that handles authentication-related functionality, such as checking if the user
 * is authenticated and logging out the user by managing authentication tokens stored in cookies.
 */
@Injectable({
  providedIn: 'root',
})
export class AuthService {
  constructor(private http: HttpClient) {}

  baseUrl = environment.apiUrl;
  /**
   * Checks if the user is authenticated
   */
  isAuthenticated(): Observable<
    { isAuthenticated: boolean; user: any } | boolean
  > {
    return this.http
      .get<{
        isAuthenticated: boolean;
        user: any;
      }>(`/api/v2/auth/isAuthenticated`, {
        withCredentials: true,
      })
      .pipe(
        map((response) => ({
          isAuthenticated: response.isAuthenticated,
          user: response.user,
        })),
        catchError(() => of(false)) // Return false if there's an error
      );
  }

  /**
   * Query to check if the user is authenticated.
   * It retrieves the user's authentication status and extracts the user ID.
   */
  isAuthenticatedQuery = injectQuery(() => ({
    queryKey: ['isAuthenticated'],
    queryFn: () =>
      this.isAuthenticated()
        .toPromise()
        .then((response: any) => {
          if (typeof response === 'boolean' || !response.isAuthenticated) {
            throw new Error('User is not authenticated');
          }
          return response.user.payload;
        }),
    onError: (error: any) => {
      throw new Error(`Error fetching authentication status: ${error.message}`);
    },
  }));

  userId = computed(() => this.isAuthenticatedQuery.data()?.id);

  userRole = computed(() => this.isAuthenticatedQuery.data()?.role);

  /**
   * Logs out the user by removing tokens from cookies ensuring that user has to log in again
   */
  logOut(): Observable<void> {
    return this.http
      .post<void>(
        '/api/v2/auth/logout',
        {},
        {
          withCredentials: true, // Ensures cookies are sent in the request
        }
      )
      .pipe(
        catchError((error) => {
          console.error('Logout API error:', error);
          return of(); // Return an observable with no value to prevent errors
        })
      );
  }
}
