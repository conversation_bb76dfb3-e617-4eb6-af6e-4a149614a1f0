import { TestBed } from '@angular/core/testing';
import { WorkLogService } from './worklog.service';
import { provideHttpClient, withFetch } from '@angular/common/http';
import { QueryClient } from '@tanstack/angular-query-experimental';

describe('WorkLogService', () => {
  let service: WorkLogService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [provideHttpClient(withFetch()), QueryClient],
    });
    service = TestBed.inject(WorkLogService);
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });
});
