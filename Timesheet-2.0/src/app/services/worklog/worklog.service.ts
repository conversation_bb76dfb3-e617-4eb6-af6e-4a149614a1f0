import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import {
  computed,
  Injectable,
  Injector,
  runInInjectionContext,
  Signal,
  signal,
} from '@angular/core';
import {
  injectMutation,
  injectQuery,
} from '@tanstack/angular-query-experimental';
import {
  addMinutes,
  endOfMonth,
  format,
  isFuture,
  startOfDay,
  startOfMonth,
} from 'date-fns';
import { lastValueFrom } from 'rxjs';
import { LEAVE_STATUS } from '../../constants/constant';
import { endpoints } from '../../endpoints';
import { AuthService } from '../auth/auth-service';
import {
  ApproveWorklogPayload,
  CreateWorkLog,
  CreateWorkLogResponse,
  DayOffType,
  EmployeeWorkLogReport,
  EmployeeWorkLogResponse,
  FilteredWorklog,
  FilteredWorklogSchema,
  RejectWorklogPayload,
  validateAddWorklogResponse,
  validateAllWorklog,
  validateDeleteWorklog,
  validateEmployeeWorklogReportResponse,
  Worklog,
  WorkLogDeleteResponse,
} from './worklog.model';
import { ApiErrorResponse, ApiResponse } from '../common.model';
import { ValidationService } from '../validation/validation.service';
import { z } from 'zod';
import { DATE_FORMAT_YYYY_MM_DD } from '../../settings';

@Injectable({
  providedIn: 'root',
})
export class WorkLogService {
  leaveDetails = computed(
    () => this.employeeWorkLogQuery.data()?.leaveDetails ?? []
  );

  resourcesWorklog = computed(
    () => this.employeeWorkLogQuery.data()?.resourcesWorklog ?? []
  );

  currentDate = signal<Date>(new Date());
  monthlyWorklogs = computed(() => this.worklogQuery.data() ?? []);
  worklogDateMap: Signal<Map<string, Worklog[]>> = computed(() =>
    this.createWorklogMap()
  );

  month = computed(() => this.currentDate().getMonth() + 1);
  year = computed(() => this.currentDate().getFullYear());

  constructor(
    private http: HttpClient,
    private authService: AuthService,
    private validationService: ValidationService,
    private injector: Injector
  ) { }

  worklogQuery = injectQuery(() => {
    return {
      queryKey: ['worklog', this.month(), this.year()],
      queryFn: () => {
        const startDate = new Date(Date.UTC(this.year(), this.month() - 1, 1)).toISOString();
        const endDate = new Date(Date.UTC(this.year(), this.month(), 0, 23, 59, 59, 999)).toISOString();

        return this.fetchWorklogs(startDate, endDate);
      },
    };
  });

  employeeWorkLogQuery = injectQuery(() => {
    return {
      queryKey: ['employeeWorkLog', this.month(), this.year()],
      enabled: !!this.authService.isAuthenticatedQuery.data(),
      queryFn: () => {
        return this.fetchEmployeeWorkLogReport(
          this.authService.userId(),
          this.month(),
          this.year()
        );
      },
    };
  });

  getWorkLogsByManagerIdQuery(
    resourceId: string,
    startDateISO: string,
    endDateISO: string,
    projectIds?: string[]
  ) {
    return runInInjectionContext(this.injector, () =>
      injectQuery(() => ({
        queryKey: [
          'resource-worklog',
          resourceId,
          startDateISO,
          endDateISO,
          projectIds,
        ],
        queryFn: () =>
          this.getWorkLogsByManagerId(
            resourceId,
            startDateISO,
            endDateISO,
            projectIds
          ),
        enabled: !!resourceId,
      }))
    );
  }

  async getWorkLogsByManagerId(
    resourceId: string,
    startDateISO: string,
    endDateISO: string,
    projectIds?: string[]
  ): Promise<Worklog[]> {
    try {
      // await new Promise((resolve) => setTimeout(resolve, 5000));
      const projectIdsParam =
        projectIds && projectIds.length > 0 ? `&projectIds=${projectIds}` : '';
      const response = await lastValueFrom(
        this.http.get<ApiResponse<Worklog[]>>(
          `${endpoints.worklog.getWorkLogsByManagerId.replace(':resourceId', resourceId)}?dateFrom=${startDateISO}&dateTo=${endDateISO}${projectIdsParam}`
        )
      );
      return validateAllWorklog(response.data);
      // Comment out to and import the function to simulate mock response
      // return validateAllWorklog(generateMockWorklogs(200));
    } catch (error) {
      console.error('Error while fetching worklogs by manager ID', error);
      throw (error as ApiErrorResponse).error;
    }
  }

  approveWorkLogQuery(workLogIdList: string[]) {
    return runInInjectionContext(this.injector, () =>
      injectMutation(() => ({
        mutationKey: ['approve-worklog', workLogIdList],
        mutationFn: () => this.approveWorkLogs(workLogIdList),
      }))
    );
  }

  async approveWorkLogs(workLogIdList: string[]): Promise<Worklog[]> {
    try {
      const response = await lastValueFrom(
        this.http.put<ApiResponse<Worklog[]>>(`${endpoints.worklog.approve}`, {
          workLogIdList,
        })
      );

      const validatedWorklogResponse = validateAllWorklog(response.data);
      return validatedWorklogResponse;
    } catch (error) {
      console.error('Error while approving worklogs:', error);
      throw (error as ApiErrorResponse).error;
    }
  }

  /**
   * Query for deleting an employee work log entry.
   *
   * This query uses the `injectMutation` utility to create a mutation for deleting
   * a work log entry. It specifies a unique mutation key and defines the mutation function
   * that calls `deleteWorkLogById` with given ID.
   */
  deleteWorklogQuery = injectMutation(() => {
    return {
      mutationKey: ['deleteEmployeeWorklog', this.authService.userId()],
      mutationFn: (id: string) => this.deleteWorkLogById(id),
    };
  });

  /**
   * Retrieves a work log for a specific date and resource ID.
   *
   * This function is designed to be used within an injection context,
   * allowing it to utilize dependency injection to fetch work log data.
   */
  getWorklogQuery(date: string, resourceId: string) {
    return runInInjectionContext(this.injector, () =>
      injectQuery(() => ({
        queryKey: ['getWorklogQuery', date, resourceId],
        queryFn: () => this.getWorklogByDate(date, resourceId),
        enabled: !!resourceId,
      }))
    );
  }

  async fetchEmployeeWorkLogReport(
    resourceId: string,
    month: number,
    year: number
  ): Promise<EmployeeWorkLogReport> {
    const url = `${endpoints.worklog.employeeReport}/${resourceId}/${month}/${year}`;
    const response = await lastValueFrom(
      this.http.get<EmployeeWorkLogResponse>(url)
    );
    const validatedResponse = validateEmployeeWorklogReportResponse(
      response.data
    );
    return validatedResponse;
  }

  async fetchWorklogs(
    startDateISO: string,
    endDateISO: string
  ): Promise<Worklog[]> {
    try {
      const response = await lastValueFrom(
        this.http.get<ApiResponse<Worklog[]>>(
          `${endpoints.worklog.list}?dateFrom=${startDateISO}&dateTo=${endDateISO}`
        )
      );
      const validatedWorklogResponse = validateAllWorklog(response.data);
      return validatedWorklogResponse;
    } catch (error) {
      console.error('Unexpected error:', (error as Error).message);
      throw error;
    }
  }

  createWorklogMap(): Map<string, Worklog[]> {
    const worklogMap = new Map<string, Worklog[]>();
    this.monthlyWorklogs()?.forEach((log) => {
      const dateKey = `${log.workDate}`.split('T')[0];
      if (!worklogMap.has(dateKey)) {
        worklogMap.set(dateKey, []);
      }
      worklogMap.get(dateKey)?.push(log);
    });
    return worklogMap;
  }

  isFullDay(date: Date): boolean {
    const leaveDetail = this.findEmployeeLeaveDetail(date);
    const worklog = this.findEmployeeWorklog(date);
    return (
      !worklog?.isWeekOff &&
      !worklog?.isCompanyOff &&
      leaveDetail?.leaveStatus !== LEAVE_STATUS.FULL_DAY_LEAVE &&
      leaveDetail?.leaveStatus !== LEAVE_STATUS.HALF_DAY_LEAVE &&
      !isFuture(date)
    );
  }

  isHalfDay(date: Date): boolean {
    const leaveDetail = this.findEmployeeLeaveDetail(date);
    const worklog = this.findEmployeeWorklog(date);
    return (
      !worklog?.isWeekOff &&
      !worklog?.isCompanyOff &&
      leaveDetail?.leaveStatus === LEAVE_STATUS.HALF_DAY_LEAVE &&
      !isFuture(date)
    );
  }

  /**
   * Determines the type of leave or day off for a given date.
   * @param date The date to check.
   * @returns A LeaveType representing the type of leave or day off, or 'None' if none apply.
   */
  getDayOffType(date: Date): DayOffType | null {
    const worklog = this.findEmployeeWorklog(date);
    const leaveDetail = this.findEmployeeLeaveDetail(date);

    if (leaveDetail?.leaveStatus === LEAVE_STATUS.FULL_DAY_LEAVE) {
      return 'FullDayLeave';
    }

    if (!worklog) {
      return null;
    }

    if (worklog.isWeekOff) {
      return 'WeekOff';
    } else if (worklog.isCompanyOff) {
      return 'Holiday';
    }

    return null;
  }

  totalWorkMinutes(date: Date): number {
    const worklog = this.worklogDateMap().get(
      format(date, DATE_FORMAT_YYYY_MM_DD)
    );
    if (!worklog) return 0;
    return worklog.reduce((total, log) => total + log.minutes, 0);
  }

  findEmployeeWorklog = (date: Date) => {
    const formattedDate = format(date, DATE_FORMAT_YYYY_MM_DD);
    return this.resourcesWorklog().find(
      (log) => `${log.workDate}`.split('T')[0] === formattedDate
    );
  };

  findEmployeeLeaveDetail = (date: Date) => {
    const formattedDate = format(date, DATE_FORMAT_YYYY_MM_DD);
    return this.leaveDetails().find(
      (detail) => `${detail.date}`.split('T')[0] === formattedDate
    );
  };

  getTotalWorkedDaysOfMonth() {
    return this.worklogDateMap().size;
  }

  getTotalWorkedHoursOfMonth() {
    let totalWorkMinutes = 0;
    this.worklogDateMap().forEach((worklog: Worklog[]) => {
      if (worklog) {
        totalWorkMinutes += this.totalWorkMinutes(worklog[0].workDate);
      }
    });

    const totalWorkedHours = totalWorkMinutes / 60;
    const totalWorkedHoursOfMonth =
      totalWorkedHours % 1 === 0
        ? totalWorkedHours.toFixed(0)
        : totalWorkedHours.toFixed(2);

    return totalWorkedHoursOfMonth;
  }

  async createWorklog(workLogData: CreateWorkLog) {
    try {
      const response = await lastValueFrom(
        this.http.post<{ data: CreateWorkLogResponse }>(
          `${endpoints.worklog.addWorklog}`,
          workLogData
        )
      );

      const validatedData = validateAddWorklogResponse(response.data);
      return validatedData;
    } catch (error: unknown) {
      // Handle HTTP errors
      if (error instanceof HttpErrorResponse) {
        const errorMessage = (error.error?.message as string).includes('POST')
          ? 'Failed to add worklog. Please try again.'
          : error.error?.message;
        if (error.status >= 400 && error.status < 500) {
          console.error('Client error:', errorMessage);
          throw new Error(errorMessage);
        } else if (error.status >= 500) {
          console.error('Server error:', error.message);
          throw new Error(
            'An unexpected server error occurred. Please try again later.'
          );
        }
      }
      console.error('Unexpected error:', (error as Error).message);
      throw new Error((error as Error).message);
    }
  }

  async updateWorklog(worklogData: CreateWorkLog) {
    try {
      if (!worklogData.id) {
        throw new Error('Work log ID is required for updating.');
      }

      const url = `${endpoints.worklog.updateWorklog.replace(':id', worklogData.id)}`;
      const response = await lastValueFrom(
        this.http.put<{ data: CreateWorkLogResponse }>(url, worklogData)
      );

      const validatedData = validateAddWorklogResponse(response.data);
      return validatedData;
    } catch (error: unknown) {
      // Handle HTTP errors
      if (error instanceof HttpErrorResponse) {
        const errorMessage =
          error.error?.message || 'An unexpected error occurred.';
        if (error.status >= 400 && error.status < 500) {
          console.error('Client error:', errorMessage);
          throw new Error('Failed to update worklog. Please try again.');
        } else if (error.status >= 500) {
          console.error('Server error:', error.message);
          throw new Error(
            'An unexpected server error occurred. Please try again later.'
          );
        }
      }
      console.error('Unexpected error:', (error as Error).message);
      throw new Error((error as Error).message);
    }
  }

  /**
   * Deletes a work log entry by its ID.
   *
   * This asynchronous function sends a DELETE request to the specified endpoint to remove
   * a work log entry identified by the provided ID. It handles the response and validates
   * the deletion result before returning it.
   */
  async deleteWorkLogById(id: string): Promise<WorkLogDeleteResponse> {
    try {
      const url = `${endpoints.worklog.delete}/${id}`;
      const deletedWorklog = await lastValueFrom(
        this.http.delete<WorkLogDeleteResponse>(url, { withCredentials: true })
      );
      validateDeleteWorklog(deletedWorklog.data);
      return deletedWorklog;
    } catch (error: any) {
      throw new Error(`Failed to delete the worklog`);
    }
  }

  /**
   * Gets filtered worklog based on date and resourceId
   *
   * This asynchronous function contains the implementation of the WorklogService which handles
   * fetching worklog data from the API.
   */
  async getWorklogByDate(
    date: string,
    resourceId: string
  ): Promise<FilteredWorklog[]> {
    try {
      const response = await lastValueFrom(
        this.http.get<{ data: FilteredWorklog[] }>(
          `${endpoints.worklog.filteredWorklog}?date=${date}&resourceId=${resourceId}`
        )
      );
      return this.validationService.validate(
        response.data,
        z.array(FilteredWorklogSchema)
      );
    } catch (error: any) {
      throw new Error(`Failed to get worklog ${error.message}`);
    }
  }

  /**
   * Approves the worklog based on worklog Payload
   *
   * This asynchronous function contains the implementation of WorklogService which
   * approves worklog based on the payload
   * @param worklogPayload
   */
  async approveWorklog(worklogPayload: ApproveWorklogPayload) {
    try {
      await lastValueFrom(
        this.http.put(`${endpoints.worklog.approve}`, worklogPayload)
      );
    } catch (error: any) {
      if (error instanceof HttpErrorResponse) {
        const errorMessage =
          error.error?.message || 'An unexpected error occurred.';
        if (error.status >= 400 && error.status < 500) {
          throw new Error(`Failed to approve Worklog: ${errorMessage}`);
        } else if (error.status >= 500) {
          throw new Error(
            `An unexpected server error occurred: ${errorMessage}`
          );
        }
      }
      throw new Error((error as Error).message);
    }
  }

  /**
   * Rejects the worklog based on worklog Payload
   *
   * This asynchronous function contains the implementation of WorklogService which
   * rejects worklog based on the payload
   * @param worklogPayload
   */
  async rejectWorklog(worklogPayload: RejectWorklogPayload) {
    try {
      await lastValueFrom(
        this.http.put(`${endpoints.worklog.reject}`, worklogPayload)
      );
    } catch (error: any) {
      if (error instanceof HttpErrorResponse) {
        const errorMessage =
          error.error?.message || 'An unexpected error occurred.';
        if (error.status >= 400 && error.status < 500) {
          throw new Error(`Failed to reject Worklog: ${errorMessage}`);
        } else if (error.status >= 500) {
          throw new Error(
            `An unexpected server error occurred: ${errorMessage}`
          );
        }
      }
      throw new Error((error as Error).message);
    }
  }

  lastEnteredWorklog() {
    const [recentWorklog] = this.monthlyWorklogs() || [];
    if (!recentWorklog) {
      return null;
    }
    const { projectId, project } = recentWorklog;
    return {
      value: projectId,
      label: project?.projectName,
      startDate: project?.startDate,
      endDate: project?.endDate,
    };
  }
}
