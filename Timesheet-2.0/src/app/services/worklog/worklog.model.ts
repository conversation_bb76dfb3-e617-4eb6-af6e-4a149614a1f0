import { z } from 'zod';
import { fromZodError } from 'zod-validation-error';

export type DayOffType =
  | 'FullDayLeave'
  | 'HalfDayLeave'
  | 'WeekOff'
  | 'Holiday';

export interface LeaveDetail {
  type: DayOffType;
  message?: string;
}

export interface EmployeeWorklog {
  workDate: string;
  minutes: number;
  attendance: string;
  isWeekOff: boolean;
  isCompanyOff: boolean;
  taskName: string;
  projectName: string;
  workLogStatus: string;
}

export interface Leaves {
  date: string;
  leaveStatus: number;
}

export interface EmployeeWorkLogResponse {
  data: EmployeeWorkLogReport;
  message: string;
  statusCode: number;
}

export const LEAVE_STATUS = {
  FULL_DAY: 2,
  HALF_DAY: 1,
};

const EmployeeReportSchema = z.object({
  resourceId: z.string(),
  resourceName: z.string(),
  totalMinutes: z.number(),
  totalDays: z.number(),
  resourcesWorklog: z.array(
    z.object({
      workDate: z.string(),
      minutes: z.number(),
      attendance: z.string(),
      isWeekOff: z.boolean(),
      isCompanyOff: z.boolean(),
      taskName: z.string(),
      projectName: z.string(),
      workLogStatus: z.string(),
    })
  ),
  leaveDetails: z.array(
    z.object({
      date: z.string(),
      leaveStatus: z.number(),
    })
  ),
});

const DailyWorkLogSchema = z.object({
  id: z.string().optional().nullable(),
  projectId: z.string().uuid(),
  taskId: z.string().uuid(),
  date: z.string().date(),
  loggedHours: z
    .string()
    .regex(/^\d{2}:\d{2}$/, 'Logged hours must be in HH:mm format'),
  description: z.string(),
  location:z.string()
});

export const CreateWorkLogResponseSchema = z.object({
  id: z.string().uuid(),
  workDate: z.string().datetime(),
  employeeId: z.string().uuid(),
  managerId: z.string().uuid(),
  projectId: z.string().uuid(),
  clientId: z.string().uuid(),
  taskId: z.string().uuid(),
  minutes: z.number().nonnegative(),
  description: z.string().min(1),
  isOnLeave: z.boolean(),
  isOnFirstHalfLeave: z.boolean(),
  isOnSecondHalfLeave: z.boolean(),
  isWeekOff: z.boolean(),
  isCompanyOff: z.boolean(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
  deleted: z.boolean(),
});

export type EmployeeWorkLogReport = z.infer<typeof EmployeeReportSchema>;
export type CreateWorkLog = z.infer<typeof DailyWorkLogSchema>;
export type CreateWorkLogResponse = z.infer<typeof CreateWorkLogResponseSchema>;

export function validateAddWorklogResponse(
  data: CreateWorkLogResponse
): CreateWorkLogResponse {
  const result = CreateWorkLogResponseSchema.safeParse(data);
  if (result.success) {
    return data;
  } else {
    const validationError = fromZodError(result.error as z.ZodError);
    throw validationError;
  }
}

export function validateAllWorkLog(data: Worklog[]): Worklog[] {
  const result = z.array(DailyWorkLogSchema).safeParse(data);
  if (result.success) {
    return data;
  } else {
    const validationError = fromZodError(result.error as z.ZodError);
    throw validationError;
  }
}

export function validateEmployeeWorklogReportResponse(
  data: EmployeeWorkLogReport
): EmployeeWorkLogReport {
  const result = EmployeeReportSchema.safeParse(data);
  if (result.success) {
    return data;
  } else {
    const validationError = fromZodError(result.error as z.ZodError);
    throw validationError;
  }
}

export const WorklogSchema = z.object({
  id: z.string(),
  workDate: z
    .string()
    .datetime()
    .transform((val) => new Date(Date.parse(val))),
  startDate: z
    .string()
    .datetime()
    .transform((val) => new Date(Date.parse(val))),
  endDate: z
    .string()
    .datetime()
    .transform((val) => new Date(Date.parse(val))),
  clientId: z.string(),
  projectId: z.string(),
  employeeId: z.string(),
  managerId: z.union([z.string(), z.null()]).nullable(),
  taskId: z.string(),
  description: z.string().nullable(),
  minutes: z.number(),
  createdAt: z
    .string()
    .datetime()
    .transform((val) => new Date(Date.parse(val))),
  updatedAt: z
    .string()
    .datetime()
    .transform((val) => new Date(Date.parse(val))),
  deleted: z.boolean(),
  isOnLeave: z.boolean(),
  isOnFirstHalfLeave: z.boolean(),
  isOnSecondHalfLeave: z.boolean(),
  isWeekOff: z.boolean(),
  isCompanyOff: z.boolean(),
  employeeResource: z
    .object({
      name: z.string(),
    })
    .optional(),
  project: z
    .object({
      projectName: z.string(),
      billable: z.boolean(),
      projectStatus: z.string().nullish(),
      startDate: z
        .string()
        .datetime()
        .transform((val) => new Date(Date.parse(val)))
        .optional(),
      endDate: z
        .string()
        .datetime()
        .transform((val) => new Date(Date.parse(val)))
        .optional(),
    })
    .optional(),
  client: z
    .object({
      name: z.string(),
      status: z.boolean().nullish(),
    })
    .optional(),
  workLogStatus: z
    .object({
      status: z.enum(['submitted', 'approved', 'revised', 'rejected']),
      remarks: z.string().optional(),
    })
    .optional()
    .nullable(),
  task: z
    .object({
      taskName: z.string(),
      contract: z
        .object({
          customContractId: z.string(),
        })
        .optional(),
    })
    .optional(),
    location:z.string().optional().nullable()
});

const DeleteWorklogSchema = z.object({
  id: z.string(),
  startDate: z.string(),
  endDate: z.string(),
  minutes: z.number(),
});

const CreateWorklogSchema = z.object({
  id: z.string(),
  workDate: z.string(),
  startDate: z.string(),
  minutes: z.number(),
  employeeId: z.string(),
});

export const FilteredWorklogSchema = WorklogSchema.merge(
  z.object({
    description: z.string(),
    project: z
      .object({
        projectName: z.string(),
        billable: z.boolean().optional(),
        projectStatus: z.string().nullish(),
      })
      .optional(),
    workLogStatus: z
      .object({
        status: z.enum(['submitted', 'approved', 'revised', 'rejected']),
        remarks: z.string().optional(),
      })
      .optional()
      .nullable(),
    employeeResource: z
      .object({
        id: z.string(),
        name: z.string(),
      })
      .optional(),
  })
);

export type Worklog = z.infer<typeof WorklogSchema>;
export type DeleteWorklog = z.infer<typeof DeleteWorklogSchema>;
export type CreateWorklog = z.infer<typeof CreateWorklogSchema>;
export type FilteredWorklog = z.infer<typeof FilteredWorklogSchema>;

export interface ErrorResponse {
  error: string;
  message: string;
  statusCode: number;
}

export interface ApproveWorklogPayload {
  workLogIdList: string[];
}

export interface RejectWorklogPayload extends ApproveWorklogPayload {
  remark: string;
}

export function validateWorklog(data: Worklog): Worklog {
  const result = WorklogSchema.safeParse(data);
  if (result.success) {
    return data;
  } else {
    const validationError = fromZodError(result.error as z.ZodError);
    throw validationError;
  }
}

export function validateAllWorklog(data: Worklog[]): Worklog[] {
  const result = z.array(WorklogSchema).safeParse(data);
  if (result.success) {
    return data;
  } else {
    const validationError = fromZodError(result.error as z.ZodError);
    throw validationError;
  }
}

export function validateDeleteWorklog(data: DeleteWorklog): DeleteWorklog {
  const result = DeleteWorklogSchema.safeParse(data);
  if (result.success) {
    return data;
  } else {
    const validationError = fromZodError(result.error as z.ZodError);
    throw validationError;
  }
}

export function validateAllWorklogResponse(data: Worklog[]): Worklog[] {
  const result = z.array(WorklogSchema).safeParse(data);
  if (result.success) {
    return data;
  } else {
    const validationError = fromZodError(result.error as z.ZodError);
    throw validationError;
  }
}

export interface WorkLogDeleteResponse {
  statusCode: HttpStatus;
  data: {
    id: string;
    startDate: string;
    endDate: string;
    minutes: number;
  };
  message: string;
}

export declare enum HttpStatus {
  CONTINUE = 100,
  SWITCHING_PROTOCOLS = 101,
  PROCESSING = 102,
  EARLYHINTS = 103,
  OK = 200,
  CREATED = 201,
  ACCEPTED = 202,
  NON_AUTHORITATIVE_INFORMATION = 203,
  NO_CONTENT = 204,
  RESET_CONTENT = 205,
  PARTIAL_CONTENT = 206,
  AMBIGUOUS = 300,
  MOVED_PERMANENTLY = 301,
  FOUND = 302,
  SEE_OTHER = 303,
  NOT_MODIFIED = 304,
  TEMPORARY_REDIRECT = 307,
  PERMANENT_REDIRECT = 308,
  BAD_REQUEST = 400,
  UNAUTHORIZED = 401,
  PAYMENT_REQUIRED = 402,
  FORBIDDEN = 403,
  NOT_FOUND = 404,
  METHOD_NOT_ALLOWED = 405,
  NOT_ACCEPTABLE = 406,
  PROXY_AUTHENTICATION_REQUIRED = 407,
  REQUEST_TIMEOUT = 408,
  CONFLICT = 409,
  GONE = 410,
  LENGTH_REQUIRED = 411,
  PRECONDITION_FAILED = 412,
  PAYLOAD_TOO_LARGE = 413,
  URI_TOO_LONG = 414,
  UNSUPPORTED_MEDIA_TYPE = 415,
  REQUESTED_RANGE_NOT_SATISFIABLE = 416,
  EXPECTATION_FAILED = 417,
  I_AM_A_TEAPOT = 418,
  MISDIRECTED = 421,
  UNPROCESSABLE_ENTITY = 422,
  FAILED_DEPENDENCY = 424,
  PRECONDITION_REQUIRED = 428,
  TOO_MANY_REQUESTS = 429,
  INTERNAL_SERVER_ERROR = 500,
  NOT_IMPLEMENTED = 501,
  BAD_GATEWAY = 502,
  SERVICE_UNAVAILABLE = 503,
  GATEWAY_TIMEOUT = 504,
  HTTP_VERSION_NOT_SUPPORTED = 505,
}
