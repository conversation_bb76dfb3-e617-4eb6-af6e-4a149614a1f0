import { z } from 'zod';
import { fromZodError } from 'zod-validation-error';

export type ProjectStatus = 'active' | 'inActive' | 'completed';

export const ProjectStatus = {
  active: 'active',
  inActive: 'inActive',
  completed: 'completed',
} as const;

const Manager = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string(),
  phoneNumber: z.string().optional().nullable(),
  profilePicUrl: z.string().optional().nullable(),
  typeOfResource: z.enum(['projectManager', 'contractManager']),
});

export const ProjectDetailsByIdSchema = z.object({
  id: z.string(),
  projectName: z.string(),
  contactName: z.string().nullable(),
  contactEmail: z.string().nullable(),
  contactPhoneNumber: z.string().nullable(),
  description: z.string().nullable(),
  startDate: z.string(),
  endDate: z.string().nullish(),
  createdBy: z.union([z.string(), z.null()]),
  clientId: z.string().optional(),
  billable: z.boolean(),
  projectStatus: z.string(),
  createdAt: z.string().optional().nullable(),
  updatedAt: z.string().optional(),
  deleted: z.boolean().optional(),
  clientName: z.string().optional().nullable(),
  managerId: z.string().optional(),
  client: z
    .object({
      id: z.string(),
      name: z.string(),
      currency: z.string().nullable().optional(),
    })
    .optional(),
  resourcesList: z.array(
    z
      .object({
        name: z.string(),
        designation: z.string().nullable(),
        profilePicUrl: z.string().nullable(),
      })
      .optional()
  ),
  projectBudgetDetails: z.array(
    z.object({
      id: z.string(),
      billingSettings: z.string(),
      amount: z.number(),
    })
  ),
  resourcesCount: z.number().optional(),
  contractResource: z.array(
    z
      .object({
        id: z.string(),
        name: z.string(),
        email: z.string(),
        phoneNumber: z.string(),
        role: z.string(),
      })
      .nullable()
  ),
  technologies: z.array(z.string()).optional(),
});

const ProjectByResourceId = z.object({
  id: z.string(),
  projectName: z.string(),
  contactName: z.string().nullable(),
  contactEmail: z.string().nullable(),
  contactPhoneNumber: z.string().nullable(),
  description: z.string().nullable(),
  startDate: z.string(),
  endDate: z.string().nullable(),
  clientName: z.string(),
  totalMinutes: z.number().nullable().optional(),
  projectStatus: z.string(),
  contracts: z.number().optional(),
  billable: z.boolean().optional(),
  contractManagers: z.array(Manager).optional(),
  projectManager: z.array(Manager).optional(),
  technologies: z.array(z.string()).optional(),
});

const CreateProjectSchema = z.object({
  projectName: z.string(),
  contactName: z.string().nullable(),
  contactEmail: z.string().nullable(),
  contactPhoneNumber: z.string().nullable(),
  description: z.string().nullable(),
  startDate: z.string(),
  endDate: z.string().nullable(),
  clientId: z.string(),
  projectManagerId: z.string(),
  createdBy: z.string(),
  technologies: z.array(z.string()).optional(),
});

export const ProjectSchema = z.object({
  id: z.string(),
  projectName: z.string(),
  contactName: z.string().nullable(),
  contactEmail: z.string().nullable(),
  contactPhoneNumber: z.string().nullable(),
  description: z.string().nullable(),
  startDate: z.date(),
  endDate: z.date().nullable(),
  billable: z.boolean(),
  createdBy: z.string().nullable(),
  clientId: z.string().nullable(),
  projectStatus: z.enum(['active', 'inActive', 'completed']),
  createdAt: z.date(),
  updatedAt: z.date(),
  technologies: z.array(z.string()).optional(),
  deleted: z.boolean().optional(),
});

export const ProjectListResponseSchema = z.object({
  id: z.string(),
  projectName: z.string(),
  startDate: z.string(),
  endDate: z.string().nullable(),
  projectStatus: z.enum(['active', 'inActive', 'completed']),
});

export const ProjectHistorySchema = z.object({
  date: z.string(),
  action: z.string(),
  projectId: z.string(),
  projectName: z.string(),
  personId: z.string(),
  personName: z.string(),
  projectManagerId: z.string().optional(),
  projectManagerName: z.string().optional(),
  resourceList: z
    .array(z.object({ resourceId: z.string(), resourceName: z.string() }))
    .optional(),
  addedResourceList: z
    .array(z.object({ resourceId: z.string(), resourceName: z.string() }))
    .optional(),
  removedResourceList: z
    .array(z.object({ resourceId: z.string(), resourceName: z.string() }))
    .optional(),
  unassignedResourceId: z.string().optional(),
  unassignedResourceName: z.string().optional(),
});

export const UpdateProjectSchema = CreateProjectSchema.extend({
  projectId: z.string().uuid(),
});

export interface TimelineData {
  id: string;
  date: string;
  title: string;
  author: string | { id: string; name: string };
  subTitle: string;
}

export type ProjectListByResourceId = z.infer<typeof ProjectListResponseSchema>;
export type Project = z.infer<typeof ProjectSchema>;
export type ProjectByResourceId = z.infer<typeof ProjectByResourceId>;
export type ProjectDetailsById = z.infer<typeof ProjectDetailsByIdSchema>;
export type CreateProject = z.infer<typeof CreateProjectSchema>;
export type ProjectHistoryResponse = z.infer<typeof ProjectHistorySchema>;
export type Manager = z.infer<typeof Manager>;
export type UpdateProject = z.infer<typeof UpdateProjectSchema>;
export interface EditProjectByResourceId extends ProjectByResourceId {
  client:
    | {
        id: string;
        name: string;
      }
    | undefined;
  projectManagerDetails:
    | {
        id: string;
        name: string;
        profilePicUrl: string | undefined | null;
      }
    | undefined;
}

export function validateProjectListByResourceId(
  data: ProjectListByResourceId[]
): ProjectListByResourceId[] {
  const result = z.array(ProjectListResponseSchema).safeParse(data);
  if (result.success) {
    return data;
  } else {
    const validationError = fromZodError(result.error as z.ZodError);
    throw validationError;
  }
}

export function validateProjectDetailsByResourceId(
  data: ProjectByResourceId[]
): ProjectByResourceId[] {
  const result = z.array(ProjectByResourceId).safeParse(data);
  if (result.success) {
    return data;
  } else {
    const validationError = fromZodError(result.error as z.ZodError);
    throw validationError;
  }
}

export function validateProjectDetailsByIdResponse(
  data: ProjectDetailsById
): ProjectDetailsById {
  const result = ProjectDetailsByIdSchema.safeParse(data);
  if (result.success) {
    return data;
  } else {
    const validationError = fromZodError(result.error as z.ZodError);
    throw validationError;
  }
}

export function validateCreateProjectData(data: CreateProject): CreateProject {
  const result = CreateProjectSchema.safeParse(data);
  if (result.success) {
    return data;
  } else {
    const validationError = fromZodError(result.error as z.ZodError);
    throw validationError;
  }
}

export function validateProjectContractHistoryById(
  data: ProjectHistoryResponse[]
): ProjectHistoryResponse[] {
  const projectSchema = z.array(ProjectHistorySchema);
  const result = projectSchema.safeParse(data);
  if (result.success) {
    return data;
  } else {
    // The temp var is intentional for debug purpose.
    const validationError = fromZodError(result.error as z.ZodError);
    throw validationError;
  }
}
