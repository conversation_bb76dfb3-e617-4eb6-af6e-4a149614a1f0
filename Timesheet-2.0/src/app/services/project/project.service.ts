import {
  HttpClient,
  HttpErrorResponse,
  HttpStatusCode,
} from '@angular/common/http';
import { Injectable, Injector, runInInjectionContext } from '@angular/core';
import { injectQuery } from '@tanstack/angular-query-experimental';
import { lastValueFrom } from 'rxjs';
import { endpoints } from '../../endpoints';
import { AuthService } from '../auth/auth-service';
import { ApiErrorResponse, ApiResponse } from '../common.model';
import { ValidationService } from '../validation/validation.service';
import {
  CreateProject,
  Project,
  ProjectByResourceId,
  ProjectDetailsById,
  ProjectHistoryResponse,
  ProjectListByResourceId,
  UpdateProject,
  UpdateProjectSchema,
  validateCreateProjectData,
  validateProjectContractHistoryById,
  validateProjectDetailsByIdResponse,
  validateProjectDetailsByResourceId,
  validateProjectListByResourceId,
} from './project.model';

// Interface for utilization stats response
export interface UtilizationStatsResponse {
  orgLevel: {
    billable: number;
    partiallyBillable: number;
    nonBillable: number;
    total: number;
  };
  departmentWise: Array<{
    department: string;
    billable: number;
    partiallyBillable: number;
    nonBillable: number;
    total: number;
  }>;
  leaveStats: {
    fullDay: number;
    halfDay: number;
    total: number;
  };
  locationStats?: {
    totals: {
      codecraftOffice: number;
      clientOffice: number;
      remote: number;
    };
    percentages: {
      codecraftOffice: string;
      clientOffice: string;
      remote: string;
    };
  };
}

@Injectable({
  providedIn: 'root',
})
export class ProjectsService {
  constructor(
    private http: HttpClient,
    private authService: AuthService,
    private validationService: ValidationService,
    private injector: Injector
  ) {}

  // Query to list active projects for a resource
  projectListQuery() {
    return runInInjectionContext(this.injector, () =>
      injectQuery(() => ({
        queryKey: ['projectsList', this.authService.userId()],
        queryFn: () => this.getProjectsList(),
        enabled: !!this.authService.userId(),
      }))
    );
  }

  employeeProjectsQuery() {
    return runInInjectionContext(this.injector, () =>
      injectQuery(() => ({
        queryKey: ['projects', this.authService.userId()],
        queryFn: () => this.getProjectsByResourceId(),
        enabled: !!this.authService.userId(),
      }))
    );
  }

  projectQuery(projectId: string) {
    return runInInjectionContext(this.injector, () =>
      injectQuery(() => ({
        queryKey: ['project', projectId, this.authService.userId()],
        queryFn: () => this.getProjectById(projectId),
        enabled: !!projectId,
      }))
    );
  }

  projectsHistoryQuery(projectId: string) {
    return runInInjectionContext(this.injector, () =>
      injectQuery(() => ({
        queryKey: ['projectHistoryByProject', projectId],
        queryFn: () => this.projectsContractHistory(projectId),
      }))
    );
  }

  utilizationStatsQuery(timePeriod: string, startDate?: Date, endDate?: Date) {
    return runInInjectionContext(this.injector, () =>
      injectQuery(() => ({
        queryKey: ['utilizationStats', timePeriod, startDate, endDate],
        queryFn: () => this.getUtilizationStats(timePeriod, startDate, endDate),
      }))
    );
  }

  getUserRole() {
    return this.authService.userRole();
  }

  async getProjectsByResourceId(): Promise<ProjectByResourceId[]> {
    try {
      const resourceId = this.authService.userId();

      if (!resourceId) {
        throw new Error('Resource id not found.');
      }

      const response = await lastValueFrom(
        this.http.get<ApiResponse<ProjectByResourceId[]>>(
          `${endpoints.project.getAllProjects}`
        )
      );
      return validateProjectDetailsByResourceId(response.data);
    } catch (error) {
      console.error('Failed to fetch projects:', error);
      throw new Error('Failed to fetch projects. Please try again later.');
    }
  }

  async getProjectsList(): Promise<ProjectListByResourceId[]> {
    try {
      const resourceId = this.authService.userId();
      if (!resourceId) {
        throw new Error('Resource id not found.');
      }

      const role = this.authService.userRole();
      const params = new URLSearchParams();

      // Only add resourceId for non-admin users
      if (role !== 'admin') {
        params.append('resourceId', resourceId);
      }

      const url = `${endpoints.project.projectsList}${params.toString() ? `?${params.toString()}` : ''}`;

      const response = await lastValueFrom(
        this.http.get<ApiResponse<ProjectListByResourceId[]>>(url)
      );

      return validateProjectListByResourceId(response.data);
    } catch (error) {
      console.error('Failed to fetch projects:', error);
      throw new Error('Failed to fetch projects. Please try again later.');
    }
  }

  async createProject(data: CreateProject): Promise<string> {
    try {
      const validatedData = validateCreateProjectData(data);

      const response = await lastValueFrom(
        this.http.post<ApiResponse<Project>>(
          `${endpoints.project.createProject}`,
          validatedData
        )
      );

      if (response.statusCode === 201) {
        return 'success';
      } else {
        throw new Error(response.message);
      }
    } catch (error) {
      console.error('Failed to create project: ', (error as Error).message);
      if ((error as HttpErrorResponse).status === HttpStatusCode.NotFound) {
        throw new Error('Failed to create project');
      } else {
        throw (error as ApiErrorResponse).error;
      }
    }
  }

  async getProjectById(projectId: string): Promise<ProjectDetailsById> {
    try {
      if (!projectId) {
        throw new Error('Project id not found.');
      }
      const response = await lastValueFrom(
        this.http.get<ApiResponse<ProjectDetailsById>>(
          `${endpoints.project.projectById.replace(':id', projectId)}`
        )
      );
      return validateProjectDetailsByIdResponse(response.data);
    } catch (error) {
      console.error('Failed to fetch project details:', error);
      throw error;
    }
  }

  /**
   * Fetches the project history for a specified project.
   *
   * This method makes an HTTP GET request to retrieve the project history
   * associated with a given project ID. It validates the response and returns
   * an array of project history records.
   */
  async projectsContractHistory(
    projectId: string
  ): Promise<ProjectHistoryResponse[]> {
    try {
      if (!projectId) {
        throw new Error('Project id not found.');
      }
      const response = await lastValueFrom(
        this.http.get<ApiResponse<ProjectHistoryResponse[]>>(
          `${endpoints.project.projectsContractHistory.replace(':projectId', projectId)}`
        )
      );
      return validateProjectContractHistoryById(response.data);
    } catch (error) {
      throw new Error(
        'Failed to fetch contracts history. Please try again later.'
      );
    }
  }

  /**
   * `updateProject` updates an existing project with the provided data.
   *
   * It validates the `projectData` using `this.validationService` against the `UpdateProjectSchema`.
   * If the validation is successful, it sends a PUT request to the server to update the project.
   * It handles both successful updates and errors, including HTTP errors.
   */
  async updateProject(projectData: UpdateProject): Promise<string> {
    try {
      if (!projectData.projectId) {
        throw new Error('Project id not found');
      }
      const validatedData = this.validationService.validate(
        projectData,
        UpdateProjectSchema
      );

      const response = await lastValueFrom(
        this.http.put<ApiResponse<Project>>(
          `${endpoints.project.updateProject.replace(':projectId', projectData.projectId)}`,
          validatedData
        )
      );

      if (response.statusCode === 200) {
        return 'success';
      } else {
        console.error('Error message', response.message);
        throw new Error(response.message);
      }
    } catch (error: any) {
      if (error instanceof HttpErrorResponse) {
        const errorMessage =
          error.error?.message || 'An unexpected error occurred.';
        if (error.status >= 400 && error.status < 500) {
          throw new Error(`Failed to update project: ${errorMessage}`);
        } else if (error.status >= 500) {
          throw new Error(
            'An unexpected server error occurred. Please try again later.'
          );
        }
      }
      throw new Error((error as Error).message);
    }
  }

  /**
   * Fetches utilization statistics for the specified time period.
   *
   * @param timePeriod - The time period for the statistics (past3Days, past1Week, past1Month, past6Months, customRange)
   * @param startDate - Optional start date for custom range
   * @param endDate - Optional end date for custom range
   * @returns Promise<UtilizationStatsResponse>
   */
  async getUtilizationStats(
    timePeriod: string,
    startDate?: Date,
    endDate?: Date
  ): Promise<UtilizationStatsResponse> {
    try {
      const params = new URLSearchParams();
      params.append('timePeriod', timePeriod);

      // Add custom date range parameters if timePeriod is customRange
      if (timePeriod === 'customRange' && startDate && endDate) {
        params.append('startDate', startDate.toISOString().split('T')[0]); // Format as YYYY-MM-DD
        params.append('endDate', endDate.toISOString().split('T')[0]); // Format as YYYY-MM-DD
      }

      const url = `${endpoints.resource.utilizationStats}?${params.toString()}`;

      const response = await lastValueFrom(
        this.http.get<ApiResponse<UtilizationStatsResponse>>(url)
      );

      if (response.statusCode === 200) {
        return response.data;
      } else {
        throw new Error(
          response.message || 'Failed to fetch utilization statistics'
        );
      }
    } catch (error) {
      console.error('Failed to fetch utilization statistics:', error);
      if (error instanceof HttpErrorResponse) {
        const errorMessage =
          error.error?.message || 'An unexpected error occurred.';
        if (error.status >= 400 && error.status < 500) {
          throw new Error(
            `Failed to fetch utilization statistics: ${errorMessage}`
          );
        } else if (error.status >= 500) {
          throw new Error(
            'An unexpected server error occurred. Please try again later.'
          );
        }
      }
      throw new Error(
        'Failed to fetch utilization statistics. Please try again later.'
      );
    }
  }
}
