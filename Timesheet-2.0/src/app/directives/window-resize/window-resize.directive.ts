import {
  DestroyRef,
  Directive,
  ElementRef,
  inject,
  output,
} from '@angular/core';
import { fromEvent, debounceTime } from 'rxjs';

@Directive({
  selector: '[tmsWindowResize]',
  standalone: true,
})
export class WindowResizeDirective {
  onResize = output<void>();

  destroyRef = inject(DestroyRef);

  constructor(private el: ElementRef) {
    const resizeSubscription = fromEvent(window, 'resize')
      .pipe(debounceTime(300))
      .subscribe(() => {
        this.onResize.emit();
      });

    this.destroyRef.onDestroy(() => resizeSubscription.unsubscribe());
  }
}
