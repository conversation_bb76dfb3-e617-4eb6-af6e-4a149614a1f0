import {
  ComponentFixture,
  TestBed,
  fakeAsync,
  tick,
} from '@angular/core/testing';
import { WindowResizeDirective } from './window-resize.directive';
import { Component, signal } from '@angular/core';

@Component({
  standalone: true,
  imports: [WindowResizeDirective],
  template: `<div tmsWindowResize (onResize)="onResize()"></div>`,
})
class TestComponent {
  resizeCalled = signal(false);

  onResize() {
    this.resizeCalled.set(true);
  }
}

describe('WindowResizeDirective', () => {
  let fixture: ComponentFixture<TestComponent>;
  let component: TestComponent;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [TestComponent, WindowResizeDirective],
    }).compileComponents();

    fixture = TestBed.createComponent(TestComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create an instance', () => {
    expect(component).toBeTruthy();
  });

  it('should emit onResize event on window resize', fakeAsync(() => {
    // Simulate window resize
    window.dispatchEvent(new Event('resize'));

    // Advance time by the debounce period
    tick(300);
    fixture.detectChanges(); // Ensure Angular processes changes

    // Check if the signal was triggered
    expect(component.resizeCalled()).toBeTrue();
  }));
});
