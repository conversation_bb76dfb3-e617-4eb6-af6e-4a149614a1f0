import { Routes } from '@angular/router';
import { AuthGuard } from './guard/auth.guard';
import { LoginGuard } from './guard/login.guard';
import { RoleGuard } from './guard/role.guard';

/**
 * Application routes configuration
 *
 * This object defines the routing for the application, enabling lazy loading of components
 * and route guarding to ensure secure navigation.
 */
export const routes: Routes = [
  {
    path: 'dashboard',
    loadComponent: () =>
      import('./layout/dashboard-layout/dashboard-layout.component').then(
        (m) => m.DashboardLayoutComponent
      ),
    canActivate: [AuthGuard],
    data: { breadcrumb: 'Dashboard' },
    children: [
      {
        path: 'tracker',
        loadComponent: () =>
          import('./pages/trackerpage/trackerpage.component').then(
            (m) => m.TrackerPageComponent
          ),
        canActivate: [AuthGuard],
      },
      {
        path: 'projects',
        data: { breadcrumb: 'Projects' },
        children: [
          {
            path: '',
            loadComponent: () =>
              import('./pages/my-projects/my-projects.component').then(
                (m) => m.MyProjectsComponent
              ),
            canActivate: [AuthGuard],
          },

          {
            path: ':projectId',
            data: { breadcrumb: 'Project Details' },
            children: [
              {
                path: '',
                loadComponent: () =>
                  import(
                    './pages/project-details/project-details.component'
                  ).then((m) => m.ProjectDetailsComponent),
                canActivate: [AuthGuard, RoleGuard],
                data: { roles: ['admin', 'manager'] },
              },
              {
                path: 'contracts/:contractId',
                loadComponent: () =>
                  import(
                    './pages/contract-details/contract-details.component'
                  ).then((m) => m.ContractDetailsComponent),
                canActivate: [AuthGuard, RoleGuard],
                data: {
                  breadcrumb: 'Contract Details',
                  roles: ['admin', 'manager'],
                },
              },
            ],
          },
        ],
      },
      {
        path: 'project-overview',
        data: { breadcrumb: 'Project Overview' },
        children: [
          {
            path: '',
            loadComponent: () =>
              import(
                './pages/project-overview/project-overview.component'
              ).then((m) => m.ProjectOverviewComponent),
            canActivate: [AuthGuard],
          },
        ],
      },
      {
        path: 'reports',
        loadComponent: () =>
          import('./pages/report-page/report-page.component').then(
            (m) => m.ReportPageComponent
          ),
        canActivate: [AuthGuard, RoleGuard],
        data: { roles: ['admin', 'manager', 'finance'] },
      },
      {
        path: 'logsheets',
        loadComponent: () =>
          import('./pages/logsheets/logsheets.component').then(
            (m) => m.LogsheetsComponent
          ),
        canActivate: [AuthGuard, RoleGuard],
        data: { roles: ['admin', 'manager'] },
      },
      {
        path: 'leaves',
        loadComponent: () =>
          import('./pages/leaves/leaves.component').then(
            (m) => m.LeavesComponent
          ),
        canActivate: [AuthGuard, RoleGuard],
        data: { roles: ['admin', 'manager'] },
      },
      {
        path: 'clients',
        loadComponent: () =>
          import('./pages/clients/clients.component').then(
            (m) => m.ClientsComponent
          ),
        canActivate: [AuthGuard, RoleGuard],
        data: { roles: ['admin'] },
      },
      {
        path: 'teams',
        loadComponent: () =>
          import('./pages/teams/teams.component').then((m) => m.TeamsComponent),
        canActivate: [AuthGuard, RoleGuard],
        data: { roles: ['admin'] },
      },
      {
        path: 'profile',
        loadComponent: () =>
          import('./pages/profilepage/profilepage.component').then(
            (m) => m.ProfilepageComponent
          ),
        canActivate: [AuthGuard],
      },
    ],
  },
  {
    path: 'login',
    loadComponent: () =>
      import('./pages/login-page/login-page.component').then(
        (m) => m.LoginPageComponent
      ),
    canActivate: [LoginGuard],
  },
  {
    path: '**',
    redirectTo: '/login',
  },
];
