###################
# Build Stage
###################

# Use Node.js base image
FROM node:20.18.0-alpine as build

# Declare the build environment argument
ARG BUILD_ENV=production  

# Install pnpm globally
RUN npm install -g pnpm@9.12.3

WORKDIR /app

# Copy package files and install dependencies
COPY package.json pnpm-lock.yaml ./
RUN pnpm install --frozen-lockfile

# Copy the rest of the application source code
COPY . .

# Build the Angular app with the specified configuration
RUN pnpm run build --configuration $BUILD_ENV


###################
# PRODUCTION
###################
FROM nginx:alpine

COPY nginx.conf /etc/nginx/conf.d/default.conf

COPY --from=build /app/dist/timesheet-2.0/browser/ /usr/share/nginx/html/


EXPOSE 80