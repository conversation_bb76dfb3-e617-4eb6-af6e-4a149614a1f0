import { provideHttpClient, withFetch } from '@angular/common/http';
import { provideAnimations } from '@angular/platform-browser/animations';
import { provideRouter, Routes, withHashLocation } from '@angular/router';
import {
  applicationConfig,
  moduleMetadata,
  type Preview,
} from '@storybook/angular';
import {
  QueryClient,
  provideTanStackQuery,
} from '@tanstack/angular-query-experimental';
import { DialogService } from 'primeng/dynamicdialog';

const routes: Routes = [];

const preview: Preview = {
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
  },
  decorators: [
    applicationConfig({
      providers: [
        provideRouter(routes, withHashLocation()),
        provideHttpClient(withFetch()),
        provideAnimations(),
        provideTanStackQuery(new QueryClient()),
        DialogService,
      ],
    }),
    moduleMetadata({}),
  ],
};

export default preview;
