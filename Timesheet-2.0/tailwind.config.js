/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./src/**/*.{html,ts}', './node_modules/primeng/**/*.js'],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#f1fafe',
          100: '#e2f4fc',
          200: '#bee9f9',
          300: '#85d7f4',
          400: '#44c3ec',
          500: '#1caedd',
          600: '#0e8cbb',
          700: '#0d6f97',
          800: '#0f5e7d',
          900: '#124e68',
        },
        alert: {
          50: 'hsl(0,80%,94%)',
          100: 'hsl(0,80%,90%)',
          200: 'hsl(0,80%,86%)',
          300: 'hsl(0,80%,77%)',
          400: 'hsl(0,80%,68%)',
          500: 'hsl(0,80%,59%)',
          600: 'hsl(0,80%,50%)',
          700: 'hsl(0,80%,41%)',
          800: 'hsl(0,80%,32%)',
          900: 'hsl(0,80%,23%)',
        },
        neutral: {
          50: '#f9fafb',
          100: '#f3f4f6',
          200: '#e5e7eb',
          300: '#d1d5db',
          400: '#9ca3af',
          500: '#6b7280',
          600: '#4b5563',
          700: '#374151',
          800: '#1f2937',
          900: '#111827',
        },
      },
      strokeWidth: {
        1.5: '1.5',
        2.5: '2.5',
        3.5: '3.5',
      },
      fontFamily: {
        poppins: ['Poppins', 'sans-serif'],
      },
    },
  },
  plugins: [],
};
