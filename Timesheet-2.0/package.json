{"name": "timesheet-2.0", "version": "2.1.1", "scripts": {"ng": "ng", "dev": "ng serve --configuration=local", "start": "ng serve --host 0.0.0.0 --port 4200", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:Timesheet-2.0": "node dist/timesheet-2.0/server/server.mjs", "lint": "ng lint", "format": "prettier --write \"src/**/*.{ts,html,scss}\"", "prepare": "husky", "storybook": "ng run Timesheet-2.0:storybook", "build-storybook": "ng run Timesheet-2.0:build-storybook"}, "private": true, "dependencies": {"@angular/animations": "^18.2.0", "@angular/common": "^18.2.0", "@angular/compiler": "^18.2.0", "@angular/core": "^18.2.0", "@angular/forms": "^18.2.0", "@angular/platform-browser": "^18.2.0", "@angular/platform-browser-dynamic": "^18.2.0", "@angular/platform-server": "^18.2.0", "@angular/router": "^18.2.0", "@angular/ssr": "^18.2.7", "@tanstack/angular-query-experimental": "^5.61.1", "date-fns": "^4.1.0", "express": "^4.18.2", "js-cookie": "^3.0.5", "ngx-cookie-service": "^19.0.0", "primeicons": "^7.0.0", "primeng": "^17.18.11", "rxjs": "~7.8.0", "tslib": "^2.8.1", "zod": "^3.23.8", "zod-validation-error": "^3.4.0", "zone.js": "~0.14.10"}, "devDependencies": {"@angular-devkit/build-angular": "^18.2.7", "@angular/cli": "^18.2.7", "@angular/compiler-cli": "^18.2.0", "@chromatic-com/storybook": "3.2.2", "@compodoc/compodoc": "1.1.26", "@storybook/addon-actions": "^8.4.4", "@storybook/addon-docs": "8.4.2", "@storybook/addon-essentials": "8.4.2", "@storybook/addon-interactions": "8.4.2", "@storybook/addon-onboarding": "8.4.2", "@storybook/angular": "8.4.2", "@storybook/blocks": "8.4.2", "@storybook/test": "8.4.2", "@storybook/types": "^8.4.2", "@types/express": "^4.17.17", "@types/jasmine": "~5.1.0", "@types/jest": "^29.5.14", "@types/js-cookie": "^3.0.6", "@types/node": "^18.18.0", "@typescript-eslint/eslint-plugin": "^8.12.2", "@typescript-eslint/parser": "^8.12.2", "angular-eslint": "18.4.0", "autoprefixer": "^10.4.20", "eslint": "^9.14.0", "eslint-plugin-storybook": "^0.11.0", "eslint-plugin-tailwindcss": "^3.17.5", "husky": "^9.1.6", "jasmine-core": "~5.2.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "lint-staged": "^15.2.10", "postcss": "^8.4.47", "prettier": "^3.3.3", "storybook": "8.4.2", "tailwindcss": "^3.4.14", "typescript": "~5.5.2", "typescript-eslint": "8.10.0"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}}