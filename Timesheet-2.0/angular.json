{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"Timesheet-2.0": {"projectType": "application", "schematics": {}, "root": "", "sourceRoot": "src", "prefix": "tms", "architect": {"build": {"builder": "@angular-devkit/build-angular:application", "options": {"outputPath": "dist/timesheet-2.0", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": ["src/assets", {"glob": "**/*", "input": "public"}], "styles": ["src/assets/theme/theme.scss", "node_modules/primeng/resources/primeng.min.css", "node_modules/primeicons/primeicons.css", "src/styles.css"], "scripts": []}, "configurations": {"production": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.production.ts"}], "budgets": [{"type": "initial", "maximumWarning": "1MB", "maximumError": "1MB"}, {"type": "anyComponentStyle", "maximumWarning": "2kB", "maximumError": "4kB"}], "outputHashing": "all"}, "development": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.development.ts"}], "optimization": false, "extractLicenses": false, "sourceMap": true}, "local": {"fileReplacements": [], "optimization": false, "sourceMap": true, "extractLicenses": false}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular-devkit/build-angular:dev-server", "configurations": {"production": {"buildTarget": "Timesheet-2.0:build:production"}, "development": {"buildTarget": "Timesheet-2.0:build:development"}, "local": {"buildTarget": "Timesheet-2.0:build:local"}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n"}, "test": {"builder": "@angular-devkit/build-angular:karma", "options": {"polyfills": ["zone.js", "zone.js/testing"], "tsConfig": "tsconfig.spec.json", "assets": [{"glob": "**/*", "input": "public"}], "styles": ["src/styles.css", "node_modules/primeng/resources/themes/lara-light-blue/theme.css", "node_modules/primeng/resources/primeng.min.css", "node_modules/primeicons/primeicons.css"], "scripts": []}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}, "storybook": {"builder": "@storybook/angular:start-storybook", "options": {"configDir": ".storybook", "browserTarget": "Timesheet-2.0:build", "compodoc": true, "compodocArgs": ["-e", "json", "-d", "."], "port": 6006}}, "build-storybook": {"builder": "@storybook/angular:build-storybook", "options": {"configDir": ".storybook", "browserTarget": "Timesheet-2.0:build", "compodoc": true, "compodocArgs": ["-e", "json", "-d", "."], "outputDir": "storybook-static"}}}}}, "cli": {"analytics": false, "schematicCollections": ["@angular-eslint/schematics"]}}