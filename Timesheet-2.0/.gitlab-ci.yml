stages:
  - build
  - test
  - deploy

# Build Jobs
.docker-build-and-push:
  image: bentolor/docker-dind-awscli:latest
  stage: build
  services:
    - name: docker:dind
  before_script:
    - aws ecr get-login-password --region ${AWS_REGION} | docker login --username AWS --password-stdin ${AWS_ACCOUNT}.dkr.ecr.${AWS_REGION}.amazonaws.com

web-docker-build-dev:
  extends: .docker-build-and-push
  script:
    - aws_registry_image="${AWS_ACCOUNT}.dkr.ecr.${AWS_REGION}.amazonaws.com/timesheet-angular"
    - tag=":$CI_COMMIT_REF_NAME"
    - cd Timesheet-2.0
    - docker build --pull --platform linux/amd64 --build-arg BUILD_ENV=development -t "${aws_registry_image}${tag}" .
    - docker push "${aws_registry_image}${tag}"
  rules:
    - if: '$CI_COMMIT_REF_NAME == "master" && $CI_COMMIT_REF_PROTECTED == "true"'
      changes:
        - Timesheet-2.0/**/*

web-docker-build-prod:
  extends: .docker-build-and-push
  script:
    - aws_registry_image="${AWS_ACCOUNT}.dkr.ecr.${AWS_REGION}.amazonaws.com/timesheet-angular"
    - tag=":$CI_COMMIT_REF_NAME"
    - cd Timesheet-2.0
    - docker build --pull --platform linux/amd64 --build-arg BUILD_ENV=production -t "${aws_registry_image}${tag}" .
    - docker push "${aws_registry_image}${tag}"
  rules:
    - if: '$CI_COMMIT_TAG =~ /^timesheet-web-v[0-9]+\.[0-9]+\.[0-9]+$/ && $CI_COMMIT_REF_PROTECTED == "true"'

# Test Jobs
# unit test-1
# web-pnpm-test:
#   image: node:20.18.0
#   stage: test
#   before_script:
#     - npm i -g pnpm@9.12.3
#   script:
#     - echo "unit test"
#     - cd Timesheet-2.0
#     - pnpm install
#     - pnpm run test
#   rules:
#     - if: $CI_MERGE_REQUEST_IID
#       changes:
#         - Timesheet-2.0/**/*

# 2 pnpm build test
web-pnpm-build:
  image: node:20.18.0
  stage: build
  before_script:
    - npm i -g pnpm@9.12.3
  script:
    - echo "Building web app.."
    - cd Timesheet-2.0
    - pnpm install
    - pnpm run build
  rules:
    - if: $CI_MERGE_REQUEST_IID
      changes:
        - Timesheet-2.0/**/*

# 3 docker build test
web-docker-build:
  image: bentolor/docker-dind-awscli:latest
  stage: build
  services:
    - name: docker:dind
  script:
    - cd Timesheet-2.0
    - docker build .
  rules:
    - if: $CI_MERGE_REQUEST_IID
      changes:
        - Timesheet-2.0/**/*

# Deployment jobs
deploy-develop:
  image: linuxserver/openssh-server:version-9.3_p2-r0
  stage: deploy
  before_script:
    - chmod 600 $AWS_DEV_PRIVATE_KEY
  script:
    - ssh -o StrictHostKeyChecking=no -i $AWS_DEV_PRIVATE_KEY -p 2222 ec2-user@$AWS_DEV_INSTANCE_IP "cd /home/<USER>/timesheet-docker/web-angular && ./update.sh update"
  rules:
    - if: '$CI_COMMIT_REF_NAME == "master" && $CI_COMMIT_REF_PROTECTED == "true"'
      changes:
        - Timesheet-2.0/**/*
