# Timesheet-2.0

Timesheet-2.0 is a time-tracking tool designed to help employees log their working hours efficiently. It enables managers to manage projects, contracts, and tasks, approve or reject worklogs, and generate reports.

## Prerequisites

Before setting up the project, ensure you have the following installed:

- **Node.js** (latest LTS version recommended)
- **pnpm** (preferred package manager for dependency management)
- **Docker** (for running PostgreSQL as the local database)
- **Timesheet Backend** (from Codecraft Internal Apps):

  - [Timesheet-backend (Prisma-client)](https://gitlab.codecrafttech.com/codecraft/codecraft-internal-apps/-/tree/master/packages/@prisma-clients/timesheet-backend?ref_type=heads) Navigate to `codecraft-internal-apps\packages\@prisma-clients\timesheet-backend`

    - Copy .env.example into .env

    ```sh
    docker compose up -d
    ```

    The docker container will be running on port localhost:5432. After that run below given commands to create the database

    ```sh
    pnpm db:generate
    ```

    ```sh
    pnpm db:migrate
    ```

- [Timesheet-backend](https://gitlab.codecrafttech.com/codecraft/codecraft-internal-apps/-/tree/master/apps/timesheet-backend?ref_type=heads) Navigate to `codecraft-internal-apps\apps\timesheet-backend` and

  - Copy .env.example into .env

    ```sh
    pnpm start:dev
    ```

    Timesheet backend service will be running on localhost:3000.

- Ensure that the Timesheet backend app and docker container is running.

## Setup Instructions

### 1. Clone the Repository

```sh
<NAME_EMAIL>:codecraft/timesheet-2.0.git
cd timesheet-2.0/Timesheet-2.0
```

### 2. Install Dependencies

```sh
pnpm install
```

### 3. Start the Development Server

```sh
pnpm dev
```

Navigate to `http://localhost:4200/`. The application will automatically reload when you modify source files.

## Code Scaffolding

You can generate new Angular components and other elements using:

```sh
ng generate component component-name
```

Or for other items:

```sh
ng generate directive|pipe|service|class|guard|interface|enum|module
```

## Building the Project

To create a production-ready build, run:

```sh
pnpm build
```

The build artifacts will be stored in the `dist/` directory.

## Running Tests

### Unit Tests

Execute unit tests via [Karma](https://karma-runner.github.io):

```sh
pnpm test
```

### End-to-End Tests

Run end-to-end tests using a preferred testing platform (ensure you install an E2E testing package first):

```sh
ng e2e
```

## Running Storybook

To launch Storybook for UI component testing:

```sh
pnpm storybook
```
